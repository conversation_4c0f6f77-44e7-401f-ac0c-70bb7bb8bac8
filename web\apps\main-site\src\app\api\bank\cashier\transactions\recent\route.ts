import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../../../lib/prisma";
import jwt from "jsonwebtoken";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
const JWT_SECRET = process.env.JWT_SECRET || "your-development-secret-key";

// Helper function to verify token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

export const GET = async (req: NextRequest) => {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Check if user is a banker
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || !user.isBanker) {
      return NextResponse.json(
        { error: "Unauthorized - Banker role required" },
        { status: 403 },
      );
    }

    // Get parameters from query
    const url = new URL(req.url);
    const limitParam = url.searchParams.get("limit");
    const statusParam = url.searchParams.get("status");
    const typeParam = url.searchParams.get("type");

    const limit = limitParam ? parseInt(limitParam, 10) : 5;

    // Build where conditions
    const whereConditions: any = {};

    if (statusParam) {
      whereConditions.status = statusParam;
    }

    if (typeParam) {
      whereConditions.type = typeParam;
    }

    // Query the database for transactions without filtering by user
    const transactions = await prisma.transaction.findMany({
      where: whereConditions,
      include: {
        sender: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        recipient: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        processedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
    });

    // Format dates as ISO strings for JSON serialization
    const formattedTransactions = transactions.map((transaction) => ({
      ...transaction,
      createdAt: transaction.createdAt.toISOString(),
      updatedAt: transaction.updatedAt.toISOString(),
      processedAt: transaction.processedAt
        ? transaction.processedAt.toISOString()
        : null,
    }));

    return NextResponse.json(formattedTransactions);
  } catch (error) {
    console.error("Error fetching cashier transactions:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
