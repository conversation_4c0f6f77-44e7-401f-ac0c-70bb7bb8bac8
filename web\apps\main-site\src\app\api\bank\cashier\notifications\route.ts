import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../../lib/prisma";
import { verifyToken } from "../../../../../lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET endpoint to fetch notifications
export const GET = async (req: NextRequest) => {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Check if user is a banker
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isBanker: true },
    });

    if (!user || !user.isBanker) {
      return NextResponse.json(
        { error: "Unauthorized. Banker role required." },
        { status: 403 },
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const limitParam = url.searchParams.get("limit");
    const unreadOnlyParam = url.searchParams.get("unreadOnly");
    const limit = limitParam ? parseInt(limitParam, 10) : 20;
    const unreadOnly = unreadOnlyParam === "true";

    // Create the where condition
    const whereCondition: any = {};
    if (unreadOnly) {
      whereCondition.read = false;
    }

    // Query the database for notifications
    const notifications = await prisma.notification.findMany({
      where: whereCondition,
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
      include: {
        transaction: {
          include: {
            sender: {
              select: {
                id: true,
                username: true,
                displayName: true,
                avatar: true,
              },
            },
          },
        },
      },
    });

    // Format dates as ISO strings for JSON serialization
    const formattedNotifications = notifications.map((notification) => ({
      ...notification,
      createdAt: notification.createdAt.toISOString(),
      updatedAt: notification.updatedAt.toISOString(),
      transaction: notification.transaction
        ? {
            ...notification.transaction,
            createdAt: notification.transaction.createdAt.toISOString(),
            updatedAt: notification.transaction.updatedAt.toISOString(),
            processedAt: notification.transaction.processedAt
              ? notification.transaction.processedAt.toISOString()
              : null,
          }
        : null,
    }));

    return NextResponse.json(formattedNotifications);
  } catch (error) {
    console.error("Error fetching notifications:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};

// PATCH endpoint to mark notifications as read
export const PATCH = async (req: NextRequest) => {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Check if user is a banker
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isBanker: true },
    });

    if (!user || !user.isBanker) {
      return NextResponse.json(
        { error: "Unauthorized. Banker role required." },
        { status: 403 },
      );
    }

    // Parse request body
    const body = await req.json();
    const { ids, all } = body;

    if (all) {
      // Mark all notifications as read
      await prisma.notification.updateMany({
        where: {
          read: false,
        },
        data: {
          read: true,
        },
      });

      return NextResponse.json({
        message: "All notifications marked as read",
      });
    } else if (ids && Array.isArray(ids) && ids.length > 0) {
      // Mark specific notifications as read
      await prisma.notification.updateMany({
        where: {
          id: {
            in: ids,
          },
        },
        data: {
          read: true,
        },
      });

      return NextResponse.json({
        message: `${ids.length} notification(s) marked as read`,
      });
    } else {
      return NextResponse.json(
        { error: "Invalid request. Provide ids or all=true" },
        { status: 400 },
      );
    }
  } catch (error) {
    console.error("Error updating notifications:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
