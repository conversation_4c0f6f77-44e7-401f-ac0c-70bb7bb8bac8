# Volunteer Management Components

Components for the volunteer management system including shift management, categories, and payment tracking.

## Main Components

- **VolunteerDashboardLayout.tsx** - Main layout for volunteer dashboard pages
- **DashboardMain.tsx** - Main dashboard component with overview
- **DashboardStats.tsx** - Statistics and metrics display
- **VolunteerQuickActions.tsx** - Quick action buttons for common volunteer operations
- **Tabs.tsx** - Tab navigation component for volunteer sections

## Category Management

- **CategoryCard.tsx** - Display card for volunteer categories
- **CategoryForm.tsx** - Form for creating/editing volunteer categories
- **CategoryList.tsx** - List view of volunteer categories
- **CategorySelector.tsx** - Dropdown selector for categories

## Shift Management

- **ShiftCard.tsx** - Individual shift display with details and actions
- **ShiftForm.tsx** - Form for creating and editing volunteer shifts
- **ShiftList.tsx** - List view of available and assigned shifts

## Payment System

- **PaymentHistory.tsx** - Payment history display for volunteers
- **PaymentList.tsx** - List of payments and transactions
- **PaymentFilters.tsx** - Filtering options for payment data
- **PaymentConfirmationModal.tsx** - Confirmation modal for payment processing

## Utilities

- **EventSelector.tsx** - Event selection component
- **UserSearchInput.tsx** - User search functionality
- **ConfirmationModal.tsx** - Generic confirmation modal
- **lead/** - Volunteer lead management components
- **public/** - Public-facing volunteer components
- **index.ts** - Component exports

This comprehensive volunteer system handles shift scheduling, category management, hour tracking, and payment processing.
