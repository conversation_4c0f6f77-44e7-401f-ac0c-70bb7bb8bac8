import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/volunteer/public/events/[id]/categories - Get categories for a specific event
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id: eventId } = params;

    // Check if user is authenticated
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    // Check if event exists and is published
    const event = await prisma.event.findUnique({
      where: { id: eventId },
      select: { id: true, status: true },
    });

    if (!event) {
      return NextResponse.json({ error: "Event not found" }, { status: 404 });
    }

    if (event.status !== "published") {
      return NextResponse.json(
        { error: "Event not available" },
        { status: 403 },
      );
    }

    // Get categories for the event
    const categories = await prisma.volunteerCategory.findMany({
      where: { eventId },
      select: {
        id: true,
        name: true,
        description: true,
        payRate: true,
        eventId: true,
        leadManagerId: true,
        createdAt: true,
        updatedAt: true,
        // Include shifts to calculate stats
        shifts: {
          select: {
            id: true,
            maxVolunteers: true,
            assignments: {
              select: {
                id: true,
                status: true,
              },
            },
          },
        },
      },
    });

    // Calculate stats for each category
    const categoriesWithStats = categories.map((category) => {
      const totalShifts = category.shifts.length;

      // Count total signups across all shifts
      const totalSignups = category.shifts.reduce(
        (sum, shift) => sum + shift.assignments.length,
        0,
      );

      // Calculate available slots
      const totalSlots = category.shifts.reduce(
        (sum, shift) => sum + shift.maxVolunteers,
        0,
      );

      const availableSlots = totalSlots - totalSignups;

      // Remove shifts from the response
      const { shifts, ...categoryData } = category;

      return {
        ...categoryData,
        stats: {
          totalShifts,
          totalSignups,
          availableSlots,
        },
      };
    });

    return NextResponse.json({ categories: categoriesWithStats });
  } catch (error) {
    console.error("Error fetching categories:", error);
    return NextResponse.json(
      { error: "Failed to fetch categories" },
      { status: 500 },
    );
  }
}
