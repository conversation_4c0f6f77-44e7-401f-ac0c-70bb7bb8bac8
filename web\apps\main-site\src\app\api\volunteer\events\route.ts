import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/volunteer/events - Get events for volunteer management
export async function GET(req: NextRequest) {
  try {
    // Check if user is authenticated and has volunteer coordinator role
    const user = await getCurrentUser(req);


    // For debugging, temporarily skip authentication check
    // if (!user) {
    //   return NextResponse.json(
    //     { error: "Unauthorized - Authentication required" },
    //     { status: 401 }
    //   );
    // }

    // const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");
    // console.log(`API /volunteer/events - Has coordinator role: ${hasCoordinatorRole}`);

    // if (!hasCoordinatorRole) {
    //   return NextResponse.json(
    //     { error: "Unauthorized - Coordinator role required" },
    //     { status: 403 }
    //   );
    // }

    // Get current date
    const now = new Date();

    // First, get a count of all events regardless of filters
    const totalEventCount = await prisma.event.count();

    // Get all events (without any filters for debugging)
    const allEvents = await prisma.event.findMany({
      orderBy: {
        startDate: "asc",
      },
      select: {
        id: true,
        name: true,
        startDate: true,
        endDate: true,
        location: true,
        isVirtual: true,
        status: true,
        category: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });


    // Get events with only date filter
    const events = await prisma.event.findMany({
      where: {
        // Remove all filters for testing
        // status: "published",
        // endDate: {
        //   gte: now,
        // },
      },
      orderBy: {
        startDate: "asc",
      },
      select: {
        id: true,
        name: true,
        startDate: true,
        endDate: true,
        location: true,
        isVirtual: true,
        status: true,
        category: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });


    return NextResponse.json({
      events,
    });
  } catch (error) {
    console.error("Error fetching events:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch events",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
