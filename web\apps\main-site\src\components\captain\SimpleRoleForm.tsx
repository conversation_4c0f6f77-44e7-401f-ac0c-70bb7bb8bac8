"use client";

import { useState } from 'react';
import { Button, Input } from '@bank-of-styx/ui';

interface SimpleRoleFormProps {
  onCreateRole: (name: string, description?: string) => Promise<void>;
  onCancel: () => void;
}

export default function SimpleRoleForm({ onCreateRole, onCancel }: SimpleRoleFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    if (!formData.name.trim()) {
      setError('Role name is required');
      return;
    }

    setIsLoading(true);
    try {
      await onCreateRole(formData.name.trim(), formData.description.trim() || undefined);
      setFormData({ name: '', description: '' });
    } catch (error: any) {
      console.error('Error creating role:', error);
      setError(error.message || 'Failed to create role');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <div className="p-3 bg-red-900/50 border border-red-700 rounded text-red-200 text-sm">
          {error}
        </div>
      )}
      
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Role Name *
        </label>
        <Input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          placeholder="e.g., First Mate, Navigator, Cook"
          maxLength={50}
          disabled={isLoading}
          className="w-full"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Description
        </label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          className="w-full bg-input border border-secondary-dark rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary resize-none"
          placeholder="Describe the role's responsibilities..."
          rows={3}
          maxLength={200}
          disabled={isLoading}
        />
      </div>

      <div className="flex justify-end space-x-3 pt-2">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="primary"
          disabled={isLoading || !formData.name.trim()}
        >
          {isLoading ? 'Creating...' : 'Create Role'}
        </Button>
      </div>
    </form>
  );
}