# Shared Components

Reusable UI components that contain business logic and are used across multiple features.

## Components

- **Badge.tsx** - Badge component for status indicators, labels, and tags
- **ConfirmationModal.tsx** - Reusable confirmation modal for destructive actions
- **Select.tsx** - Enhanced select dropdown component with custom styling and functionality
- **index.ts** - Component exports

These shared components provide common UI patterns and interactions that are used throughout the Bank of Styx platform while maintaining consistent styling and behavior.
