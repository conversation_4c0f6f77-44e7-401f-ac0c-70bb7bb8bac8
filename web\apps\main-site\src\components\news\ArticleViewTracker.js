"use client";

import { useEffect, useRef } from "react";
import { useUserState } from "@/contexts/UserStateContext";
import { useAuth } from "@/contexts/AuthContext";

/**
 * Component that tracks article views for authenticated users
 * Increments the articleReads counter in the user state
 * @param {Object} props - Component props
 * @param {string} props.articleId - ID of the article being viewed
 */
export default function ArticleViewTracker({ articleId }) {
  const { incrementArticleReads, userState, setCustomState } = useUserState();
  const { isAuthenticated } = useAuth();
  const hasTrackedRef = useRef(false);

  useEffect(() => {
    // Only track article views for authenticated users and only once per article
    if (isAuthenticated && articleId && !hasTrackedRef.current && userState) {
      // Get the list of read articles from custom state
      const readArticles = userState.customState?.readArticles || [];

      // Check if this article has already been read
      if (!readArticles.includes(articleId)) {
        // Mark as tracked to prevent multiple tracking
        hasTrackedRef.current = true;

        // Add the article to the read articles list
        const updatedReadArticles = [...readArticles, articleId];

        // Use a small timeout to avoid race conditions
        setTimeout(() => {
          // Update the custom state
          setCustomState("readArticles", updatedReadArticles)
            .then(() => {
              // Increment the article reads counter
              return incrementArticleReads();
            })
            .then(() => {
              console.log("Article view tracked:", articleId);
            })
            .catch(error => {
              console.error("Error tracking article view:", error);
            });
        }, 500);
      } else {
        // Mark as tracked even if already read
        hasTrackedRef.current = true;
      }
    }
  }, [articleId, isAuthenticated, incrementArticleReads, setCustomState, userState]);

  // This component doesn't render anything
  return null;
}
