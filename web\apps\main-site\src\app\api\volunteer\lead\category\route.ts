import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/volunteer/lead/category - Get the lead manager's assigned category
export async function GET(req: NextRequest) {
  try {
    // Check if user is authenticated and has lead manager role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasLeadRole = await userHasRole(req, "leadManager");
    if (!hasLeadRole) {
      return NextResponse.json(
        { error: "Unauthorized - Lead Manager role required" },
        { status: 403 },
      );
    }

    // Get the lead manager's category ID
    const leadManagerCategoryId = user.leadManagerCategoryId;
    if (!leadManagerCategoryId) {
      return NextResponse.json(
        { error: "No category assigned to this lead manager" },
        { status: 404 },
      );
    }

    // Get the lead manager's category with event details
    const category = await prisma.volunteerCategory.findUnique({
      where: { id: leadManagerCategoryId },
      include: {
        event: {
          select: {
            id: true,
            name: true,
            startDate: true,
            endDate: true,
            status: true,
            location: true,
            description: true,
          },
        },
      },
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 },
      );
    }

    return NextResponse.json({ category });
  } catch (error) {
    console.error("Error fetching lead category:", error);
    return NextResponse.json(
      { error: "Failed to fetch category data" },
      { status: 500 },
    );
  }
}
