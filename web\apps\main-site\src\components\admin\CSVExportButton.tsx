import React from "react";
import { Button } from "@bank-of-styx/ui";
import { exportToCSV } from "@/utils/csv-export";
import toast from "react-hot-toast";

interface CSVExportButtonProps<T extends Record<string, any>> {
  data: T[];
  filename: string;
  headers?: { key: keyof T; label: string }[];
  buttonText?: string;
  variant?: "primary" | "secondary" | "accent" | "outline" | "ghost";
  disabled?: boolean;
  onExport?: () => void;
}

/**
 * Reusable CSV Export Button component
 *
 * This component provides a button that triggers a CSV export when clicked.
 * It can be used across the application for admin and cashier features.
 */
export function CSVExportButton<T extends Record<string, any>>({
  data,
  filename,
  headers,
  buttonText = "Export to CSV",
  variant = "outline",
  disabled = false,
  onExport,
}: CSVExportButtonProps<T>) {
  const handleExport = () => {
    try {
      if (!data || data.length === 0) {
        toast.error("No data to export");
        return;
      }

      exportToCSV(data, filename, headers);
      toast.success(`${filename} exported successfully`);

      // Call the onExport callback if provided
      if (onExport) {
        onExport();
      }
    } catch (error) {
      console.error("Error exporting data:", error);
      toast.error("Failed to export data");
    }
  };

  return (
    <Button
      variant={variant}
      onClick={handleExport}
      disabled={disabled || !data || data.length === 0}
    >
      {buttonText}
    </Button>
  );
}

export default CSVExportButton;
