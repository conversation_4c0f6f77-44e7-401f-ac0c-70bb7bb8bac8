// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// User model based on the BankUser interface
model User {
  id              String   @id @default(uuid())
  username        String   @unique @db.VarChar(255)
  displayName     String   @db.VarChar(255)
  email           String   @unique @db.VarChar(255)
  passwordHash    String
  avatar          String   @default("/images/avatars/default.png")
  balance         Float    @default(0) @db.Double
  isEmailVerified <PERSON><PERSON>an  @default(false) @db.TinyInt
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // User status
  status String @default("active") // active, pending, inactive, suspended, frozen

  // User preferences
  defaultView       String  @default("dashboard")
  notifyTransfers   Boolean @default(true) @db.TinyInt
  notifyDeposits    <PERSON>olean @default(true) @db.TinyInt
  notifyWithdrawals Boolean @default(true) @db.TinyInt
  notifyNewsEvents  Boolean @default(false) @db.TinyInt
  notifyAuctions    Boolean @default(true) @db.TinyInt
  notifyChat        Boolean @default(true) @db.TinyInt
  notifyAdmin       Boolean @default(true) @db.TinyInt

  // Connected accounts
  discordConnected  Boolean @default(false) @db.TinyInt
  discordId         String?
  facebookConnected Boolean @default(false) @db.TinyInt
  facebookId        String?

  // Merchant information
  merchantStatus String? @default("none")
  merchantId     Int?
  merchantSlug   String?

  // Auction information
  hasCreatedAuctions Boolean @default(false) @db.TinyInt
  auctionCount       Int     @default(0)

  // User roles
  isAdmin                Boolean            @default(false) @db.TinyInt
  isEditor               Boolean            @default(false) @db.TinyInt
  isBanker               Boolean            @default(false) @db.TinyInt
  isChatModerator        Boolean            @default(false) @db.TinyInt
  isVolunteer            Boolean            @default(false) @db.TinyInt
  isVolunteerCoordinator Boolean            @default(false) @db.TinyInt
  isLeadManager          Boolean            @default(false) @db.TinyInt
  leadManagerCategoryId  String?            @unique
  leadManagerCategory    VolunteerCategory? @relation("CategoryLeadManager", fields: [leadManagerCategoryId], references: [id])
  isSalesManager         Boolean            @default(false) @db.TinyInt
  isLandSteward          Boolean            @default(false) @db.TinyInt

  // Relationships to other models
  newsArticles           NewsArticle[]
  createdEvents          Event[]               @relation("CreatedEvents")
  volunteerShifts        VolunteerAssignment[]
  volunteerHours         VolunteerHours[]
  verifiedVolunteerHours VolunteerHours[]      @relation("VerifiedHours")

  // Bank relationships
  sentTransactions      Transaction[] @relation("SentTransactions")
  receivedTransactions  Transaction[] @relation("ReceivedTransactions")
  processedTransactions Transaction[] @relation("ProcessedTransactions")
  createdCodes          PayCode[]     @relation("CreatedCodes")
  redeemedCodes         PayCode[]     @relation("RedeemedCodes")
  verifiedLedgers       Ledger[]

  // User credentials
  credentials UserCredential[]

  // User notifications
  notifications Notification[] @relation("UserNotifications")

  // User state
  state UserState?

  // Verification codes and temporary passwords
  verificationCodes VerificationCode[]
  temporaryPassword TemporaryPassword?

  // Support ticket relationships
  submittedTickets SupportTicket[] @relation("UserTickets")
  assignedTickets  SupportTicket[] @relation("AssignedTickets")
  resolvedTickets  SupportTicket[] @relation("ResolvedTickets")
  ticketNotes      TicketNote[]

  // Shopping cart and orders
  carts               Cart[]
  orders              Order[]
  ticketHolds         TicketHold[]
  eventCapacityHolds  EventCapacityHold[]
  volunteerSlotHolds  VolunteerSlotHold[]

  // Ships relationships
  captainedShips           Ship[]              @relation("ShipCaptains")
  shipMemberships          ShipMember[]
  captainApplications      CaptainApplication[]
  reviewedApplications     CaptainApplication[] @relation("ApplicationReviewers")
  shipJoinRequests         ShipJoinRequest[]    @relation("ShipJoinRequests")
  initiatedJoinRequests    ShipJoinRequest[]    @relation("InitiatedJoinRequests")
  // Ship deletion relationships
  requestedShipDeletions   ShipDeletionRequest[] @relation("RequestedShipDeletions")
  reviewedShipDeletions    ShipDeletionRequest[] @relation("ReviewedShipDeletions")
  // Form relationships
  createdFormTemplates     FormTemplate[]       @relation("CreatedFormTemplates")
  createdEventForms        EventForm[]          @relation("CreatedEventForms")
  submittedForms           FormSubmission[]     @relation("SubmittedForms")
  reviewedForms            FormSubmission[]     @relation("ReviewedForms")

  @@map("user")
}

// News article model
model NewsArticle {
  id          String       @id @default(uuid())
  title       String       @db.VarChar(255)
  content     String       @db.LongText
  excerpt     String       @db.Text
  image       String?
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  publishedAt DateTime?
  author      User         @relation(fields: [authorId], references: [id])
  authorId    String
  category    NewsCategory @relation(fields: [categoryId], references: [id])
  categoryId  String
  slug        String       @unique
  status      String       @default("draft") // draft, published, paused
  featured    Boolean      @default(false) @db.TinyInt
  views       Int          @default(0)

  @@map("newsarticle")
}

// News category model
model NewsCategory {
  id          String        @id @default(uuid())
  name        String        @unique @db.VarChar(255)
  slug        String        @unique
  description String?       @db.Text
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  articles    NewsArticle[]

  @@map("newscategory")
}

// Transaction model for bank operations
model Transaction {
  id          String   @id @default(uuid())
  amount      Float    @db.Double
  type        String
  status      String
  description String?  @db.VarChar(255)
  note        String?  @db.VarChar(255)
  senderId    String?
  recipientId String?
  sender      User?    @relation("SentTransactions", fields: [senderId], references: [id])
  recipient   User?    @relation("ReceivedTransactions", fields: [recipientId], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // For audit and reconciliation
  processedById String?
  processedBy   User?     @relation("ProcessedTransactions", fields: [processedById], references: [id])
  processedAt   DateTime?

  // For deposits/withdrawals
  paymentMethod String?
  receiptImage  String?

  // For statistical tracking
  ledgerId String?
  ledger   Ledger? @relation(fields: [ledgerId], references: [id])

  // For pay codes
  payCodeId String?
  payCode   PayCode? @relation(fields: [payCodeId], references: [id])

  // For notifications
  notifications Notification[]

  // Volunteer hours
  volunteerHours VolunteerHours[] @relation("VolunteerHoursTransactions")

  @@map("transaction")
}

// PayCode model for creating and redeeming pay codes
model PayCode {
  id           String    @id @default(uuid())
  code         String    @unique
  amount       Float     @db.Double
  createdAt    DateTime  @default(now())
  expiresAt    DateTime
  status       String // active, redeemed, cancelled, expired, paused
  createdById  String
  createdBy    User      @relation("CreatedCodes", fields: [createdById], references: [id])
  redeemedById String?
  redeemedBy   User?     @relation("RedeemedCodes", fields: [redeemedById], references: [id])
  redeemedAt   DateTime?
  uses         Int       @default(0)
  maxUses      Int?

  // For tracking
  transactions Transaction[]

  @@map("paycode")
}

// Ledger model for reconciliation and statistics
model Ledger {
  id               String        @id @default(uuid())
  date             DateTime      @default(now())
  description      String
  totalDeposits    Float         @default(0) @db.Double
  totalWithdrawals Float         @default(0) @db.Double
  totalTransfers   Float         @default(0) @db.Double
  netChange        Float         @default(0) @db.Double
  status           String // pending, verified
  verifiedById     String?
  verifiedBy       User?         @relation(fields: [verifiedById], references: [id])
  verifiedAt       DateTime?
  transactions     Transaction[]

  @@map("ledger")
}

// Notification model for all system notifications
model Notification {
  id        String   @id @default(uuid())
  // Category of notification: transaction, auction, news, admin, system, etc.
  category  String
  // Specific type within category: deposit_request, outbid, new_article, etc.
  type      String
  title     String   @db.VarChar(255)
  message   String   @db.Text
  read      Boolean  @default(false) @db.TinyInt
  // Link to relevant content (URL or resource identifier)
  link      String?
  // Icon to display with notification
  icon      String?
  // Priority level: low, medium, high
  priority  String   @default("medium")
  // User who should receive this notification
  userId    String
  user      User     @relation("UserNotifications", fields: [userId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Optional relation to a transaction
  transactionId String?
  transaction   Transaction? @relation(fields: [transactionId], references: [id])

  @@index([userId])
  @@index([category])
  @@index([read])
  @@map("notification")
}

// UserCredential model for storing multiple authentication methods
model UserCredential {
  id     String @id @default(uuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Credential type (email, discord, facebook, etc.)
  type String // email, discord, facebook, etc.

  // Identifier (email address, discord ID, etc.)
  identifier String

  // Password hash (only for email credentials)
  passwordHash String?

  // OAuth token data (for OAuth providers)
  accessToken  String?   @db.Text
  refreshToken String?   @db.Text
  tokenExpiry  DateTime?

  // Metadata
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  lastUsedAt DateTime?

  @@unique([type, identifier])
  @@index([userId])
  @@map("user_credential")
}

// UserState model for tracking user state and caching
model UserState {
  id     String @id @default(uuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Content state flags
  lastNewsSync     DateTime?
  lastCategorySync DateTime?

  // Feature flags
  betaFeaturesEnabled Boolean @default(false) @db.TinyInt

  // UI preferences
  darkModeEnabled    Boolean @default(true) @db.TinyInt
  compactViewEnabled Boolean @default(false) @db.TinyInt

  // Cache invalidation
  globalCacheVersion Int @default(1)

  // Session data
  lastActive DateTime @default(now())
  deviceInfo String?  @db.Text

  // Analytics
  pageViews    Int @default(0)
  articleReads Int @default(0)

  // Custom JSON data for flexible storage
  customState Json? // For storing arbitrary state data

  // Content access flags
  hasAccessToAuctions  Boolean @default(true) @db.TinyInt
  hasAccessToMerchants Boolean @default(true) @db.TinyInt
  hasAccessToChat      Boolean @default(true) @db.TinyInt

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@map("user_state")
}

// VerificationCode model for email verification and security codes
model VerificationCode {
  id        String   @id @default(uuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  type      String // EMAIL_VERIFICATION, PASSWORD_RESET, ACCOUNT_SECURITY
  code      String
  expiresAt DateTime
  verified  Boolean  @default(false) @db.TinyInt
  attempts  Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, type])
  @@index([userId])
  @@index([code])
  @@map("verification_code")
}

// TemporaryPassword model for storing generated passwords with expiry
model TemporaryPassword {
  id           String   @id @default(uuid())
  userId       String   @unique
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  passwordHash String
  expiresAt    DateTime
  used         Boolean  @default(false) @db.TinyInt
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@index([userId])
  @@map("temporary_password")
}

// SupportTicket model for help desk functionality
model SupportTicket {
  id       String @id @default(uuid())
  subject  String @db.VarChar(255)
  message  String @db.Text
  status   String @default("open") // open, in_progress, resolved, closed
  priority String @default("medium") // low, medium, high, urgent
  category String @default("general") // general, account, banking, technical, other

  // Contact information
  name  String? @db.VarChar(255)
  email String  @db.VarChar(255)
  phone String? @db.VarChar(50)

  // User who submitted the ticket (if authenticated)
  userId String?
  user   User?   @relation("UserTickets", fields: [userId], references: [id])

  // Admin assigned to the ticket
  assignedToId String?
  assignedTo   User?     @relation("AssignedTickets", fields: [assignedToId], references: [id])
  assignedAt   DateTime?

  // Resolution information
  resolvedById String?
  resolvedBy   User?     @relation("ResolvedTickets", fields: [resolvedById], references: [id])
  resolvedAt   DateTime?
  resolution   String?   @db.Text

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Notes and comments
  notes TicketNote[]

  @@index([status])
  @@index([priority])
  @@index([userId])
  @@index([assignedToId])
  @@map("support_ticket")
}

// TicketNote model for comments and internal notes on tickets
model TicketNote {
  id         String  @id @default(uuid())
  content    String  @db.Text
  isInternal Boolean @default(false) @db.TinyInt // true for admin-only notes

  // Ticket this note belongs to
  ticketId String
  ticket   SupportTicket @relation(fields: [ticketId], references: [id], onDelete: Cascade)

  // User who created the note
  authorId String
  author   User   @relation(fields: [authorId], references: [id])

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([ticketId])
  @@index([authorId])
  @@map("ticket_note")
}

// UploadedImage model for tracking image uploads
model UploadedImage {
  id            String   @id @default(cuid())
  filename      String // Original filename
  path          String // File path on disk
  url           String // Public URL path
  mimeType      String // Content type
  size          Int // File size in bytes
  entityId      String? // Related content ID (article, user, etc.)
  entityType    String? // Type of related entity (news, profile, etc.)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  upload_type   String?  @default("general") @db.VarChar(50)
  upload_config Json?
  dimensions    Json?

  @@unique([entityType, entityId, filename])
  @@index([entityType, entityId])
  @@map("uploaded_image")
}

// Event model for the event management system
model Event {
  id               String   @id @default(uuid())
  name             String   @db.VarChar(255)
  description      String   @db.Text
  shortDescription String?  @db.VarChar(500)
  startDate        DateTime
  endDate          DateTime
  location         String?  @db.VarChar(255)
  address          String?  @db.Text
  virtualLink      String?  @db.VarChar(255)
  isVirtual        Boolean  @default(false)
  image            String?
  status           String   @default("draft") // draft, published, cancelled, completed
  capacity         Int?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relationships
  createdById         String
  createdBy           User                  @relation("CreatedEvents", fields: [createdById], references: [id])
  categoryId          String
  category            EventCategory         @relation(fields: [categoryId], references: [id])
  volunteerCategories VolunteerCategory[]   @relation("EventVolunteerCategories")
  volunteerShifts     VolunteerShift[]      @relation("EventVolunteerShifts")
  products            Product[]
  capacityHolds       EventCapacityHold[]
  eventForms          EventForm[]

  @@index([createdById])
  @@index([categoryId])
  @@index([status])
  @@index([startDate])
  @@map("events")
}

// EventCategory model for categorizing events
model EventCategory {
  id          String   @id @default(uuid())
  name        String   @db.VarChar(255)
  description String?  @db.Text
  color       String?  @db.VarChar(50)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  events Event[]

  @@map("event_categories")
}

// Volunteer management models
model VolunteerCategory {
  id          String   @id @default(uuid())
  name        String   @db.VarChar(255)
  description String?  @db.Text
  payRate     Float?   @db.Double // Payment rate in sterling per hour
  eventId     String
  event       Event    @relation("EventVolunteerCategories", fields: [eventId], references: [id], onDelete: Cascade)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Lead Manager relationship
  leadManagerId String? @unique
  leadManager   User?   @relation("CategoryLeadManager")

  // Relationships
  shifts VolunteerShift[]

  @@index([eventId])
  @@map("volunteer_categories")
}

model VolunteerShift {
  id            String            @id @default(uuid())
  title         String            @db.VarChar(255)
  description   String?           @db.Text
  startTime     DateTime
  endTime       DateTime
  location      String?           @db.VarChar(255)
  maxVolunteers Int               @default(1)
  eventId       String
  event         Event             @relation("EventVolunteerShifts", fields: [eventId], references: [id], onDelete: Cascade)
  categoryId    String
  category      VolunteerCategory @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  isAutomated   Boolean           @default(false) // Flag to indicate if shift was auto-generated
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt

  // Relationships
  assignments VolunteerAssignment[]
  slots       VolunteerSlot[] // Individual volunteer slots

  @@index([eventId])
  @@index([categoryId])
  @@index([startTime])
  @@map("volunteer_shifts")
}

model VolunteerAssignment {
  id        String         @id @default(uuid())
  userId    String
  user      User           @relation(fields: [userId], references: [id])
  shiftId   String
  shift     VolunteerShift @relation(fields: [shiftId], references: [id], onDelete: Cascade)
  slotId    String?        @unique // Link to specific volunteer slot
  slot      VolunteerSlot? @relation(fields: [slotId], references: [id])
  status      String    @default("pending") // pending, confirmed, checked_in, completed, no_show, cancelled
  checkedInAt DateTime? // Timestamp when volunteer checked in
  notes       String?   @db.Text
  metadata    Json? // Store additional form data like pirateName, stayingWith, etc.
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relationships
  hours                   VolunteerHours?
  notificationPreferences VolunteerNotificationPreferences?

  @@unique([userId, shiftId])
  @@index([userId])
  @@index([shiftId])
  @@index([slotId])
  @@index([status])
  @@map("volunteer_assignments")
}

model VolunteerNotificationPreferences {
  id                  String              @id @default(uuid())
  assignmentId        String              @unique
  assignment          VolunteerAssignment @relation(fields: [assignmentId], references: [id], onDelete: Cascade)
  emailNotification   Boolean             @default(true)
  websiteNotification Boolean             @default(true)
  createdAt           DateTime            @default(now())
  updatedAt           DateTime            @updatedAt

  @@map("volunteer_notification_preferences")
}

model VolunteerHours {
  id            String              @id @default(uuid())
  assignmentId  String              @unique
  assignment    VolunteerAssignment @relation(fields: [assignmentId], references: [id], onDelete: Cascade)
  userId        String
  user          User                @relation(fields: [userId], references: [id])
  hoursWorked   Float               @db.Double
  paymentAmount Float               @db.Double
  
  // Ship tracking for volunteer hour requirements
  creditShipId  String?             // Which ship gets credit for these hours
  creditShip    Ship?               @relation("CreditedVolunteerHours", fields: [creditShipId], references: [id])
  isDockHours   Boolean             @default(false)
  isLandGrant   Boolean             @default(false)
  paymentStatus String              @default("pending") // pending, processing, paid, cancelled
  verifiedById  String?
  verifiedBy    User?               @relation("VerifiedHours", fields: [verifiedById], references: [id])
  verifiedAt    DateTime?
  transactionId String?
  transaction   Transaction?        @relation("VolunteerHoursTransactions", fields: [transactionId], references: [id])
  createdAt     DateTime            @default(now())
  updatedAt     DateTime            @updatedAt

  @@index([userId])
  @@index([assignmentId])
  @@index([paymentStatus])
  @@map("volunteer_hours")
}

// Individual Volunteer Slot Model
model VolunteerSlot {
  id           String               @id @default(uuid())
  shiftId      String
  shift        VolunteerShift       @relation(fields: [shiftId], references: [id])
  status       TicketStatus         @default(AVAILABLE)
  holdId       String?
  hold         VolunteerSlotHold?   @relation(fields: [holdId], references: [id])
  assignmentId String?              @unique
  assignment   VolunteerAssignment?
  createdAt    DateTime             @default(now())
  updatedAt    DateTime             @updatedAt

  @@index([shiftId])
  @@index([status])
  @@index([holdId])
  @@index([assignmentId])
  @@map("volunteer_slots")
}

// Volunteer Slot Hold Model
model VolunteerSlotHold {
  id        String          @id @default(uuid())
  userId    String
  user      User            @relation(fields: [userId], references: [id])
  slots     VolunteerSlot[] // Direct relationship to specific slots
  expiresAt DateTime
  createdAt DateTime        @default(now())
  updatedAt DateTime        @updatedAt

  @@index([userId])
  @@index([expiresAt])
  @@map("volunteer_slot_holds")
}

// Product Categories
model ProductCategory {
  id          String   @id @default(uuid())
  name        String   @db.VarChar(255)
  description String?  @db.Text
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  products Product[]

  @@map("product_categories")
}

// Products
model Product {
  id               String   @id @default(uuid())
  name             String   @db.VarChar(255)
  description      String?  @db.Text
  shortDescription String?  @db.VarChar(500)
  price            Float    @db.Double
  image            String?
  isActive         Boolean  @default(true)
  affectsCapacity  Boolean  @default(true)
  inventory        Int? // null = unlimited
  isAutoGenerated  Boolean  @default(false) @db.TinyInt // Marks products auto-created for events
  isFree           Boolean  @default(false) @db.TinyInt // Marks products as free/complimentary
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relationships
  categoryId      String
  category        ProductCategory @relation(fields: [categoryId], references: [id])
  eventId         String?
  event           Event?          @relation(fields: [eventId], references: [id])
  orderItems      OrderItem[]
  cartItems       CartItem[]
  tickets         Ticket[] // Individual tickets for this product
  redemptionCodes RedemptionCode[]

  @@index([categoryId])
  @@index([eventId])
  @@map("products")
}

// Ticket Status Enum
enum TicketStatus {
  AVAILABLE
  HELD
  SOLD
  CANCELLED
}

// Individual Ticket Model
model Ticket {
  id        String       @id @default(uuid())
  productId String
  product   Product      @relation(fields: [productId], references: [id])
  status    TicketStatus @default(AVAILABLE)
  holdId    String?
  hold      TicketHold?  @relation(fields: [holdId], references: [id])
  orderId   String?
  order     Order?       @relation(fields: [orderId], references: [id])
  seatInfo  Json? // For events with specific seating
  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt

  @@index([productId])
  @@index([status])
  @@index([holdId])
  @@index([orderId])
  @@map("tickets")
}

// Ticket Hold Model
model TicketHold {
  id         String    @id @default(uuid())
  userId     String
  user       User      @relation(fields: [userId], references: [id])
  cartItemId String?   @unique
  cartItem   CartItem? @relation(fields: [cartItemId], references: [id], onDelete: SetNull)
  tickets    Ticket[] // Direct relationship to specific tickets
  expiresAt  DateTime
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  @@index([userId])
  @@index([expiresAt])
  @@map("ticket_holds")
}

// Event Capacity Hold Model
model EventCapacityHold {
  id         String    @id @default(uuid())
  eventId    String
  event      Event     @relation(fields: [eventId], references: [id], onDelete: Cascade)
  userId     String
  user       User      @relation(fields: [userId], references: [id])
  cartItemId String    @unique
  cartItem   CartItem  @relation(fields: [cartItemId], references: [id], onDelete: Cascade)
  quantity   Int       // Number of capacity units held
  expiresAt  DateTime
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  @@index([eventId])
  @@index([userId])
  @@index([expiresAt])
  @@map("event_capacity_holds")
}

// Shopping Cart Models
model Cart {
  id        String   @id @default(uuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  items CartItem[]

  @@index([userId])
  @@map("carts")
}

model CartItem {
  id                String   @id @default(uuid())
  quantity          Int // We still track quantity for UI purposes
  isCodeRedemption  Boolean  @default(false) @db.TinyInt // Track if added via redemption code
  redemptionCodeId  String?  // Track which redemption code was used
  redemptionCode    RedemptionCode? @relation(fields: [redemptionCodeId], references: [id])
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relationships
  cartId            String
  cart              Cart                @relation(fields: [cartId], references: [id], onDelete: Cascade)
  productId         String
  product           Product             @relation(fields: [productId], references: [id])
  ticketHold        TicketHold?         // Relationship to individual ticket hold
  eventCapacityHold EventCapacityHold?  // Relationship to event capacity hold

  @@index([cartId])
  @@index([productId])
  @@index([redemptionCodeId])
  @@map("cart_items")
}

// Order Models
model Order {
  id              String   @id @default(uuid())
  orderNumber     String   @unique
  status          String // pending, paid, fulfilled, cancelled, refunded
  total           Float    @db.Double
  subtotal        Float    @db.Double
  tax             Float?   @db.Double
  discount        Float?   @db.Double
  paymentMethod   String?
  paymentIntentId String? // Stripe payment intent ID
  notes           String?  @db.Text
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relationships
  userId  String
  user    User        @relation(fields: [userId], references: [id])
  items   OrderItem[]
  tickets Ticket[] // Tickets sold with this order

  @@index([userId])
  @@index([status])
  @@index([orderNumber])
  @@map("orders")
}

model OrderItem {
  id          String   @id @default(uuid())
  quantity    Int
  price       Float    @db.Double // Price at time of purchase
  name        String // Product name at time of purchase
  description String? // Product description at time of purchase
  createdAt   DateTime @default(now())

  // Relationships
  orderId   String
  order     Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  productId String
  product   Product @relation(fields: [productId], references: [id])

  @@index([orderId])
  @@index([productId])
  @@map("order_items")
}

// Redemption Code Model for free product access
model RedemptionCode {
  id        String     @id @default(uuid())
  code      String     @unique
  productId String
  product   Product    @relation(fields: [productId], references: [id], onDelete: Cascade)
  isActive  Boolean    @default(true)
  createdAt DateTime   @default(now())
  cartItems CartItem[] // Track which cart items used this code

  @@index([productId])
  @@index([code])
  @@map("redemption_codes")
}

// Ships System Models
model Ship {
  id          String   @id @default(uuid())
  name        String   @unique @db.VarChar(255)
  description String   @db.Text
  slogan      String?  @db.VarChar(500)
  logo        String?  // file path to uploaded logo
  tags        Json? // array of custom tags
  status      String   @default("active") // active, inactive, deleted, pending_deletion
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  captainId String
  captain   User           @relation("ShipCaptains", fields: [captainId], references: [id])
  members   ShipMember[]
  roles     ShipRole[]
  joinRequests ShipJoinRequest[]
  formSubmissions FormSubmission[]
  deletionRequests ShipDeletionRequest[]
  volunteerRequirements ShipVolunteerRequirement[]
  creditedVolunteerHours VolunteerHours[] @relation("CreditedVolunteerHours")

  @@index([captainId])
  @@index([status])
  @@map("ships")
}

// Ship roles (custom roles created by captains)
model ShipRole {
  id          String   @id @default(uuid())
  name        String   @db.VarChar(255)
  description String?  @db.Text
  createdAt   DateTime @default(now())

  // Relationships
  shipId  String
  ship    Ship         @relation(fields: [shipId], references: [id], onDelete: Cascade)
  members ShipMember[]

  @@unique([shipId, name])
  @@index([shipId])
  @@map("ship_roles")
}

model ShipMember {
  id        String   @id @default(uuid())
  role      String   @default("Member") @db.VarChar(255) // Keep for backward compatibility
  status    String   @default("active") // active, invited, left, removed
  joinedAt  DateTime @default(now())
  leftAt    DateTime?

  // Custom role relationship
  roleId    String?
  customRole ShipRole? @relation(fields: [roleId], references: [id])

  // Relationships
  userId String
  user   User   @relation(fields: [userId], references: [id])
  shipId String
  ship   Ship   @relation(fields: [shipId], references: [id], onDelete: Cascade)

  @@unique([userId, shipId])
  @@index([userId])
  @@index([shipId])
  @@index([status])
  @@index([roleId])
  @@map("ship_members")
}

model CaptainApplication {
  id                  String    @id @default(uuid())
  shipName            String    @db.VarChar(255)
  description         String    @db.Text
  tags                Json? // array of requested tags
  logoPath            String? // path to uploaded logo
  status              String    @default("pending") // pending, approved, rejected
  rejectionReason     String?   @db.Text
  previouslyRejected  Boolean   @default(false)
  appliedAt           DateTime  @default(now())
  reviewedAt          DateTime?

  // Relationships
  userId      String
  user        User    @relation(fields: [userId], references: [id])
  reviewedById String?
  reviewedBy   User?   @relation("ApplicationReviewers", fields: [reviewedById], references: [id])

  @@index([userId])
  @@index([status])
  @@index([reviewedById])
  @@map("captain_applications")
}

model ShipJoinRequest {
  id          String    @id @default(uuid())
  type        String    // invite, request
  status      String    @default("pending") // pending, accepted, declined, expired
  message     String?   @db.Text
  createdAt   DateTime  @default(now())
  respondedAt DateTime?

  // Relationships
  userId      String // User who will join the ship
  user        User   @relation("ShipJoinRequests", fields: [userId], references: [id])
  shipId      String
  ship        Ship   @relation(fields: [shipId], references: [id], onDelete: Cascade)
  requestedById String // Captain who sent invite or user who requested
  requestedBy   User   @relation("InitiatedJoinRequests", fields: [requestedById], references: [id])

  @@index([userId])
  @@index([shipId])
  @@index([requestedById])
  @@index([status])
  @@map("ship_join_requests")
}

model ShipDeletionRequest {
  id          String    @id @default(uuid())
  reason      String?   @db.Text // Optional reason for deletion
  status      String    @default("pending") // pending, approved, rejected, cancelled
  message     String?   @db.Text // Message from captain or admin notes
  createdAt   DateTime  @default(now())
  reviewedAt  DateTime?

  // Relationships
  shipId      String
  ship        Ship   @relation(fields: [shipId], references: [id], onDelete: Cascade)
  requestedById String // Captain who requested deletion
  requestedBy   User   @relation("RequestedShipDeletions", fields: [requestedById], references: [id])
  reviewedById  String? // Land Steward who reviewed the request
  reviewedBy    User?   @relation("ReviewedShipDeletions", fields: [reviewedById], references: [id])

  @@index([shipId])
  @@index([requestedById])
  @@index([reviewedById])
  @@index([status])
  @@map("ship_deletion_requests")
}

// Form Builder Models for Phase 4
model FormTemplate {
  id            String      @id @default(uuid())
  name          String      @db.VarChar(255)
  description   String?     @db.Text
  isReusable    Boolean     @default(true)
  formStructure Json        // Field definitions
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // Relationships
  createdById String
  createdBy   User        @relation("CreatedFormTemplates", fields: [createdById], references: [id])
  eventForms  EventForm[]

  @@index([createdById])
  @@index([isReusable])
  @@map("form_templates")
}

model EventForm {
  id                    String     @id @default(uuid())
  name                  String     @db.VarChar(255)
  description           String?    @db.Text
  formStructure         Json       // Field definitions
  status                String     @default("draft") // draft, active, closed
  submissionDeadline    DateTime?
  requiredVolunteerHours Float?     @db.Double // Volunteer hours required when form is approved
  createdAt             DateTime   @default(now())
  updatedAt             DateTime   @updatedAt

  // Relationships
  eventId     String
  event       Event           @relation(fields: [eventId], references: [id], onDelete: Cascade)
  templateId  String?
  template    FormTemplate?   @relation(fields: [templateId], references: [id])
  createdById String
  createdBy   User            @relation("CreatedEventForms", fields: [createdById], references: [id])
  submissions FormSubmission[]

  @@index([eventId])
  @@index([templateId])
  @@index([createdById])
  @@index([status])
  @@index([submissionDeadline])
  @@map("event_forms")
}

model FormSubmission {
  id             String    @id @default(uuid())
  submissionData Json      // Answers to form fields
  status         String    @default("submitted") // draft, submitted, reviewed, approved, rejected
  reviewNotes    String?   @db.Text
  submittedAt    DateTime  @default(now())
  reviewedAt     DateTime?

  // Relationships
  formId        String
  form          EventForm @relation(fields: [formId], references: [id], onDelete: Cascade)
  shipId        String
  ship          Ship      @relation(fields: [shipId], references: [id], onDelete: Cascade)
  submittedById String
  submittedBy   User      @relation("SubmittedForms", fields: [submittedById], references: [id])
  reviewedById  String?
  reviewedBy    User?     @relation("ReviewedForms", fields: [reviewedById], references: [id])
  volunteerRequirement ShipVolunteerRequirement?

  @@unique([formId, shipId]) // One submission per ship per form
  @@index([formId])
  @@index([shipId])
  @@index([submittedById])
  @@index([reviewedById])
  @@index([status])
  @@map("form_submissions")
}

model ShipVolunteerRequirement {
  id                String        @id @default(uuid())
  shipId            String
  ship              Ship          @relation(fields: [shipId], references: [id], onDelete: Cascade)
  formSubmissionId  String        @unique
  formSubmission    FormSubmission @relation(fields: [formSubmissionId], references: [id], onDelete: Cascade)
  requiredHours     Float         @db.Double
  completedHours    Float         @default(0) @db.Double
  status            String        @default("pending") // pending, in_progress, completed, overdue
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  @@index([shipId])
  @@index([formSubmissionId])
  @@index([status])
  @@map("ship_volunteer_requirements")
}
