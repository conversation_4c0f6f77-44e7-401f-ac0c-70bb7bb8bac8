"use client";

import React, { useState, useEffect } from "react";
import { format, addDays, subDays, isToday, isSameDay } from "date-fns";
import fetchClient from "@/lib/fetchClient";
import { LeadDashboardStats } from "./LeadDashboardStats";
import { LeadShiftCard } from "./ShiftCard";
import toast from "react-hot-toast";

interface User {
  id: string;
  displayName: string;
  avatar: string;
  email: string;
}

interface VolunteerHours {
  id: string;
  hoursWorked: number;
  paymentAmount: number;
  paymentStatus: string;
}

interface Assignment {
  id: string;
  userId: string;
  status: string;
  user: User;
  hours: VolunteerHours | null;
}

interface Shift {
  id: string;
  title: string;
  description: string | null;
  startTime: string;
  endTime: string;
  location: string | null;
  maxVolunteers: number;
  vacancies: number;
  assignments: Assignment[];
}

interface LeadDashboardMainProps {
  categoryId?: string;
  isCoordinatorView?: boolean;
}

export const LeadDashboardMain: React.FC<LeadDashboardMainProps> = ({
  categoryId,
  isCoordinatorView = false,
}) => {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [shifts, setShifts] = useState<Shift[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchShifts = async (date: Date) => {
    try {
      setIsLoading(true);
      const formattedDate = format(date, "yyyy-MM-dd");
      
      // Use coordinator endpoint if in coordinator view, otherwise use regular lead endpoint
      const endpoint = isCoordinatorView && categoryId
        ? `/api/volunteer/management/shifts?date=${formattedDate}&categoryId=${categoryId}`
        : `/api/volunteer/lead/shifts?date=${formattedDate}`;
        
      const response = await fetchClient.get<{ shifts: Shift[] }>(endpoint);
      setShifts(response.shifts);
      setError(null);
    } catch (err) {
      console.error("Error fetching shifts:", err);
      setError("Failed to load shifts. Please try again.");
      setShifts([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchShifts(selectedDate);
  }, [selectedDate, categoryId]);

  const handlePreviousDay = () => {
    setSelectedDate((prev) => subDays(prev, 1));
  };

  const handleNextDay = () => {
    setSelectedDate((prev) => addDays(prev, 1));
  };

  const handleToday = () => {
    setSelectedDate(new Date());
  };

  const handleRefresh = () => {
    fetchShifts(selectedDate);
    toast.success("Shifts refreshed");
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white mb-2">
          {isCoordinatorView ? "Category Lead Dashboard (Coordinator View)" : "Lead Dashboard"}
        </h1>
        <p className="text-gray-400">
          {isCoordinatorView 
            ? "Coordinator view of category lead dashboard - manage shifts and volunteers"
            : "Manage your assigned category, shifts, and volunteers"
          }
        </p>
      </div>

      {/* Stats Section */}
      <LeadDashboardStats categoryId={categoryId} isCoordinatorView={isCoordinatorView} />

      {/* Date Navigation */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-2">
          <button
            onClick={handlePreviousDay}
            className="p-2 bg-secondary hover:bg-secondary-dark rounded-md text-white"
            aria-label="Previous day"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
          <button
            onClick={handleToday}
            className={`px-3 py-1 rounded-md text-sm font-medium ${
              isToday(selectedDate)
                ? "bg-primary text-white"
                : "bg-secondary hover:bg-secondary-dark text-white"
            }`}
          >
            Today
          </button>
          <button
            onClick={handleNextDay}
            className="p-2 bg-secondary hover:bg-secondary-dark rounded-md text-white"
            aria-label="Next day"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        </div>
        <div className="text-center">
          <h2 className="text-lg font-semibold text-white">
            {format(selectedDate, "MMMM d, yyyy")}
            {isToday(selectedDate) && (
              <span className="ml-2 bg-primary text-white text-xs px-2 py-1 rounded-full">
                Today
              </span>
            )}
          </h2>
        </div>
        <button
          onClick={handleRefresh}
          className="p-2 bg-secondary hover:bg-secondary-dark rounded-md text-white"
          aria-label="Refresh"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
        </button>
      </div>

      {/* Shifts Section */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-white">Shifts</h2>
          <span className="text-gray-400 text-sm">
            {shifts.length} {shifts.length === 1 ? "shift" : "shifts"} found
          </span>
        </div>

        {isLoading ? (
          <div className="space-y-4 animate-pulse">
            {[...Array(3)].map((_, i) => (
              <div
                key={i}
                className="bg-secondary rounded-lg shadow-md border border-gray-600 h-24"
              ></div>
            ))}
          </div>
        ) : error ? (
          <div className="bg-red-900/20 border border-red-700 text-red-100 p-4 rounded-lg">
            <p>{error}</p>
          </div>
        ) : shifts.length === 0 ? (
          <div className="bg-secondary-dark p-6 rounded-lg border border-gray-600 text-center">
            <p className="text-gray-400 mb-2">No shifts found for this date.</p>
            <p className="text-gray-500 text-sm">
              Try selecting a different date or check if shifts have been
              created for your category.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {shifts.map((shift) => (
              <LeadShiftCard
                key={shift.id}
                shift={shift}
                onUpdate={handleRefresh}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
