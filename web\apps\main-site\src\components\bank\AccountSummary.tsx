"use client";

import React from "react";
import { useAccountSummary } from "../../hooks/useBank";

export const AccountSummary: React.FC = () => {
  const { data: accountSummary, isLoading, error } = useAccountSummary();

  // Format date for last login
  const formatLastLogin = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();

    // Check if the date is today
    if (date.toDateString() === today.toDateString()) {
      return `Today, ${date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })}`;
    }

    // Check if the date is yesterday
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    if (date.toDateString() === yesterday.toDateString()) {
      return `Yesterday, ${date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })}`;
    }

    // Otherwise, return the full date
    return date.toLocaleDateString([], {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (isLoading) {
    return (
      <div className="bg-secondary-light rounded-lg shadow-md p-4">
        <h2 className="text-lg font-semibold mb-4">Account Summary</h2>
        <div className="space-y-3">
          <div className="animate-pulse h-4 bg-gray-600 rounded w-3/4 mb-3"></div>
          <div className="animate-pulse h-4 bg-gray-600 rounded w-3/4 mb-3"></div>
          <div className="animate-pulse h-4 bg-gray-600 rounded w-3/4 mb-3"></div>
          <div className="animate-pulse h-4 bg-gray-600 rounded w-3/4"></div>
        </div>
      </div>
    );
  }

  if (error || !accountSummary) {
    return (
      <div className="bg-secondary-light rounded-lg shadow-md p-4">
        <h2 className="text-lg font-semibold mb-4">Account Summary</h2>
        <div className="text-error">
          Failed to load account summary. Please try again later.
        </div>
      </div>
    );
  }

  return (
    <div className="bg-secondary-light rounded-lg shadow-md p-4">
      <h2 className="text-lg font-semibold mb-4">Account Summary</h2>
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-text-secondary">Account Type:</span>
          <span className="font-medium">{accountSummary.accountType}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-text-secondary">Account Status:</span>
          <span className="font-medium text-success">
            {accountSummary.accountStatus}
          </span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-text-secondary">Last Login:</span>
          <span className="font-medium">
            {formatLastLogin(accountSummary.lastLogin)}
          </span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-text-secondary">Pending Transactions:</span>
          <span className="font-medium">
            {accountSummary.pendingTransactions}
          </span>
        </div>
      </div>
    </div>
  );
};
