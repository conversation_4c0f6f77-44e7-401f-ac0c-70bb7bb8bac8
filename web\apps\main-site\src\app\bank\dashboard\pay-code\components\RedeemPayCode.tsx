import React, { useState } from "react";
import { toast } from "react-hot-toast";
import { RedeemPayCodeConfirmationModal } from "./modals/RedeemPayCodeConfirmationModal";
import { useRedeemPayCode, useBankUser } from "../../../../../hooks/useBank";

// No props needed for this component
type RedeemPayCodeProps = Record<string, never>;

export const RedeemPayCode: React.FC<RedeemPayCodeProps> = () => {
  const [redeemCode, setRedeemCode] = useState("");
  const [showRedeemConfirmation, setShowRedeemConfirmation] = useState(false);
  const [redeemDetails, setRedeemDetails] = useState<{
    amount: string;
    createdBy: string;
    code: string;
  } | null>(null);

  // Hooks for API calls
  const redeemPayCodeMutation = useRedeemPayCode();
  const { data: userData } = useBankUser();

  const handleRedeemSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Use the trial run feature to validate the code without redeeming it
    redeemPayCodeMutation.mutate(
      { code: redeemCode, trialRun: true },
      {
        onSuccess: (data: any) => {
          if (data.valid) {
            setRedeemDetails({
              amount: data.payCode.amount.toString(),
              createdBy: data.payCode.createdBy.displayName,
              code: data.payCode.code,
            });
            setShowRedeemConfirmation(true);
          } else {
            toast.error("Invalid pay code");
          }
        },
        onError: (error: any) => {
          toast.error(error.message || "Failed to validate pay code");
        },
      },
    );
  };

  const handleRedeemConfirm = () => {
    // Actually redeem the pay code
    redeemPayCodeMutation.mutate(
      { code: redeemCode, trialRun: false },
      {
        onSuccess: () => {
          toast.success("Pay code redeemed successfully!");
          setRedeemCode("");
          setRedeemDetails(null);
          setShowRedeemConfirmation(false);
        },
        onError: (error: any) => {
          toast.error(error.message || "Failed to redeem pay code");
          setShowRedeemConfirmation(false);
        },
      },
    );
  };

  const handleRedeemCancel = () => {
    setRedeemDetails(null);
    setShowRedeemConfirmation(false);
  };

  return (
    <>
      <h2 className="text-2xl font-bold text-white mb-6">Redeem Pay-Code</h2>
      <div className="mb-4 p-3 bg-secondary rounded-md text-sm text-gray-300">
        <p className="flex items-start">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2 flex-shrink-0 text-primary"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <span>
            Redeeming a pay code will{" "}
            <strong>deduct the specified amount from your balance</strong> and
            pay it to the person who created the code.
          </span>
        </p>
      </div>
      <form onSubmit={handleRedeemSubmit}>
        <div className="space-y-4">
          <div>
            <label htmlFor="code" className="block text-white font-medium mb-2">
              Pay-Code
            </label>
            <input
              type="text"
              id="code"
              value={redeemCode}
              onChange={(e) => setRedeemCode(e.target.value)}
              className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Enter Pay-Code (e.g., BOS-XXXX-XXXX)"
              required
            />
          </div>

          <div className="flex justify-end space-x-4 pt-4">
            <button
              type="button"
              onClick={() => setRedeemCode("")}
              className="px-4 py-2 border border-gray-600 rounded-md text-white hover:bg-secondary"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
            >
              Redeem Pay-Code
            </button>
          </div>
        </div>
      </form>{" "}
      {/* Confirmation Modal */}
      {showRedeemConfirmation && redeemDetails && (
        <RedeemPayCodeConfirmationModal
          redeemDetails={redeemDetails}
          userData={userData ? { balance: userData.balance } : null}
          onConfirm={handleRedeemConfirm}
          onCancel={handleRedeemCancel}
        />
      )}
    </>
  );
};
