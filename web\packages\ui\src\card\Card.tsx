import React from "react";

export interface CardProps {
  /**
   * Card children
   */
  children: React.ReactNode;
  /**
   * Card title
   */
  title?: string;
  /**
   * Card padding
   */
  padding?: "none" | "sm" | "md" | "lg";
  /**
   * Card border
   */
  border?: boolean;
  /**
   * Card shadow
   */
  shadow?: "none" | "sm" | "md" | "lg";
  /**
   * Card className
   */
  className?: string;
  /**
   * Card header action
   */
  headerAction?: React.ReactNode;
}

export const Card = ({
  children,
  title,
  padding = "md",
  border = true,
  shadow = "md",
  className = "",
  headerAction,
}: CardProps) => {
  // Padding classes
  const paddingClasses = {
    none: "",
    sm: "p-3",
    md: "p-4",
    lg: "p-6",
  };

  // Shadow classes
  const shadowClasses = {
    none: "",
    sm: "shadow-sm",
    md: "shadow",
    lg: "shadow-lg",
  };

  // Border classes
  const borderClasses = border ? "border border-gray-600" : "";

  return (
    <div
      className={`
        bg-secondary-light rounded-lg
        ${shadowClasses[shadow]}
        ${borderClasses}
        ${className}
      `}
    >
      {title && (
        <div className="px-4 py-3 border-b border-gray-600 bg-secondary flex justify-between items-center overflow-hidden">
          <h3 className="text-lg font-medium">{title}</h3>
          {headerAction && <div>{headerAction}</div>}
        </div>
      )}
      <div className={paddingClasses[padding]}>{children}</div>
    </div>
  );
};

Card.displayName = "Card";
