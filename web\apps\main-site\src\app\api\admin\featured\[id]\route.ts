import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
interface Params {
  params: {
    id: string;
  };
}

export async function PATCH(request: Request, { params }: Params) {
  const { id } = params;

  try {
    // Check if user is authenticated and has admin role
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const isAdmin = await userHasRole(request, "admin");
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Admin privileges required" },
        { status: 403 },
      );
    }

    // Get type from request body
    const { type } = await request.json();

    if (type === "news") {
      // Find the article
      const article = await prisma.newsArticle.findUnique({
        where: { id },
      });

      if (!article) {
        return NextResponse.json(
          { error: "Article not found" },
          { status: 404 },
        );
      }

      // Toggle featured status
      const updatedArticle = await prisma.newsArticle.update({
        where: { id },
        data: {
          featured: !article.featured,
        },
        select: {
          id: true,
          title: true,
          image: true,
          featured: true,
          updatedAt: true,
        },
      });

      // Transform to match the FeaturedContent interface
      const transformedArticle = {
        id: updatedArticle.id,
        title: updatedArticle.title,
        type: "news" as const,
        image: updatedArticle.image,
        featured: updatedArticle.featured,
        updatedAt: updatedArticle.updatedAt.toISOString(),
      };

      return NextResponse.json(transformedArticle);
    } else if (type === "hero") {
      // This would typically update a hero banner table
      // For now, return a placeholder
      return NextResponse.json({
        id: id,
        title: "Bank of Styx Welcome Banner",
        type: "hero",
        image: "/images/hero/welcome-banner.jpg",
        featured: true,
        updatedAt: new Date().toISOString(),
      });
    } else {
      return NextResponse.json(
        { error: "Invalid content type" },
        { status: 400 },
      );
    }
  } catch (error) {
    console.error("Error toggling featured status:", error);
    return NextResponse.json(
      { error: "Failed to toggle featured status" },
      { status: 500 },
    );
  }
}
