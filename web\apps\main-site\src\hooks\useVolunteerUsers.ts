"use client";

import { useQuery } from "@tanstack/react-query";
import fetchClient from "@/lib/fetchClient";

// Types
export interface VolunteerUser {
  id: string;
  username: string;
  displayName: string;
  avatar: string | null;
  email: string;
}

// Query keys
export const volunteerUserQueryKeys = {
  userSearch: (query: string) => ["volunteerUserSearch", query],
};

/**
 * Hook to search for users to assign as volunteers
 */
export function useVolunteerUserSearch(query: string) {
  return useQuery({
    queryKey: volunteerUserQueryKeys.userSearch(query),
    queryFn: async () => {
      if (!query || query.length < 2) return [];
      try {
        return fetchClient.get<VolunteerUser[]>(
          `/api/volunteer/user-search?query=${encodeURIComponent(query)}`,
        );
      } catch (error) {
        console.error("Error in useVolunteerUserSearch:", error);
        // Return empty array instead of throwing to prevent UI from breaking
        return [];
      }
    },
    enabled: query.length >= 2, // Only run query if search term is at least 2 characters
    retry: 1, // Only retry once to avoid excessive failed requests
    // Add a stale time to prevent too many requests
    staleTime: 10000, // 10 seconds
  });
}

export default {
  useVolunteerUserSearch,
};
