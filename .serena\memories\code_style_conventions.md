# Bank of Styx - Code Style & Conventions

## TypeScript Configuration
- **Strict Type Checking**: Enabled via shared base config
- **Path Aliases**: `@/*` maps to `./src/*`
- **Target**: ES modules with modern JS features

## Import Conventions

### Shared UI Components
- **CORRECT**: `import { Modal, Button, Input } from "@bank-of-styx/ui"`
- **WRONG**: `@ui/modal` or direct paths
- **Available Components**: Modal, Button, Input, Card, Pagination, Spinner

### Prisma Import Pattern
- **CORRECT**: `import prisma from "@/lib/prisma"` (default import)
- **WRONG**: `import { prisma } from "@/lib/prisma"` (named import doesn't exist)

## Authentication Patterns

### API Routes
- **CORRECT**: `const user = await getCurrentUser(request);` (pass request)
- **WRONG**: `const user = await getCurrentUser();` (missing parameter)
- **Pattern**: Server-side auth functions require request context

### Client vs Server
- **Client Components**: Use API endpoints (`fetch("/api/auth/me")`)
- **Server Components**: Use server utilities directly
- **Never mix**: Server utilities don't work in client components

## Database Patterns

### ID Handling
- **CORRECT**: `const id = params.id;` (UUIDs are strings)
- **WRONG**: `const id = parseInt(params.id);` (don't parse UUIDs)
- **Pattern**: Check schema.prisma for actual field types

### Prisma Field Naming
- **CORRECT**: `submittedById`, `reviewedById` (Prisma auto-adds "Id")
- **WRONG**: `submittedBy`, `reviewedBy` (these are relation names)
- **Pattern**: Relation fields end with "Id"

## Component Props Reference

### Button Component
- **Valid variants**: "primary", "secondary", "accent", "outline", "ghost"
- **Valid sizes**: "sm", "md", "lg"
- **Note**: "danger" variant doesn't exist

### Modal Component
- **Valid sizes**: "sm", "md", "lg", "xl", "full"
- **Note**: "large" is not valid - use "lg"

## File Organization
- **Feature-based**: Components organized by domain (auth/, bank/, events/)
- **Shared Components**: Use `@bank-of-styx/ui` package
- **Type Definitions**: Shared `types.ts` files to avoid duplication
- **API Routes**: Follow Next.js App Router conventions

## Code Quality Rules
- **No Comments**: Don't add comments unless explicitly asked
- **Security**: Never log or expose secrets/keys
- **Error Handling**: Use standardized API error responses
- **Real-time**: Use SSE for live updates, not polling