/**
 * User-related type definitions
 */

// User roles interface
export interface UserRoles {
  admin: boolean;
  editor: boolean;
  banker: boolean;
  chatModerator: boolean;
  volunteerCoordinator: boolean;
  leadManager: boolean;
  salesManager: boolean;
  landSteward: boolean;
  volunteer?: boolean;
}

// User status type
export type UserStatus =
  | "active"
  | "pending"
  | "inactive"
  | "suspended"
  | "frozen";

// Basic user interface
export interface User {
  id: string;
  username: string;
  displayName: string;
  avatar: string;
  email?: string;
  isEmailVerified?: boolean;
  status?: UserStatus;
  roles?: UserRoles;
  createdAt?: string;
}
