import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import prisma from "@/lib/prisma";
import { getDiscordUser } from "@/lib/discord";
import { downloadAndSaveDiscordAvatar } from "@/lib/discordAvatarUtils";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export async function OPTIONS(req: NextRequest) {
  // Handle OPTIONS request for CORS preflight
  const response = new NextResponse(null, { status: 204 });

  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set("Access-Control-Allow-Methods", "POST, OPTIONS");
  response.headers.set(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization",
  );

  return response;
}

export async function POST(req: NextRequest) {
  try {
    // Get current user
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Log user data for debugging
    console.log("User data for Discord avatar sync:", {
      id: user.id,
      username: user.username,
      discordConnected: user.discordConnected,
      discordId: user.discordId,
    });

    // Check if user has Discord connected
    if (!user.discordConnected || !user.discordId) {
      return NextResponse.json(
        { error: "Discord account not connected" },
        { status: 400 },
      );
    }

    // Get Discord credential
    const discordCredential = await prisma.userCredential.findFirst({
      where: {
        userId: user.id,
        type: "discord",
      },
    });

    // Log credential data for debugging
    console.log("Discord credential found:", discordCredential ? true : false);

    if (!discordCredential || !discordCredential.accessToken) {
      // If we have discordConnected=true but no credential, fix the inconsistency
      if (user.discordConnected && user.discordId) {
        console.log(
          "Inconsistent state: User has Discord connected but no credential found",
        );

        // Update user record to reflect the actual state
        await prisma.user.update({
          where: { id: user.id },
          data: {
            discordConnected: false,
            discordId: null,
          },
        });
      }

      return NextResponse.json(
        { error: "Discord credentials not found" },
        { status: 400 },
      );
    }

    // Get Discord user data
    const discordUser = await getDiscordUser(discordCredential.accessToken);

    if (!discordUser.avatar) {
      return NextResponse.json(
        { error: "Discord user has no avatar" },
        { status: 400 },
      );
    }

    try {
      // Download and save avatar
      const avatarUrl = await downloadAndSaveDiscordAvatar(
        discordUser.id,
        discordUser.avatar,
        user.id,
      );

      // Update user with new avatar URL
      await prisma.user.update({
        where: { id: user.id },
        data: { avatar: avatarUrl },
      });

      return NextResponse.json({
        success: true,
        avatarUrl,
      });
    } catch (avatarError) {
      console.error("Error in avatar download/save process:", avatarError);
      return NextResponse.json(
        { error: "Failed to download or save Discord avatar" },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error("Error syncing Discord avatar:", error);

    // Check if it's a Discord API error
    if (error instanceof Error) {
      if (error.message.includes("Failed to get Discord user")) {
        // Token might be expired or invalid
        return NextResponse.json(
          {
            error:
              "Discord authentication expired. Please reconnect your Discord account.",
          },
          { status: 401 },
        );
      }
    }

    return NextResponse.json(
      { error: "Failed to sync Discord avatar" },
      { status: 500 },
    );
  }
}
