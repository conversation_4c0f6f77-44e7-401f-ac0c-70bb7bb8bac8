import React, { useState, useEffect, useRef } from "react";

export interface User {
  id: string;
  username: string;
  displayName: string;
  avatar?: string;
}

export interface UserSearchInputProps {
  users: User[];
  onUserSelect: (username: string) => void;
  selectedUsername?: string;
  placeholder?: string;
  label?: string;
  required?: boolean;
  className?: string;
  disabled?: boolean;
  error?: string;
  helperText?: string;
}

export const UserSearchInput: React.FC<UserSearchInputProps> = ({
  users,
  onUserSelect,
  selectedUsername = "",
  placeholder = "Search for a user...",
  label = "",
  required = false,
  className = "",
  disabled = false,
  error = "",
  helperText = "",
}) => {
  const [searchInput, setSearchInput] = useState<string>("");
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [showSuggestions, setShowSuggestions] = useState<boolean>(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Find the initially selected user
  useEffect(() => {
    if (selectedUsername) {
      const user = users.find((user) => user.username === selectedUsername);
      if (user) {
        setSelectedUser(user);
        setSearchInput(`@${user.username} (${user.displayName})`);
      }
    }
  }, [selectedUsername, users]);

  // Handle input change and filter users
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value;
    setSearchInput(input);

    // Clear selected user when input changes
    if (
      selectedUser &&
      input !== `@${selectedUser.username} (${selectedUser.displayName})`
    ) {
      setSelectedUser(null);
      onUserSelect("");
    }

    // Filter users if input is 3 or more characters
    if (input.length >= 3) {
      const query = input.toLowerCase();
      const filtered = users.filter(
        (user) =>
          user.username.toLowerCase().includes(query) ||
          user.displayName.toLowerCase().includes(query),
      );
      setFilteredUsers(filtered);
      setShowSuggestions(true);
    } else {
      setFilteredUsers([]);
      setShowSuggestions(false);
    }
  };

  // Handle user selection from suggestions
  const handleSelectUser = (user: User) => {
    setSelectedUser(user);
    setSearchInput(`@${user.username} (${user.displayName})`);
    setShowSuggestions(false);
    onUserSelect(user.username);
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Generate a random ID for accessibility
  const inputId = `user-search-${Math.random().toString(36).substring(2, 9)}`;

  return (
    <div className={`${className}`}>
      {label && (
        <label htmlFor={inputId} className="block text-white font-medium mb-2">
          {label} {required && <span className="text-accent">*</span>}
        </label>
      )}
      <div className="relative">
        <input
          id={inputId}
          ref={inputRef}
          type="text"
          value={searchInput}
          onChange={handleInputChange}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          className={`w-full px-4 py-2 bg-secondary border ${
            error ? "border-accent" : "border-gray-600"
          } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary ${
            disabled ? "opacity-60 cursor-not-allowed" : ""
          }`}
          aria-invalid={error ? "true" : "false"}
          aria-describedby={
            error
              ? `${inputId}-error`
              : helperText
              ? `${inputId}-helper-text`
              : undefined
          }
          onClick={() => {
            // Show suggestions if input has 3+ characters and no user is selected
            if (searchInput.length >= 3 && !selectedUser) {
              setShowSuggestions(true);
            }
          }}
        />

        {/* Clear button */}
        {searchInput && !disabled && (
          <button
            type="button"
            onClick={() => {
              setSearchInput("");
              setSelectedUser(null);
              onUserSelect("");
              inputRef.current?.focus();
            }}
            className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white focus:outline-none"
            aria-label="Clear input"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        )}

        {/* User suggestions dropdown */}
        {showSuggestions && filteredUsers.length > 0 && (
          <div
            ref={suggestionsRef}
            className="absolute z-10 mt-1 w-full bg-secondary border border-gray-600 rounded-md shadow-lg"
          >
            <ul className="py-1 max-h-60 overflow-auto">
              {filteredUsers.map((user) => (
                <li
                  key={user.id}
                  className="px-4 py-2 hover:bg-secondary-light cursor-pointer flex items-center gap-3"
                  onClick={() => handleSelectUser(user)}
                >
                  {user.avatar && (
                    <img
                      src={user.avatar}
                      alt={user.displayName}
                      className="w-6 h-6 rounded-full object-cover"
                    />
                  )}
                  <div>
                    <span className="text-white font-medium">
                      @{user.username}
                    </span>
                    <span className="text-gray-400 ml-2">
                      ({user.displayName})
                    </span>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* No results message */}
        {showSuggestions &&
          searchInput.length >= 3 &&
          filteredUsers.length === 0 && (
            <div className="absolute z-10 mt-1 w-full bg-secondary border border-gray-600 rounded-md shadow-lg p-4 text-center">
              <p className="text-gray-400">No users found</p>
            </div>
          )}
      </div>

      {/* Error message */}
      {error && (
        <p
          id={`${inputId}-error`}
          className="mt-1 text-sm text-accent"
          role="alert"
        >
          {error}
        </p>
      )}

      {/* Helper text */}
      {helperText && !error && (
        <p id={`${inputId}-helper-text`} className="mt-1 text-sm text-gray-400">
          {helperText}
        </p>
      )}
    </div>
  );
};
