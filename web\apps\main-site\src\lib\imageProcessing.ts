/**
 * Utility functions for image processing
 *
 * Note: This is a simplified version that doesn't use <PERSON> for image processing.
 * It simply saves the original image and returns the same URL for all versions.
 * In a production environment, you would want to use Sharp or another image processing
 * library to create properly sized versions of the images.
 */
import { join } from "path";
import { v4 as uuidv4 } from "uuid";
import { mkdir, writeFile } from "fs/promises";
import { existsSync } from "fs";

// Define upload directories
const UPLOAD_DIR = join(process.cwd(), "public", "uploads");
const NEWS_IMAGES_DIR = join(UPLOAD_DIR, "news");

/**
 * Ensure all required directories exist
 */
export async function ensureDirectories() {
  const dirs = [UPLOAD_DIR, NEWS_IMAGES_DIR];

  for (const dir of dirs) {
    if (!existsSync(dir)) {
      await mkdir(dir, { recursive: true });
    }
  }
}

/**
 * Process an image file for news articles
 * This simplified version just saves the original image and returns the same URL for all versions
 */
export async function processNewsImage(file: File): Promise<{
  original: string;
  processed: string;
  thumbnail: string;
}> {
  // Ensure directories exist
  await ensureDirectories();

  // Generate unique filename
  const fileExtension = file.name.split(".").pop() || "jpg";
  const fileName = `${uuidv4()}.${fileExtension}`;

  // Define file path
  const originalPath = join(NEWS_IMAGES_DIR, fileName);

  // Convert file to Uint8Array
  const arrayBuffer = await file.arrayBuffer();
  const uint8Array = new Uint8Array(arrayBuffer);

  // Save original file
  await writeFile(originalPath, uint8Array);

  // Generate URL - use absolute path to avoid issues with SSE connections
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "";
  const imageUrl = `${baseUrl}/uploads/news/${fileName}`;

  // Return the same URL for all versions
  return {
    original: imageUrl,
    processed: imageUrl,
    thumbnail: imageUrl,
  };
}
