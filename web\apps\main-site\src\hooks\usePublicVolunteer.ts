"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import fetchClient from "@/lib/fetchClient";
import { CheckInStatus } from "@/services/volunteerCheckinService";

// Types
export interface VolunteerEvent {
  id: string;
  name: string;
  description: string;
  shortDescription: string | null;
  startDate: string;
  endDate: string;
  location: string | null;
  image: string | null;
}

export interface VolunteerCategory {
  id: string;
  name: string;
  description: string | null;
  payRate: number;
  eventId: string;
  leadManagerId: string | null;
  stats: {
    totalShifts: number;
    totalSignups: number;
    availableSlots: number;
  };
}

export interface VolunteerShift {
  id: string;
  title: string;
  description: string | null;
  startTime: string;
  endTime: string;
  location: string | null;
  maxVolunteers: number;
  vacancies: number;
  categoryId: string;
  eventId: string;
  category: {
    name: string;
    payRate: number;
  };
  event: {
    name: string;
  };
}

export interface VolunteerAssignment {
  id: string;
  userId: string;
  shiftId: string;
  status:
    | "pending"
    | "confirmed"
    | "checked_in"
    | "completed"
    | "no_show"
    | "cancelled";
  createdAt: string;
  updatedAt: string;
  emailNotification: boolean;
  websiteNotification: boolean;
  shift: VolunteerShift;
}

export interface VolunteerSignupFormData {
  firstName: string;
  lastName: string;
  email: string;
  pirateName: string;
  stayingWith: string;
  stayingWithShipId?: string; // Ship ID for the volunteer's ship
  isDock: boolean;
  isLandGrant: boolean;
  landGrantCredit: string;
  landGrantCreditShipId?: string; // Ship ID for credit assignment
  checkedAutofill: boolean;
  pronouns: string;
  addToDeedsLottery: boolean;
  emailNotification: boolean;
  websiteNotification: boolean;
}

export interface Ship {
  id: string;
  name: string;
  captain: {
    id: string;
    displayName: string;
    username: string;
  };
}

// Query keys
export const publicVolunteerQueryKeys = {
  events: ["volunteerEvents"],
  event: (id: string) => ["volunteerEvent", id],
  categories: (eventId: string) => ["volunteerCategories", eventId],
  category: (id: string) => ["volunteerCategory", id],
  shifts: (categoryId: string) => ["volunteerShifts", categoryId],
  shift: (id: string) => ["volunteerShift", id],
  userShifts: ["userVolunteerShifts"],
  upcomingShifts: (userId: string) => ["userUpcomingShifts", userId],
};

/**
 * Hook to fetch all events with volunteer opportunities
 */
export function useVolunteerEvents() {
  return useQuery({
    queryKey: publicVolunteerQueryKeys.events,
    queryFn: async () => {
      return fetchClient.get<{ events: VolunteerEvent[] }>(
        "/api/volunteer/public/events",
      );
    },
  });
}

/**
 * Hook to fetch a specific event
 */
export function useVolunteerEvent(eventId: string | null) {
  return useQuery({
    queryKey: eventId ? publicVolunteerQueryKeys.event(eventId) : [],
    queryFn: async () => {
      if (!eventId) return null;
      return fetchClient.get<{ event: VolunteerEvent }>(
        `/api/volunteer/public/events/${eventId}`,
      );
    },
    enabled: !!eventId,
  });
}

/**
 * Hook to fetch categories for a specific event
 */
export function useVolunteerCategoriesByEvent(eventId: string | null) {
  return useQuery({
    queryKey: eventId ? publicVolunteerQueryKeys.categories(eventId) : [],
    queryFn: async () => {
      if (!eventId) return { categories: [] };
      return fetchClient.get<{ categories: VolunteerCategory[] }>(
        `/api/volunteer/public/events/${eventId}/categories`,
      );
    },
    enabled: !!eventId,
  });
}

/**
 * Hook to fetch a specific category
 */
export function useVolunteerCategory(categoryId: string | null) {
  return useQuery({
    queryKey: categoryId ? publicVolunteerQueryKeys.category(categoryId) : [],
    queryFn: async () => {
      if (!categoryId) return null;
      return fetchClient.get<{ category: VolunteerCategory }>(
        `/api/volunteer/public/categories/${categoryId}`,
      );
    },
    enabled: !!categoryId,
  });
}

/**
 * Hook to fetch shifts for a specific category
 */
export function useVolunteerShiftsByCategory(categoryId: string | null) {
  return useQuery({
    queryKey: categoryId ? publicVolunteerQueryKeys.shifts(categoryId) : [],
    queryFn: async () => {
      if (!categoryId) return { shifts: [] };
      return fetchClient.get<{ shifts: VolunteerShift[] }>(
        `/api/volunteer/public/categories/${categoryId}/shifts`,
      );
    },
    enabled: !!categoryId,
  });
}

/**
 * Hook to fetch a specific shift
 */
export function useVolunteerShift(shiftId: string | null) {
  return useQuery({
    queryKey: shiftId ? publicVolunteerQueryKeys.shift(shiftId) : [],
    queryFn: async () => {
      if (!shiftId) return null;
      return fetchClient.get<{ shift: VolunteerShift }>(
        `/api/volunteer/public/shifts/${shiftId}`,
      );
    },
    enabled: !!shiftId,
  });
}

/**
 * Hook to fetch user's upcoming shifts
 */
export function useUserUpcomingShifts(userId: string | null) {
  return useQuery({
    queryKey: userId ? publicVolunteerQueryKeys.upcomingShifts(userId) : [],
    queryFn: async () => {
      if (!userId) return { assignments: [] };
      try {
        return await fetchClient.get<{ assignments: VolunteerAssignment[] }>(
          `/api/volunteer/public/user/shifts`,
        );
      } catch (error: any) {
        console.error("Error fetching user shifts:", error);
        // Return empty assignments array instead of throwing to prevent UI from breaking
        return { assignments: [] };
      }
    },
    enabled: !!userId,
    // Add retry configuration to handle transient errors
    retry: 2,
    retryDelay: 1000,
    // Error will be handled by error boundary or error state in component
  });
}

/**
 * Hook to sign up for a volunteer shift
 */
export function useVolunteerSignup() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      shiftId,
      formData,
    }: {
      shiftId: string;
      formData: VolunteerSignupFormData;
    }) => {
      return fetchClient.post<{ assignment: VolunteerAssignment }>(
        `/api/volunteer/public/shifts/${shiftId}/signup`,
        formData,
      );
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: publicVolunteerQueryKeys.userShifts,
      });
    },
  });
}

/**
 * Hook to cancel a shift signup
 */
export function useCancelVolunteerShift() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (shiftId: string) => {
      return fetchClient.delete<{ success: boolean }>(
        `/api/volunteer/public/user/shifts/${shiftId}`,
      );
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: publicVolunteerQueryKeys.userShifts,
      });
    },
  });
}

// Check-in related types
export interface CheckInStatusResponse {
  assignmentId: string;
  shiftId: string;
  shiftTitle: string;
  shiftStartTime: string;
  currentStatus: string;
  checkInStatus: CheckInStatus;
}

export interface CheckInResponse {
  message: string;
  assignment: {
    id: string;
    status: string;
    shift: {
      title: string;
      startTime: string;
      category: string;
      event: string;
    };
  };
}

/**
 * Hook to get check-in status for a shift
 */
export function useVolunteerCheckInStatus(shiftId: string | null) {
  return useQuery({
    queryKey: ["volunteer", "checkin-status", shiftId],
    queryFn: async () => {
      if (!shiftId) return null;
      return fetchClient.get<CheckInStatusResponse>(
        `/api/volunteer/public/shifts/${shiftId}/checkin`,
      );
    },
    enabled: !!shiftId,
    refetchInterval: 60000, // Refetch every minute to update check-in window status
  });
}

/**
 * Hook to check in to a volunteer shift
 */
export function useVolunteerCheckIn() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (shiftId: string) => {
      return fetchClient.post<CheckInResponse>(
        `/api/volunteer/public/shifts/${shiftId}/checkin`,
        {}
      );
    },
    onSuccess: (data, shiftId) => {
      // Invalidate check-in status query
      queryClient.invalidateQueries({
        queryKey: ["volunteer", "checkin-status", shiftId],
      });
      
      // Invalidate user shifts query to update status
      queryClient.invalidateQueries({
        queryKey: publicVolunteerQueryKeys.userShifts,
      });
    },
  });
}

/**
 * Hook to search for ships
 */
export function useShipSearch(query: string) {
  return useQuery({
    queryKey: ["ships", "search", query],
    queryFn: async () => {
      if (!query.trim()) return { ships: [] };
      return fetchClient.get<{ ships: Ship[] }>(`/api/ships?search=${encodeURIComponent(query)}&limit=10`);
    },
    enabled: query.length >= 2, // Only search when there are at least 2 characters
    staleTime: 30000, // Cache for 30 seconds
  });
}

/**
 * Hook to get user's ship (if they have one)
 */
export function useUserShip() {
  return useQuery({
    queryKey: ["user", "ship"],
    queryFn: async () => {
      try {
        return await fetchClient.get<{ ship: Ship | null }>("/api/ships/my-ship");
      } catch (error: any) {
        // If user doesn't have a ship, that's ok
        if (error.status === 404) {
          return { ship: null };
        }
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
  });
}
