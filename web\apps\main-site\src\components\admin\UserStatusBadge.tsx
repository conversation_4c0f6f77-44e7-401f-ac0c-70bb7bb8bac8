"use client";

import React from "react";

type UserStatus = "active" | "pending" | "suspended" | "inactive" | "frozen";

interface UserStatusBadgeProps {
  status: UserStatus;
  size?: "sm" | "md" | "lg";
}

export const UserStatusBadge: React.FC<UserStatusBadgeProps> = ({
  status,
  size = "md",
}) => {
  const getStatusStyles = () => {
    switch (status) {
      case "active":
        return "bg-green-900 text-green-100";
      case "pending":
        return "bg-yellow-900 text-yellow-100";
      case "suspended":
        return "bg-orange-900 text-orange-100";
      case "frozen":
        return "bg-blue-900 text-blue-100";
      case "inactive":
      default:
        return "bg-red-900 text-red-100";
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case "sm":
        return "px-1.5 py-0.5 text-xs";
      case "lg":
        return "px-3 py-1.5 text-sm";
      case "md":
      default:
        return "px-2 py-1 text-xs";
    }
  };

  return (
    <span
      className={`${getStatusStyles()} ${getSizeStyles()} rounded-full font-medium inline-flex items-center justify-center`}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};
