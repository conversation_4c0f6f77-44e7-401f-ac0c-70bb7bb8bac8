import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/admin/events - List all events with pagination and filtering
export async function GET(req: NextRequest) {
  try {
    const currentUser = await getCurrentUser(req);

    // Check if user is authenticated and is an admin
    if (!currentUser || !(await userHasRole(req, "admin"))) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(req.url);

    // Parse query parameters
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const status = url.searchParams.get("status");
    const categoryId = url.searchParams.get("categoryId");
    const search = url.searchParams.get("search");

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build filter conditions
    const where: any = {};

    if (status) {
      where.status = status;
    }

    if (categoryId) {
      where.categoryId = categoryId;
    }

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } },
        { shortDescription: { contains: search } },
        { location: { contains: search } },
      ];
    }

    // Get total count for pagination
    const total = await prisma.event.count({ where });

    // Get events with pagination and filtering
    const events = await prisma.event.findMany({
      where,
      include: {
        category: true,
        createdBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
      orderBy: { startDate: "desc" },
      skip,
      take: limit,
    });

    // Calculate pagination metadata
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      events,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNextPage,
        hasPrevPage,
      },
    });
  } catch (error) {
    console.error("Error fetching events:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}

// POST /api/admin/events - Create a new event
export async function POST(req: NextRequest) {
  try {
    const currentUser = await getCurrentUser(req);

    // Check if user is authenticated and is an admin
    if (!currentUser || !(await userHasRole(req, "admin"))) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const {
      name,
      description,
      shortDescription,
      startDate,
      endDate,
      location,
      address,
      virtualLink,
      isVirtual,
      image,
      status,
      capacity,
      categoryId,
    } = await req.json();

    // Validate required fields
    if (!name || !description || !startDate || !endDate || !categoryId) {
      return NextResponse.json(
        {
          error:
            "Missing required fields: name, description, startDate, endDate, and categoryId are required",
        },
        { status: 400 },
      );
    }

    // Validate dates
    const startDateTime = new Date(startDate);
    const endDateTime = new Date(endDate);

    if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
      return NextResponse.json(
        { error: "Invalid date format for startDate or endDate" },
        { status: 400 },
      );
    }

    if (endDateTime < startDateTime) {
      return NextResponse.json(
        { error: "endDate must be after startDate" },
        { status: 400 },
      );
    }

    // Check if category exists
    const category = await prisma.eventCategory.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 400 },
      );
    }

    // Create new event
    const event = await prisma.event.create({
      data: {
        name,
        description,
        shortDescription,
        startDate: startDateTime,
        endDate: endDateTime,
        location,
        address,
        virtualLink,
        isVirtual: isVirtual || false,
        image,
        status: status || "draft",
        capacity: capacity ? parseInt(capacity) : null,
        categoryId,
        createdById: currentUser.id,
      },
    });

    return NextResponse.json(event, { status: 201 });
  } catch (error) {
    console.error("Error creating event:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
