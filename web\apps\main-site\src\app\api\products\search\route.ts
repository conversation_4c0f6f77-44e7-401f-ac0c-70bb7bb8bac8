import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/products/search - Search products
export async function GET(req: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(req.url);
    const query = searchParams.get("q") || "";
    const categoryId = searchParams.get("categoryId");
    const eventId = searchParams.get("eventId");

    // Build filter
    const filter: any = {
      isActive: true,
      OR: [
        { name: { contains: query } },
        { description: { contains: query } },
        { shortDescription: { contains: query } },
      ],
    };

    if (categoryId) filter.categoryId = categoryId;
    if (eventId) filter.eventId = eventId;

    // Get products with filtering
    const products = await prisma.product.findMany({
      where: filter,
      include: {
        category: true,
        event: {
          select: {
            id: true,
            name: true,
            startDate: true,
            endDate: true,
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    return NextResponse.json({ products });
  } catch (error) {
    console.error("Error searching products:", error);
    return NextResponse.json(
      { error: "Failed to search products" },
      { status: 500 },
    );
  }
}
