# Custom Upload System

## Overview
The application uses a dual upload system with both legacy endpoints and a modern V2 system for handling file uploads with advanced image processing.

## V2 Upload System (Current)

### Unified Endpoint
- **Route**: `/api/uploads/v2`
- **Method**: POST with FormData
- **Configuration**: Type-based routing with validation

### Upload Configuration (`uploadConfig.ts`)
Each upload type has specific constraints:

```typescript
export const uploadConfig: UploadConfigMap = {
  avatar: {
    maxSize: 1024 * 1024 * 2, // 2MB
    allowedTypes: ["image/jpeg", "image/png"],
    processImage: true,
    generateThumbnail: true,
    quality: 90,
  },
  news: {
    maxSize: 1024 * 1024 * 5, // 5MB
    allowedTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
    processImage: true,
    generateThumbnail: false,
    quality: 85,
  },
  deposit: {
    maxSize: 1024 * 1024 * 10, // 10MB
    allowedTypes: ["image/jpeg", "image/png", "application/pdf"],
    processImage: true,
    generateThumbnail: false,
    quality: 90,
  },
  // ... other types
};
```

### Request Format
```typescript
const formData = new FormData();
formData.append('file', file);
formData.append('type', 'avatar'); // Upload type
formData.append('entityId', 'user123'); // Optional entity reference
formData.append('options', JSON.stringify({ customOption: true }));
```

## Image Processing (`imageProcessingV2.ts`)

### Advanced Processing with Sharp
- **Library**: Sharp for high-performance image processing
- **Formats**: JPEG, PNG, WebP support
- **Optimization**: Type-specific settings for quality and dimensions

### Type-specific Optimization
```typescript
const optimizationSettings = {
  avatar: {
    maxWidth: 200,
    maxHeight: 200,
    quality: 90,
    format: "jpeg",
    fit: "cover",
  },
  news: {
    maxWidth: 1200,
    maxHeight: 800,
    quality: 85,
    format: "jpeg",
    fit: "inside",
  },
  product: {
    maxWidth: 800,
    maxHeight: 600,
    quality: 90,
    format: "jpeg",
    fit: "inside",
  },
  // ... other types
};
```

### Processing Features
- **Automatic Resizing**: Based on upload type
- **Quality Optimization**: Configurable compression
- **Format Conversion**: Standardizes to optimal formats
- **Dimension Tracking**: Stores original dimensions
- **Thumbnail Generation**: For applicable types

## File Storage

### Directory Structure
```
public/uploads/
├── avatars/     # User profile images
├── news/        # News article images
├── deposits/    # Bank deposit receipts
├── products/    # Product images
├── events/      # Event images
└── general/     # Fallback directory
```

### Filename Generation
```typescript
const timestamp = Date.now();
const sanitizedName = file.name.replace(/[^a-zA-Z0-9.-]/g, "-");
const filename = `${timestamp}-${uploadId.slice(0, 8)}-${sanitizedName}`;
```

### Database Tracking
All uploads are tracked in the `uploadedImage` table:

```typescript
const uploadRecord = await prisma.uploadedImage.create({
  data: {
    id: uploadId,
    filename: filename,
    path: relativePath,
    url: `/api/uploads/${directory}/${filename}`,
    mimeType: file.type,
    size: fileBuffer.length,
    entityType: uploadType,
    entityId: entityId || null,
    upload_type: uploadType,
    upload_config: uploadOptions,
    dimensions: dimensions,
  },
});
```

## Validation System

### File Type Validation
- **MIME Type Check**: Against allowed types list
- **Header Validation**: Verifies actual file format
- **Image Validation**: Checks for valid image headers

```typescript
// Basic image validation - check for common image headers
const header = buffer.toString("hex", 0, 4);
const isValidImage =
  header.startsWith("ffd8") || // JPEG
  header.startsWith("8950") || // PNG
  header.startsWith("4749") || // GIF
  header.startsWith("5249");   // WEBP
```

### Size Validation
- **Per-type Limits**: Different size limits for different upload types
- **Progressive Validation**: Client and server-side checks

## Error Handling

### Graceful Degradation
```typescript
try {
  // Process and optimize image
  const processedBuffer = await optimizeForWeb(originalBuffer, uploadType);
  fileBuffer = new Uint8Array(processedBuffer);
} catch (error) {
  console.warn("Image processing failed, using original:", error);
  // Continue with original file if processing fails
}
```

### Detailed Error Messages
- Type-specific error messages
- Size limit feedback in MB
- Validation failure details

## Legacy System

### Multiple Endpoints
- `/api/uploads/avatar` - Avatar uploads
- `/api/uploads/[type]` - Type-specific uploads
- Various component-specific uploaders

### Migration Pattern
The system maintains backward compatibility while encouraging migration to V2:

```typescript
// Legacy endpoint still functional
POST /api/uploads/avatar

// New V2 endpoint preferred
POST /api/uploads/v2
// With type: "avatar" in FormData
```

## Integration Patterns

### React Components
Multiple upload components for different use cases:
- `AvatarUploaderV2.tsx` - User avatars
- `NewsImageUploaderV2.tsx` - News articles
- `ProductImageUploader.tsx` - Product images
- `DepositReceiptUploader.tsx` - Bank deposits

### Client-side Processing
Some components include client-side image validation and preview:
- Image dimension checking
- File size validation
- Preview generation
- Progress tracking

## Important Files
- `src/app/api/uploads/v2/route.ts` - Main V2 upload endpoint
- `src/lib/imageProcessingV2.ts` - Image processing utilities
- `src/lib/uploadConfig.ts` - Upload type configurations
- `src/services/uploadServiceV2.ts` - Core upload service
- `src/types/upload.ts` - TypeScript definitions