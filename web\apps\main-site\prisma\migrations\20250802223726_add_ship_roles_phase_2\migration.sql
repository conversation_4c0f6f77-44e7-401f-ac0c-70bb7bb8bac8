-- AlterTable
ALTER TABLE `ship_members` ADD COLUMN `roleId` VARCHAR(191) NULL;

-- CreateTable
CREATE TABLE `ship_roles` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `description` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `shipId` VARCHAR(191) NOT NULL,

    INDEX `ship_roles_shipId_idx`(`shipId`),
    UNIQUE INDEX `ship_roles_shipId_name_key`(`shipId`, `name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE INDEX `ship_members_roleId_idx` ON `ship_members`(`roleId`);

-- AddForeignKey
ALTER TABLE `ship_roles` ADD CONSTRAINT `ship_roles_shipId_fkey` FOREIGN KEY (`shipId`) REFERENCES `ships`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ship_members` ADD CONSTRAINT `ship_members_roleId_fkey` FOREIGN KEY (`roleId`) REFERENCES `ship_roles`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
