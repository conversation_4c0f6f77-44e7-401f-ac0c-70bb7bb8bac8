"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getProductCategories,
  getSalesProductCategories,
  getSalesProductCategory,
  createProductCategory,
  updateProductCategory,
  deleteProductCategory,
  ProductCategory,
} from "@/services/productService";

// Hook for fetching public product categories
export const useProductCategories = (isActive?: boolean) => {
  return useQuery({
    queryKey: ["productCategories", { isActive }],
    queryFn: () => getProductCategories(isActive),
  });
};

// Hook for fetching sales manager product categories
export const useSalesProductCategories = (isActive?: boolean) => {
  return useQuery({
    queryKey: ["salesProductCategories", { isActive }],
    queryFn: () => getSalesProductCategories(isActive),
  });
};

// Hook for fetching a single product category
export const useSalesProductCategory = (id: string) => {
  return useQuery({
    queryKey: ["salesProductCategory", id],
    queryFn: () => getSalesProductCategory(id),
    enabled: !!id,
  });
};

// Hook for creating a product category
export const useCreateProductCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {
      name: string;
      description?: string;
      isActive?: boolean;
    }) => createProductCategory(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["salesProductCategories"] });
      queryClient.invalidateQueries({ queryKey: ["productCategories"] });
    },
  });
};

// Hook for updating a product category
export const useUpdateProductCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: {
        name?: string;
        description?: string;
        isActive?: boolean;
      };
    }) => updateProductCategory(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["salesProductCategory", variables.id],
      });
      queryClient.invalidateQueries({ queryKey: ["salesProductCategories"] });
      queryClient.invalidateQueries({ queryKey: ["productCategories"] });
    },
  });
};

// Hook for deleting a product category
export const useDeleteProductCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteProductCategory(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["salesProductCategories"] });
      queryClient.invalidateQueries({ queryKey: ["productCategories"] });
    },
  });
};
