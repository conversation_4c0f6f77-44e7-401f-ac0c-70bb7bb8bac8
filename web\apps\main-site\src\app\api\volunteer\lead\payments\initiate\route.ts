import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// POST /api/volunteer/lead/payments/initiate - Initiate payment for completed shifts
export async function POST(req: NextRequest) {
  try {
    const { assignmentIds, payMultiplier = 1 } = await req.json();

    // Check if user is authenticated and has lead manager role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasLeadRole = await userHasRole(req, "leadManager");
    if (!hasLeadRole) {
      return NextResponse.json(
        { error: "Unauthorized - Lead Manager role required" },
        { status: 403 },
      );
    }

    // Get the lead manager's category ID
    const leadManagerCategoryId = user.leadManagerCategoryId;
    if (!leadManagerCategoryId) {
      return NextResponse.json(
        { error: "No category assigned to this lead manager" },
        { status: 404 },
      );
    }

    // Get the category to check pay rate
    const category = await prisma.volunteerCategory.findUnique({
      where: { id: leadManagerCategoryId },
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 },
      );
    }

    const payRate = category.payRate || 0;

    // Process each assignment
    const results = await Promise.all(
      assignmentIds.map(async (assignmentId: string) => {
        try {
          // Verify the assignment exists and belongs to the lead manager's category
          const assignment = await prisma.volunteerAssignment.findUnique({
            where: { id: assignmentId },
            include: {
              shift: {
                include: {
                  category: true,
                },
              },
              user: {
                select: {
                  id: true,
                  displayName: true,
                },
              },
            },
          });

          if (!assignment) {
            return {
              assignmentId,
              success: false,
              error: "Assignment not found",
            };
          }

          // Check if the assignment belongs to the lead manager's category
          if (assignment.shift.category.id !== leadManagerCategoryId) {
            return {
              assignmentId,
              success: false,
              error: "This assignment is not in your category",
            };
          }

          // Update the assignment status to completed
          const updatedAssignment = await prisma.volunteerAssignment.update({
            where: { id: assignmentId },
            data: {
              status: "completed",
            },
          });

          // Calculate hours worked based on shift duration
          const startTime = new Date(assignment.shift.startTime);
          const endTime = new Date(assignment.shift.endTime);
          const hoursWorked =
            (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);

          // Calculate payment amount based on category pay rate and multiplier
          const paymentAmount = payRate * hoursWorked * payMultiplier;

          // Create or update volunteer hours
          const hours = await prisma.volunteerHours.upsert({
            where: { assignmentId },
            update: {
              hoursWorked,
              paymentAmount,
              verifiedById: user.id,
              verifiedAt: new Date(),
              paymentStatus: "pending",
            },
            create: {
              assignmentId,
              userId: assignment.userId,
              hoursWorked,
              paymentAmount,
              verifiedById: user.id,
              verifiedAt: new Date(),
              paymentStatus: "pending",
            },
          });

          return {
            assignmentId,
            success: true,
            hours,
            volunteer: assignment.user,
          };
        } catch (error) {
          console.error(`Error processing assignment ${assignmentId}:`, error);
          return {
            assignmentId,
            success: false,
            error: "Failed to process assignment",
          };
        }
      }),
    );

    // Count successful and failed operations
    const successful = results.filter((result) => result.success).length;
    const failed = results.filter((result) => !result.success).length;

    return NextResponse.json({
      results,
      summary: {
        total: results.length,
        successful,
        failed,
      },
      message: `Successfully initiated ${successful} payments, ${failed} failed`,
    });
  } catch (error) {
    console.error("Error initiating payments:", error);
    return NextResponse.json(
      { error: "Failed to initiate payments" },
      { status: 500 },
    );
  }
}
