/**
 * @file c:\Users\<USER>\projects\Bank-of-styx-website\web\apps\api\auth\login\route.ts
 * @summary Defines the API route handler for user login (`/api/auth/login`). It handles POST requests, validates email and password, finds the user by email, verifies the password hash, generates a JWT token upon successful authentication, and returns the user's details along with the token. Also handles OPTIONS requests for CORS preflight.
 */
import { NextRequest, NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import prisma from "@/lib/prisma";
import jwt from "jsonwebtoken";
// Middleware import removed - using inline CORS handling

const JWT_SECRET = process.env.JWT_SECRET || "your-development-secret-key";

/**
 * Handles OPTIONS requests for the login endpoint.
 * @param {NextRequest} req - The incoming request object.
 * @returns {Promise<NextResponse | null>} A response suitable for CORS preflight.
 */
export const OPTIONS = async (req: NextRequest) => {
  // Handle OPTIONS request for CORS preflight
  const response = new NextResponse(null, { status: 204 });

  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT, DELETE, OPTIONS",
  );
  response.headers.set(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization",
  );

  return response;
};

/**
 * Handles POST requests for user login.
 * @param {NextRequest} req - The incoming request object, expected to contain email and password in the JSON body.
 * @returns {Promise<NextResponse>} A JSON response containing the user object and a JWT token on successful login, or an error response.
 */
export const POST = async (req: NextRequest) => {
  try {
    const { email, password } = await req.json();

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { error: "Email and password are required" },
        { status: 400 },
      );
    }

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      return NextResponse.json(
        { error: "Invalid credentials" },
        { status: 401 },
      );
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);

    if (!isPasswordValid) {
      return NextResponse.json(
        { error: "Invalid credentials" },
        { status: 401 },
      );
    }

    // Create JWT token
    const token = jwt.sign({ id: user.id, email: user.email }, JWT_SECRET, {
      expiresIn: "7d",
    });

    // Return user data and token
    return NextResponse.json({
      user: {
        id: user.id,
        username: user.username,
        displayName: user.displayName,
        email: user.email,
        avatar: user.avatar,
        balance: user.balance,
        isEmailVerified: user.isEmailVerified,
        preferences: {
          defaultView: user.defaultView,
          notifications: {
            transfers: user.notifyTransfers,
            deposits: user.notifyDeposits,
            withdrawals: user.notifyWithdrawals,
            newsAndEvents: user.notifyNewsEvents,
          },
        },
        connectedAccounts: {
          discord: user.discordConnected,
          discordId: user.discordId,
          facebook: user.facebookConnected,
          facebookId: user.facebookId,
        },
        merchant: {
          status: user.merchantStatus,
          merchantId: user.merchantId,
          slug: user.merchantSlug,
        },
        auctions: {
          hasCreated: user.hasCreatedAuctions,
          auctionCount: user.auctionCount,
        },
        roles: {
          admin: user.isAdmin,
          editor: user.isEditor,
          banker: user.isBanker,
          chatModerator: user.isChatModerator,
          volunteerCoordinator: user.isVolunteerCoordinator,
          leadManager: user.isLeadManager,
        },
      },
      token,
    });
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
