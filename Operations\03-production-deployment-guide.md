# Production Deployment Guide

## Overview

This guide provides step-by-step instructions for safely deploying changes to the production Bank of Styx website. This process is designed to minimize downtime and risk while ensuring all changes are properly tested and documented.

## 🚨 CRITICAL DEPLOYMENT RULES

### Before You Start
- **NEVER deploy on Fridays** (unless emergency)
- **NEVER deploy during peak hours** (typically 6 PM - 10 PM)
- **ALWAYS have someone available** to help if things go wrong
- **ALWAYS test in staging first**
- **ALWAYS have a rollback plan**

### Deployment Windows
- **Preferred**: Tuesday-Thursday, 10 AM - 2 PM
- **Acceptable**: Monday-Wednesday, 8 AM - 4 PM
- **Emergency Only**: Evenings, weekends, holidays

## Pre-Deployment Checklist

### 1. Code Readiness (Complete 24 hours before deployment)
- [ ] All code changes committed and pushed
- [ ] Code review completed and approved
- [ ] All tests passing in development
- [ ] Documentation updated
- [ ] No known critical bugs

### 2. Environment Preparation (Complete 2 hours before deployment)
- [ ] Staging environment updated and tested
- [ ] Production backup completed
- [ ] Database migration scripts prepared (if needed)
- [ ] Environment variables updated (if needed)
- [ ] SSL certificates valid and not expiring soon

### 3. Team Coordination (Complete 1 hour before deployment)
- [ ] Deployment time communicated to stakeholders
- [ ] Technical support person identified and available
- [ ] Rollback person identified and briefed
- [ ] User communication prepared (if needed)
- [ ] Monitoring alerts configured

### 4. Final Verification (Complete 30 minutes before deployment)
- [ ] Staging environment final test completed
- [ ] Production server health check passed
- [ ] Database connectivity verified
- [ ] Backup integrity verified
- [ ] Deployment scripts tested

## Step-by-Step Deployment Process

### Phase 1: Pre-Deployment Setup (15 minutes)

#### Step 1.1: Create Deployment Backup
```bash
# Navigate to project directory
cd /path/to/bank-of-styx

# Create timestamped backup
BACKUP_NAME="pre-deployment-$(date +%Y%m%d_%H%M%S)"
mkdir -p /backups/deployments/$BACKUP_NAME

# Backup database
mysqldump -u root -p bank_of_styx > /backups/deployments/$BACKUP_NAME/database.sql

# Backup application files
tar -czf /backups/deployments/$BACKUP_NAME/application.tar.gz .

# Backup environment files
cp .env.production /backups/deployments/$BACKUP_NAME/

echo "Backup created: $BACKUP_NAME"
```

#### Step 1.2: Verify Current System Status
```bash
# Check application status
pm2 status bank-of-styx

# Check database connectivity
mysql -u root -p -e "SELECT 1 FROM User LIMIT 1;"

# Check disk space
df -h

# Check memory usage
free -h

# Check system load
uptime
```

#### Step 1.3: Prepare Deployment Environment
```bash
# Ensure we're on the correct branch
git checkout main
git pull origin main

# Verify the commit we're deploying
git log --oneline -5

# Check for any uncommitted changes
git status
```

### Phase 2: Application Deployment (10-15 minutes)

#### Step 2.1: Stop Application Services
```bash
# Stop the application gracefully
pm2 stop bank-of-styx

# Verify it's stopped
pm2 status

# Optional: Stop nginx if needed for maintenance
# systemctl stop nginx
```

#### Step 2.2: Update Application Code
```bash
# Pull latest changes
git pull origin main

# Install any new dependencies
cd web
pnpm install

# Build the application
pnpm build

# Verify build completed successfully
ls -la web/apps/main-site/.next/
```

#### Step 2.3: Database Migrations (if needed)
```bash
# Navigate to main application
cd web/apps/main-site

# Check migration status
npx prisma migrate status

# Apply migrations (if any)
npx prisma migrate deploy

# Generate Prisma client
npx prisma generate

# Verify database schema
npx prisma db pull
```

#### Step 2.4: Update Environment Configuration
```bash
# Copy production environment file
cp .env.production .env.local

# Verify environment variables
cat .env.local | grep -v PASSWORD | grep -v SECRET
```

### Phase 3: Service Restart and Verification (5-10 minutes)

#### Step 3.1: Start Application Services
```bash
# Start the application
pm2 start bank-of-styx

# Verify it started successfully
pm2 status

# Check application logs
pm2 logs bank-of-styx --lines 20

# Start nginx if it was stopped
# systemctl start nginx
```

#### Step 3.2: Health Check Verification
```bash
# Wait for application to fully start
sleep 30

# Check if application responds
curl -I http://localhost:3000

# Check database connectivity through app
curl http://localhost:3000/api/health

# Check specific endpoints
curl http://localhost:3000/api/auth/me
```

### Phase 4: Post-Deployment Verification (15-20 minutes)

#### Step 4.1: Functional Testing
1. **Authentication System**
   - [ ] Login page loads
   - [ ] User can log in
   - [ ] User dashboard accessible
   - [ ] Logout works

2. **Banking System**
   - [ ] Account balance displays
   - [ ] Transaction history loads
   - [ ] Pay code generation works
   - [ ] Real-time updates function

3. **Core Features**
   - [ ] News system loads
   - [ ] Events page accessible
   - [ ] Shop functionality works
   - [ ] Admin dashboard accessible

4. **Performance Check**
   - [ ] Page load times under 3 seconds
   - [ ] No JavaScript errors in console
   - [ ] Mobile responsiveness works
   - [ ] SSL certificate valid

#### Step 4.2: System Monitoring
```bash
# Check system resources
top
htop

# Monitor application logs
pm2 logs bank-of-styx --lines 50

# Check for errors
grep -i error /var/log/nginx/error.log | tail -10

# Monitor database performance
mysql -u root -p -e "SHOW PROCESSLIST;"
```

#### Step 4.3: User Communication
```bash
# If deployment affects users, send notification
# Example: Post in Discord, send email, update status page

# Template message:
"The Bank of Styx website has been updated with new features and improvements. 
If you experience any issues, please contact support."
```

## Rollback Procedures

### When to Rollback
- Application won't start
- Critical functionality broken
- Database errors
- Performance significantly degraded
- Security issues discovered

### Quick Rollback Process (5-10 minutes)

#### Step 1: Stop Current Application
```bash
pm2 stop bank-of-styx
```

#### Step 2: Restore Previous Version
```bash
# Find the backup to restore
ls -la /backups/deployments/

# Set backup name
BACKUP_NAME="pre-deployment-YYYYMMDD_HHMMSS"

# Restore application files
tar -xzf /backups/deployments/$BACKUP_NAME/application.tar.gz

# Restore environment file
cp /backups/deployments/$BACKUP_NAME/.env.production .env.local
```

#### Step 3: Restore Database (if needed)
```bash
# Only if database changes were made
mysql -u root -p bank_of_styx < /backups/deployments/$BACKUP_NAME/database.sql
```

#### Step 4: Restart Application
```bash
pm2 start bank-of-styx
pm2 status
```

#### Step 5: Verify Rollback
```bash
# Test critical functionality
curl -I http://localhost:3000
curl http://localhost:3000/api/health
```

### Advanced Rollback (if quick rollback fails)

#### Git-based Rollback
```bash
# Find the last known good commit
git log --oneline -10

# Reset to previous commit
git reset --hard [previous-commit-hash]

# Rebuild application
cd web
pnpm install
pnpm build

# Restart services
pm2 restart bank-of-styx
```

## Monitoring and Alerting Setup

### Application Monitoring
```bash
# Set up PM2 monitoring
pm2 install pm2-server-monit

# Configure log rotation
pm2 install pm2-logrotate

# Set up process monitoring
pm2 startup
pm2 save
```

### Database Monitoring
```sql
-- Enable slow query log
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

-- Monitor connections
SHOW STATUS LIKE 'Threads_connected';
```

### System Monitoring
```bash
# Set up disk space monitoring
echo "df -h | grep -E '(8[0-9]|9[0-9])%' && echo 'Disk space warning'" | crontab -e

# Set up memory monitoring
echo "free | grep Mem | awk '{print ($3/$2) * 100.0}' | grep -E '^[8-9][0-9]' && echo 'Memory warning'" | crontab -e
```

## Deployment Troubleshooting

### Common Issues and Solutions

#### Application Won't Start
```bash
# Check logs for errors
pm2 logs bank-of-styx

# Common fixes:
# 1. Check environment variables
cat .env.local

# 2. Rebuild application
cd web && pnpm build

# 3. Clear cache
rm -rf web/apps/main-site/.next/
pnpm build
```

#### Database Connection Issues
```bash
# Test database connectivity
mysql -u root -p -e "SELECT 1;"

# Check database service
systemctl status mysql

# Restart database if needed
systemctl restart mysql
```

#### Performance Issues
```bash
# Check system resources
htop
iotop

# Check application performance
pm2 monit

# Restart application
pm2 restart bank-of-styx
```

## Post-Deployment Tasks

### Immediate (within 1 hour)
- [ ] Monitor error logs
- [ ] Check user feedback channels
- [ ] Verify all critical functions
- [ ] Update deployment log

### Within 24 hours
- [ ] Review performance metrics
- [ ] Check backup completion
- [ ] Update documentation
- [ ] Communicate success to stakeholders

### Within 1 week
- [ ] Analyze user adoption of new features
- [ ] Review any issues that arose
- [ ] Plan next deployment
- [ ] Update procedures based on lessons learned

## Deployment Log Template

```
Deployment Date: [YYYY-MM-DD]
Deployment Time: [HH:MM - HH:MM]
Deployed By: [Name]
Version/Commit: [git commit hash]

Changes Deployed:
- [List of features/fixes]

Pre-deployment Checklist: [✓/✗]
Backup Created: [✓/✗]
Staging Tested: [✓/✗]
Rollback Plan: [✓/✗]

Deployment Status: [Success/Failed/Rolled Back]
Issues Encountered: [None/List issues]
Rollback Required: [Yes/No]

Post-deployment Verification:
- Authentication: [✓/✗]
- Banking System: [✓/✗]
- Core Features: [✓/✗]
- Performance: [✓/✗]

Notes: [Any additional notes]
```

## Advanced Monitoring and Health Checks

### Comprehensive Health Check Script

#### Create Health Check Script
```bash
#!/bin/bash
# health-check.sh - Comprehensive system health check

LOG_FILE="/var/log/health-check.log"
ALERT_WEBHOOK="https://discord.com/api/webhooks/YOUR_WEBHOOK"

echo "$(date): Starting health check" >> $LOG_FILE

# Function to send alerts
send_alert() {
    local message="$1"
    curl -X POST "$ALERT_WEBHOOK" \
      -H "Content-Type: application/json" \
      -d "{\"content\": \"🚨 ALERT: $message\"}" \
      2>/dev/null
}

# Check website accessibility
echo "Checking website accessibility..." >> $LOG_FILE
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000)
if [ "$HTTP_STATUS" != "200" ]; then
    echo "$(date): ERROR: Website not accessible (HTTP $HTTP_STATUS)" >> $LOG_FILE
    send_alert "Website not accessible - HTTP $HTTP_STATUS"
    exit 1
fi

# Check API endpoints
echo "Checking API endpoints..." >> $LOG_FILE
API_HEALTH=$(curl -s http://localhost:3000/api/health | jq -r '.status' 2>/dev/null)
if [ "$API_HEALTH" != "ok" ]; then
    echo "$(date): ERROR: API health check failed" >> $LOG_FILE
    send_alert "API health check failed"
fi

# Check database connectivity
echo "Checking database connectivity..." >> $LOG_FILE
DB_CHECK=$(mysql -u root -p$DB_PASSWORD -e "SELECT 1" 2>/dev/null)
if [ $? -ne 0 ]; then
    echo "$(date): ERROR: Database connection failed" >> $LOG_FILE
    send_alert "Database connection failed"
    exit 1
fi

# Check application process
echo "Checking application process..." >> $LOG_FILE
PM2_STATUS=$(pm2 jlist | jq -r '.[0].pm2_env.status' 2>/dev/null)
if [ "$PM2_STATUS" != "online" ]; then
    echo "$(date): ERROR: Application not running (Status: $PM2_STATUS)" >> $LOG_FILE
    send_alert "Application not running - Status: $PM2_STATUS"
    exit 1
fi

# Check disk space
echo "Checking disk space..." >> $LOG_FILE
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 80 ]; then
    echo "$(date): WARNING: Disk usage high ($DISK_USAGE%)" >> $LOG_FILE
    send_alert "Disk usage high: $DISK_USAGE%"
fi

# Check memory usage
echo "Checking memory usage..." >> $LOG_FILE
MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
if [ "$MEMORY_USAGE" -gt 80 ]; then
    echo "$(date): WARNING: Memory usage high ($MEMORY_USAGE%)" >> $LOG_FILE
    send_alert "Memory usage high: $MEMORY_USAGE%"
fi

# Check SSL certificate expiration
echo "Checking SSL certificate..." >> $LOG_FILE
SSL_EXPIRY=$(echo | openssl s_client -servername bankofstyx.com -connect bankofstyx.com:443 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
SSL_EXPIRY_EPOCH=$(date -d "$SSL_EXPIRY" +%s)
CURRENT_EPOCH=$(date +%s)
DAYS_UNTIL_EXPIRY=$(( (SSL_EXPIRY_EPOCH - CURRENT_EPOCH) / 86400 ))

if [ "$DAYS_UNTIL_EXPIRY" -lt 30 ]; then
    echo "$(date): WARNING: SSL certificate expires in $DAYS_UNTIL_EXPIRY days" >> $LOG_FILE
    send_alert "SSL certificate expires in $DAYS_UNTIL_EXPIRY days"
fi

echo "$(date): Health check completed successfully" >> $LOG_FILE
```

### Performance Monitoring

#### Application Performance Monitoring
```bash
#!/bin/bash
# performance-monitor.sh - Monitor application performance

LOG_FILE="/var/log/performance.log"

# Monitor response times
echo "$(date): Monitoring response times" >> $LOG_FILE

# Test homepage response time
HOMEPAGE_TIME=$(curl -o /dev/null -s -w "%{time_total}" http://localhost:3000)
echo "$(date): Homepage response time: ${HOMEPAGE_TIME}s" >> $LOG_FILE

# Test API response time
API_TIME=$(curl -o /dev/null -s -w "%{time_total}" http://localhost:3000/api/health)
echo "$(date): API response time: ${API_TIME}s" >> $LOG_FILE

# Test database query time
DB_START=$(date +%s.%N)
mysql -u root -p$DB_PASSWORD -e "SELECT COUNT(*) FROM User;" > /dev/null 2>&1
DB_END=$(date +%s.%N)
DB_TIME=$(echo "$DB_END - $DB_START" | bc)
echo "$(date): Database query time: ${DB_TIME}s" >> $LOG_FILE

# Alert if response times are too high
if (( $(echo "$HOMEPAGE_TIME > 5.0" | bc -l) )); then
    curl -X POST "$ALERT_WEBHOOK" \
      -H "Content-Type: application/json" \
      -d "{\"content\": \"⚠️ WARNING: Homepage slow response time: ${HOMEPAGE_TIME}s\"}"
fi
```

#### Resource Usage Monitoring
```bash
#!/bin/bash
# resource-monitor.sh - Monitor system resources

LOG_FILE="/var/log/resources.log"

# CPU usage
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
echo "$(date): CPU Usage: $CPU_USAGE%" >> $LOG_FILE

# Memory usage
MEMORY_INFO=$(free -m | grep Mem)
MEMORY_TOTAL=$(echo $MEMORY_INFO | awk '{print $2}')
MEMORY_USED=$(echo $MEMORY_INFO | awk '{print $3}')
MEMORY_PERCENT=$(echo "scale=1; $MEMORY_USED * 100 / $MEMORY_TOTAL" | bc)
echo "$(date): Memory Usage: $MEMORY_PERCENT% ($MEMORY_USED/$MEMORY_TOTAL MB)" >> $LOG_FILE

# Disk I/O
DISK_IO=$(iostat -x 1 1 | grep -E '^[a-z]' | awk '{print $1 ": " $10 "%"}')
echo "$(date): Disk I/O: $DISK_IO" >> $LOG_FILE

# Network connections
CONNECTIONS=$(netstat -an | grep :3000 | wc -l)
echo "$(date): Active connections: $CONNECTIONS" >> $LOG_FILE

# Application-specific metrics
PM2_MEMORY=$(pm2 jlist | jq -r '.[0].monit.memory' 2>/dev/null)
PM2_CPU=$(pm2 jlist | jq -r '.[0].monit.cpu' 2>/dev/null)
echo "$(date): App Memory: $PM2_MEMORY bytes, App CPU: $PM2_CPU%" >> $LOG_FILE
```

### Automated Deployment Pipeline

#### CI/CD Pipeline Configuration
```yaml
# .github/workflows/production-deploy.yml
name: Production Deployment

on:
  push:
    branches: [main]
    tags: ['v*']

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: cd web && pnpm install --frozen-lockfile

      - name: Run linting
        run: cd web && pnpm lint

      - name: Run type checking
        run: cd web && pnpm type-check

      - name: Run unit tests
        run: cd web && pnpm test

      - name: Build application
        run: cd web && pnpm build

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Run security audit
        run: cd web && npm audit --audit-level moderate

      - name: Check for vulnerabilities
        run: cd web && npx audit-ci --moderate

  deploy:
    name: Deploy to Production
    needs: [test, security-scan]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
      - name: Deploy to server
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            cd /path/to/bank-of-styx
            ./deploy.sh

      - name: Verify deployment
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            sleep 30
            curl -f http://localhost:3000/api/health || exit 1

      - name: Notify success
        if: success()
        run: |
          curl -X POST "${{ secrets.DISCORD_WEBHOOK }}" \
            -H "Content-Type: application/json" \
            -d '{"content": "✅ Production deployment successful!"}'

      - name: Notify failure
        if: failure()
        run: |
          curl -X POST "${{ secrets.DISCORD_WEBHOOK }}" \
            -H "Content-Type: application/json" \
            -d '{"content": "❌ Production deployment failed!"}'
```

### Database Migration Safety

#### Safe Migration Procedures
```bash
#!/bin/bash
# safe-migrate.sh - Safe database migration script

set -e

DB_NAME="bank_of_styx"
BACKUP_DIR="/backups/migrations"
DATE=$(date +%Y%m%d_%H%M%S)

echo "Starting safe database migration..."

# 1. Create pre-migration backup
echo "Creating pre-migration backup..."
mkdir -p $BACKUP_DIR
mysqldump -u root -p$DB_PASSWORD $DB_NAME > $BACKUP_DIR/pre_migration_$DATE.sql

# 2. Verify backup integrity
echo "Verifying backup integrity..."
mysql -u root -p$DB_PASSWORD -e "CREATE DATABASE IF NOT EXISTS migration_test_$DATE;"
mysql -u root -p$DB_PASSWORD migration_test_$DATE < $BACKUP_DIR/pre_migration_$DATE.sql

# Count records in original and backup
ORIGINAL_COUNT=$(mysql -u root -p$DB_PASSWORD $DB_NAME -e "SELECT COUNT(*) FROM User;" | tail -1)
BACKUP_COUNT=$(mysql -u root -p$DB_PASSWORD migration_test_$DATE -e "SELECT COUNT(*) FROM User;" | tail -1)

if [ "$ORIGINAL_COUNT" != "$BACKUP_COUNT" ]; then
    echo "ERROR: Backup verification failed!"
    mysql -u root -p$DB_PASSWORD -e "DROP DATABASE migration_test_$DATE;"
    exit 1
fi

# Clean up test database
mysql -u root -p$DB_PASSWORD -e "DROP DATABASE migration_test_$DATE;"

# 3. Run migrations
echo "Running database migrations..."
cd web/apps/main-site
npx prisma migrate deploy

# 4. Verify migration success
echo "Verifying migration success..."
npx prisma migrate status

# 5. Test application connectivity
echo "Testing application connectivity..."
node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
prisma.user.count().then(count => {
  console.log('User count:', count);
  process.exit(0);
}).catch(err => {
  console.error('Database test failed:', err);
  process.exit(1);
});
"

echo "Migration completed successfully!"
```

### Load Testing and Capacity Planning

#### Load Testing Script
```bash
#!/bin/bash
# load-test.sh - Load testing script

echo "Starting load test..."

# Install artillery if not present
if ! command -v artillery &> /dev/null; then
    npm install -g artillery
fi

# Create load test configuration
cat > load-test.yml << EOF
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 5
      name: "Warm up"
    - duration: 120
      arrivalRate: 10
      name: "Ramp up load"
    - duration: 300
      arrivalRate: 20
      name: "Sustained load"
  processor: "./load-test-functions.js"

scenarios:
  - name: "Homepage and login flow"
    weight: 70
    flow:
      - get:
          url: "/"
      - get:
          url: "/auth/login"
      - post:
          url: "/api/auth/login"
          json:
            email: "<EMAIL>"
            password: "testpassword"

  - name: "Banking operations"
    weight: 20
    flow:
      - get:
          url: "/bank/dashboard"
      - get:
          url: "/api/bank/balance"
      - get:
          url: "/api/bank/transactions"

  - name: "News and events"
    weight: 10
    flow:
      - get:
          url: "/news"
      - get:
          url: "/events"
EOF

# Run load test
artillery run load-test.yml --output load-test-report.json

# Generate HTML report
artillery report load-test-report.json --output load-test-report.html

echo "Load test completed. Report saved to load-test-report.html"

# Check for performance issues
RESPONSE_TIME=$(cat load-test-report.json | jq '.aggregate.latency.p95')
ERROR_RATE=$(cat load-test-report.json | jq '.aggregate.counters["errors.ECONNREFUSED"] // 0')

if (( $(echo "$RESPONSE_TIME > 5000" | bc -l) )); then
    echo "WARNING: High response time detected: ${RESPONSE_TIME}ms"
fi

if [ "$ERROR_RATE" -gt 0 ]; then
    echo "WARNING: Connection errors detected: $ERROR_RATE"
fi
```

---

*Remember: A successful deployment is one where everything works exactly as it did before, plus the new features work perfectly.*
