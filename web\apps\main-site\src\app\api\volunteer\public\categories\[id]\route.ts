import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/volunteer/public/categories/[id] - Get a specific category
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id: categoryId } = params;

    // Check if user is authenticated
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    // Get the category
    const category = await prisma.volunteerCategory.findUnique({
      where: { id: categoryId },
      select: {
        id: true,
        name: true,
        description: true,
        payRate: true,
        eventId: true,
        leadManagerId: true,
        createdAt: true,
        updatedAt: true,
        // Include event to check if it's published
        event: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
        // Include shifts to calculate stats
        shifts: {
          select: {
            id: true,
            maxVolunteers: true,
            assignments: {
              select: {
                id: true,
                status: true,
              },
            },
          },
        },
      },
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 },
      );
    }

    // Check if event is published
    if (category.event.status !== "published") {
      return NextResponse.json(
        { error: "Category not available" },
        { status: 403 },
      );
    }

    // Calculate stats
    const totalShifts = category.shifts.length;
    const totalSignups = category.shifts.reduce(
      (sum, shift) => sum + shift.assignments.length,
      0,
    );
    const totalSlots = category.shifts.reduce(
      (sum, shift) => sum + shift.maxVolunteers,
      0,
    );
    const availableSlots = totalSlots - totalSignups;

    // Remove shifts and event from the response
    const { shifts, event, ...categoryData } = category;

    return NextResponse.json({
      category: {
        ...categoryData,
        stats: {
          totalShifts,
          totalSignups,
          availableSlots,
        },
      },
    });
  } catch (error) {
    console.error("Error fetching category:", error);
    return NextResponse.json(
      { error: "Failed to fetch category" },
      { status: 500 },
    );
  }
}
