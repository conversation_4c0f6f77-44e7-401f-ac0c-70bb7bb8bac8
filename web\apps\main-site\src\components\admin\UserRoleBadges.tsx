"use client";

import React from "react";

import { UserRoles } from "@/types/user";

interface UserRoleBadgesProps {
  roles: UserRoles;
  size?: "sm" | "md" | "lg";
}

export const UserRoleBadges: React.FC<UserRoleBadgesProps> = ({
  roles,
  size = "md",
}) => {
  const getSizeStyles = () => {
    switch (size) {
      case "sm":
        return "px-1.5 py-0.5 text-xs";
      case "lg":
        return "px-3 py-1.5 text-sm";
      case "md":
      default:
        return "px-2 py-1 text-xs";
    }
  };

  return (
    <div className="flex flex-wrap gap-2">
      {roles.admin && (
        <span
          className={`${getSizeStyles()} rounded-full bg-red-900 text-red-100`}
        >
          Admin
        </span>
      )}
      {roles.editor && (
        <span
          className={`${getSizeStyles()} rounded-full bg-blue-900 text-blue-100`}
        >
          Editor
        </span>
      )}
      {roles.banker && (
        <span
          className={`${getSizeStyles()} rounded-full bg-green-900 text-green-100`}
        >
          Banker
        </span>
      )}
      {roles.chatModerator && (
        <span
          className={`${getSizeStyles()} rounded-full bg-purple-900 text-purple-100`}
        >
          Chat Mod
        </span>
      )}
      {roles.volunteerCoordinator && (
        <span
          className={`${getSizeStyles()} rounded-full bg-teal-900 text-teal-100`}
        >
          Vol. Coordinator
        </span>
      )}
      {roles.leadManager && (
        <span
          className={`${getSizeStyles()} rounded-full bg-indigo-900 text-indigo-100`}
        >
          Category Lead
        </span>
      )}
      {roles.salesManager && (
        <span
          className={`${getSizeStyles()} rounded-full bg-amber-900 text-amber-100`}
        >
          Sales Manager
        </span>
      )}
      {roles.landSteward && (
        <span
          className={`${getSizeStyles()} rounded-full bg-orange-900 text-orange-100`}
        >
          Land Steward
        </span>
      )}
      {!roles.admin &&
        !roles.editor &&
        !roles.banker &&
        !roles.chatModerator &&
        !roles.volunteerCoordinator &&
        !roles.leadManager &&
        !roles.salesManager &&
        !roles.landSteward && (
          <span
            className={`${getSizeStyles()} rounded-full bg-gray-700 text-gray-300`}
          >
            No Roles
          </span>
        )}
    </div>
  );
};
