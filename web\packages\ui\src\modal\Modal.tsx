import React, { useEffect, useRef } from "react";
import "./Modal.css";

export interface ModalProps {
  /**
   * Is the modal open
   */
  isOpen: boolean;
  /**
   * Function to close the modal
   */
  onClose: () => void;
  /**
   * Modal title
   */
  title?: string;
  /**
   * Modal children
   */
  children: React.ReactNode;
  /**
   * Modal footer
   */
  footer?: React.ReactNode;
  /**
   * Modal size
   */
  size?: "sm" | "md" | "lg" | "xl" | "full";
  /**
   * Close on click outside
   */
  closeOnClickOutside?: boolean;
  /**
   * Close on escape key
   */
  closeOnEsc?: boolean;
}

export const Modal = ({
  isOpen,
  onClose,
  title,
  children,
  footer,
  size = "md",
  closeOnClickOutside = true,
  closeOnEsc = true,
}: ModalProps) => {
  const modalRef = useRef<HTMLDivElement>(null);

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        closeOnClickOutside &&
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose, closeOnClickOutside]);

  // Handle escape key
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (closeOnEsc && event.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscKey);
    }

    return () => {
      document.removeEventListener("keydown", handleEscKey);
    };
  }, [isOpen, onClose, closeOnEsc]);

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }

    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  // Apply dark mode styles immediately when modal opens
  useEffect(() => {
    if (isOpen && modalRef.current) {
      // Ensure dark mode styles are applied immediately
      modalRef.current.classList.add("modal-dark");

      // Find and style the header, body, and footer
      const header = modalRef.current.querySelector("div:first-child");
      const body = modalRef.current.querySelector("div:nth-child(2)");
      const footer = modalRef.current.querySelector("div:last-child");

      if (header) header.classList.add("modal-dark-header");
      if (body) body.classList.add("modal-dark-body");
      if (footer) footer.classList.add("modal-dark-footer");
    }
  }, [isOpen]);

  if (!isOpen) {
    return null;
  }

  // Size classes
  const sizeClasses = {
    sm: "max-w-sm",
    md: "max-w-md",
    lg: "max-w-lg",
    xl: "max-w-xl",
    full: "max-w-full",
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 transition-opacity bg-black bg-opacity-80"
          aria-hidden="true"
        ></div>

        {/* Center modal */}
        <span
          className="hidden sm:inline-block sm:align-middle sm:h-screen"
          aria-hidden="true"
        >
          &#8203;
        </span>

        {/* Modal panel */}
        <div
          ref={modalRef}
          className={`
            inline-block align-bottom rounded-lg text-left
            overflow-hidden shadow-xl transform transition-all
            sm:my-8 sm:align-middle sm:w-full ${sizeClasses[size]} sm:p-0
            bg-secondary modal-dark
          `}
          style={{
            backgroundColor: "var(--color-secondary)",
          }} /* Inline style to prevent flash */
          role="dialog"
          aria-modal="true"
          aria-labelledby="modal-title"
        >
          {/* Modal header */}
          {title && (
            <div
              className="px-4 py-3 border-b border-gray-600 bg-secondary modal-dark-header"
              style={{
                backgroundColor: "var(--color-secondary)",
              }} /* Inline style to prevent flash */
            >
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium" id="modal-title">
                  {title}
                </h3>
                <button
                  type="button"
                  className="text-gray-400 hover:text-white focus:outline-none"
                  onClick={onClose}
                  aria-label="Close"
                >
                  <svg
                    className="w-6 h-6"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            </div>
          )}

          {/* Modal body */}
          <div
            className="px-4 py-3 bg-secondary-light modal-dark-body"
            style={{
              backgroundColor: "var(--color-secondary-light)",
            }} /* Inline style to prevent flash */
          >
            {children}
          </div>

          {/* Modal footer */}
          {footer && (
            <div
              className="px-4 py-3 bg-secondary border-t border-gray-600 sm:px-6 sm:flex sm:flex-row-reverse modal-dark-footer"
              style={{
                backgroundColor: "var(--color-secondary)",
              }} /* Inline style to prevent flash */
            >
              {footer}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

Modal.displayName = "Modal";
