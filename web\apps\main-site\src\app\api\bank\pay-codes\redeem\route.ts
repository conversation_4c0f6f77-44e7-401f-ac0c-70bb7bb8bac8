import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../../lib/prisma";
import { verifyToken } from "../../../../../lib/auth";
import { createNotification } from "../../../../../lib/notifications";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export const POST = async (req: NextRequest) => {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Get request body
    const { code, trialRun = false } = await req.json();

    // Validate input
    if (!code) {
      return NextResponse.json({ error: "Code is required" }, { status: 400 });
    }

    // Find the pay code
    const payCode = await prisma.payCode.findUnique({
      where: { code },
      include: {
        createdBy: true,
      },
    });

    if (!payCode) {
      return NextResponse.json(
        { error: "Pay code not found" },
        { status: 404 },
      );
    }

    // Check if the code is active
    if (payCode.status !== "active") {
      return NextResponse.json(
        { error: `Pay code is ${payCode.status}` },
        { status: 400 },
      );
    }

    // Check if the code has expired
    if (new Date() > payCode.expiresAt) {
      // Update the code status to expired
      await prisma.payCode.update({
        where: { id: payCode.id },
        data: { status: "expired" },
      });

      return NextResponse.json(
        { error: "Pay code has expired" },
        { status: 400 },
      );
    }

    // Check if the code has reached its maximum uses
    if (payCode.maxUses && payCode.uses >= payCode.maxUses) {
      // Update the code status to expired
      await prisma.payCode.update({
        where: { id: payCode.id },
        data: { status: "expired" },
      });

      return NextResponse.json(
        { error: "Pay code has reached its maximum uses" },
        { status: 400 },
      );
    }

    // Check if the user is trying to redeem their own code
    if (payCode.createdById === userId) {
      return NextResponse.json(
        { error: "You cannot redeem your own pay code" },
        { status: 400 },
      );
    }

    // If this is a trial run, just return the details without processing the redemption
    if (trialRun) {
      // Get user details
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          username: true,
          displayName: true,
          avatar: true,
          balance: true,
        },
      });

      if (!user) {
        return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Return the validation details with more comprehensive pay code information
      return NextResponse.json({
        valid: true,
        trialRun: true,
        payCode: {
          id: payCode.id,
          code: payCode.code,
          amount: payCode.amount,
          status: payCode.status,
          createdAt: payCode.createdAt.toISOString(),
          expiresAt: payCode.expiresAt.toISOString(),
          uses: payCode.uses,
          maxUses: payCode.maxUses,
          createdBy: {
            id: payCode.createdBy.id,
            username: payCode.createdBy.username,
            displayName: payCode.createdBy.displayName,
            avatar: payCode.createdBy.avatar,
          },
        },
        user: {
          id: user.id,
          username: user.username,
          displayName: user.displayName,
          avatar: user.avatar,
          balance: user.balance,
          newBalance: user.balance - payCode.amount, // Show what the balance would be after redemption (decreasing)
        },
      });
    }

    // Process the redemption in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update the pay code
      const updatedPayCode = await tx.payCode.update({
        where: { id: payCode.id },
        data: {
          uses: { increment: 1 },
          redeemedById: userId,
          redeemedAt: new Date(),
          status:
            payCode.maxUses && payCode.uses + 1 >= payCode.maxUses
              ? "expired"
              : "active",
        },
      });

      // Create a transaction record
      const transaction = await tx.transaction.create({
        data: {
          amount: payCode.amount,
          type: "paycode_redeem",
          status: "completed",
          description: `Payment to @${payCode.createdBy.username} via pay code`,
          senderId: userId, // The redeemer is sending money
          recipientId: payCode.createdById, // The creator is receiving money
          payCodeId: payCode.id,
        },
        include: {
          sender: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatar: true,
            },
          },
          recipient: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatar: true,
            },
          },
        },
      });

      // Update redeemer's balance (decrease)
      await tx.user.update({
        where: { id: userId },
        data: { balance: { decrement: payCode.amount } },
      });

      // Update creator's balance (increase)
      await tx.user.update({
        where: { id: payCode.createdById },
        data: { balance: { increment: payCode.amount } },
      });

      return { transaction, updatedPayCode };
    });

    // Format dates for response
    const formattedTransaction = {
      ...result.transaction,
      createdAt: result.transaction.createdAt.toISOString(),
      updatedAt: result.transaction.updatedAt.toISOString(),
      processedAt: result.transaction.processedAt
        ? result.transaction.processedAt.toISOString()
        : null,
    };

    // Get user details for notifications
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        username: true,
        displayName: true,
      },
    });

    // Create notification for the redeemer (sender)
    await createNotification(userId, {
      category: "transaction",
      type: "transfer_sent",
      title: "Payment Sent via Pay Code",
      message: `You paid NS ${payCode.amount.toFixed(0)} to @${
        payCode.createdBy.username
      } using a pay code. View this transaction in your Bank Dashboard > Transactions section.`,
      // No link property - clicking will just mark as read
      priority: "medium",
      transactionId: result.transaction.id,
    });

    // Create notification for the creator (recipient)
    await createNotification(payCode.createdById, {
      category: "transaction",
      type: "transfer_received",
      title: "Payment Received via Pay Code",
      message: `You received NS ${payCode.amount.toFixed(
        0,
      )} from @${user?.username} using your pay code. View this transaction in your Bank Dashboard > Transactions section.`,
      // No link property - clicking will just mark as read
      priority: "medium",
      transactionId: result.transaction.id,
    });

    return NextResponse.json(formattedTransaction);
  } catch (error) {
    console.error("Error redeeming pay code:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
