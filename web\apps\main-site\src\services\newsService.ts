/**
 * News API Service
 * Provides functions for interacting with the News API endpoints
 */
import fetchClient from "@/lib/fetchClient";

// Types
export interface Article {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  image: string;
  slug: string;
  status: "draft" | "published" | "paused";
  featured: boolean;
  views: number;
  createdAt: string;
  updatedAt: string;
  publishedAt: string | null;
  author: {
    id: string;
    username: string;
    displayName: string;
    avatar: string | null;
  };
  category: {
    id: string;
    name: string;
    slug: string;
  };
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  articleCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface ArticleFilters {
  page?: number;
  limit?: number;
  status?: "draft" | "published" | "paused";
  featured?: boolean;
  category?: string;
  search?: string;
  sortBy?: string;
  order?: "asc" | "desc";
}

export interface PaginationMeta {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface ArticleResponse {
  data: Article[];
  meta: PaginationMeta;
}

export interface CreateArticleData {
  title: string;
  content: string;
  excerpt: string;
  categoryId: string;
  image: string;
  status?: "draft" | "published";
  featured?: boolean;
}

export interface UpdateArticleData {
  title?: string;
  content?: string;
  excerpt?: string;
  categoryId?: string;
  image?: string;
  status?: "draft" | "published" | "paused";
  featured?: boolean;
}

/**
 * Get articles with filtering and pagination
 */
export async function getArticles(
  filters: ArticleFilters = {},
): Promise<ArticleResponse> {
  // Convert filters to query params
  const queryParams: Record<string, string> = {};
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams[key] = String(value);
    }
  });

  return fetchClient.get<ArticleResponse>("/api/news/articles", {
    params: queryParams,
  });
}

/**
 * Get a single article by ID
 */
export async function getArticleById(id: string): Promise<Article> {
  return fetchClient.get<Article>(`/api/news/articles/${id}`);
}

/**
 * Create a new article
 */
export async function createArticle(data: CreateArticleData): Promise<Article> {
  return fetchClient.post<Article>("/api/news/articles", data);
}

/**
 * Update an existing article
 */
export async function updateArticle(
  id: string,
  data: UpdateArticleData,
): Promise<Article> {
  return fetchClient.put<Article>(`/api/news/articles/${id}`, data);
}

/**
 * Delete an article
 */
export async function deleteArticle(id: string): Promise<void> {
  await fetchClient.delete(`/api/news/articles/${id}`);
}

/**
 * Toggle article status (published/paused)
 */
export async function toggleArticleStatus(id: string): Promise<Article> {
  return fetchClient.put<Article>(`/api/news/articles/${id}/status`, {});
}

/**
 * Toggle article featured status
 */
export async function toggleArticleFeatured(id: string): Promise<Article> {
  return fetchClient.put<Article>(`/api/news/articles/${id}/featured`, {});
}

/**
 * Get all categories
 */
export async function getCategories(): Promise<Category[]> {
  return fetchClient.get<Category[]>("/api/news/categories");
}

/**
 * Create a new category
 */
export async function createCategory(data: {
  name: string;
  description?: string;
}): Promise<Category> {
  return fetchClient.post<Category>("/api/news/categories", data);
}

/**
 * Update an existing category
 */
export async function updateCategory(
  id: string,
  data: { name: string; description?: string },
): Promise<Category> {
  return fetchClient.put<Category>(`/api/news/categories/${id}`, data);
}

/**
 * Delete a category
 */
export async function deleteCategory(id: string): Promise<void> {
  await fetchClient.delete(`/api/news/categories/${id}`);
}
