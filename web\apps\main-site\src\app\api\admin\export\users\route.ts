import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { generateCSVOnServer } from "@/utils/csv-export";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
/**
 * API route for exporting user data as CSV
 *
 * This endpoint allows admins to export user data in CSV format.
 * It supports filtering by status, role, and search term.
 */
export async function GET(request: Request) {
  try {
    // Check if user is authenticated and has admin role
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const isAdmin = await userHasRole(request, "admin");
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Admin privileges required" },
        { status: 403 },
      );
    }

    // Get query parameters for filtering
    const url = new URL(request.url);
    const status = url.searchParams.get("status");
    const role = url.searchParams.get("role");
    const search = url.searchParams.get("search");

    // Build the query
    const where: any = {};

    if (status) {
      where.status = status;
    }

    if (role) {
      switch (role) {
        case "admin":
          where.isAdmin = true;
          break;
        case "editor":
          where.isEditor = true;
          break;
        case "banker":
          where.isBanker = true;
          break;
        case "chatModerator":
          where.isChatModerator = true;
          break;
        case "volunteerCoordinator":
          where.isVolunteerCoordinator = true;
          break;
        case "leadManager":
          where.isLeadManager = true;
      }
    }

    if (search) {
      where.OR = [
        { username: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
        { displayName: { contains: search, mode: "insensitive" } },
      ];
    }

    // Fetch users from database
    const users = await prisma.user.findMany({
      where,
      select: {
        id: true,
        username: true,
        email: true,
        displayName: true,
        isAdmin: true,
        isEditor: true,
        isBanker: true,
        isChatModerator: true,
        isEmailVerified: true,
        createdAt: true,
        updatedAt: true,
        isVolunteerCoordinator: true,
        isLeadManager: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Transform data for CSV export
    const formattedUsers = users.map((user) => ({
      id: user.id,
      username: user.username,
      displayName: user.displayName || "",
      email: user.email,
      isEmailVerified: user.isEmailVerified ? "Yes" : "No",
      roles: [
        user.isAdmin ? "Admin" : "",
        user.isEditor ? "Editor" : "",
        user.isBanker ? "Banker" : "",
        user.isChatModerator ? "Chat Moderator" : "",
        user.isVolunteerCoordinator ? "Volunteer Coordinator" : "",
        user.isLeadManager ? "Lead Manager" : "",
      ]
        .filter(Boolean)
        .join(", "),
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString(),
    }));

    // Define headers with proper typing
    const headers: { key: keyof (typeof formattedUsers)[0]; label: string }[] =
      [
        { key: "id", label: "ID" },
        { key: "username", label: "Username" },
        { key: "displayName", label: "Display Name" },
        { key: "email", label: "Email" },
        { key: "isEmailVerified", label: "Email Verified" },
        { key: "roles", label: "Roles" },
        { key: "createdAt", label: "Created At" },
        { key: "updatedAt", label: "Last Updated" },
      ];

    // Generate CSV content
    const csvContent = generateCSVOnServer(formattedUsers, headers);

    // Set headers for CSV download
    const filename = `users-export-${
      new Date().toISOString().split("T")[0]
    }.csv`;

    return new Response(csvContent, {
      status: 200,
      headers: {
        "Content-Type": "text/csv;charset=utf-8;",
        "Content-Disposition": `attachment; filename="${filename}"`,
      },
    });
  } catch (error) {
    console.error("Error exporting users:", error);
    return NextResponse.json(
      { error: "Failed to export users" },
      { status: 500 },
    );
  }
}
