import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../lib/prisma";
import { getCurrentUser } from "../../../lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET endpoint to fetch user notifications
export async function GET(req: NextRequest) {
  try {
    // Get current user
    const currentUser = await getCurrentUser(req);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Parse query parameters
    const url = new URL(req.url);
    const limit = parseInt(url.searchParams.get("limit") || "20");
    const unreadOnly = url.searchParams.get("unreadOnly") === "true";
    const category = url.searchParams.get("category") || undefined;

    // Build where condition
    const whereCondition: any = {
      userId: currentUser.id,
    };

    if (unreadOnly) {
      whereCondition.read = false;
    }

    if (category) {
      whereCondition.category = category;
    }

    // Query the database for notifications
    const notifications = await prisma.notification.findMany({
      where: whereCondition,
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
      include: {
        transaction: true,
      },
    });

    // Format dates as ISO strings for JSON serialization
    const formattedNotifications = notifications.map((notification) => ({
      ...notification,
      createdAt: notification.createdAt.toISOString(),
      updatedAt: notification.updatedAt.toISOString(),
      transaction: notification.transaction
        ? {
            ...notification.transaction,
            createdAt: notification.transaction.createdAt.toISOString(),
            updatedAt: notification.transaction.updatedAt.toISOString(),
            processedAt: notification.transaction.processedAt
              ? notification.transaction.processedAt.toISOString()
              : null,
          }
        : null,
    }));

    return NextResponse.json(formattedNotifications);
  } catch (error) {
    console.error("Error fetching notifications:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

// PATCH endpoint to mark notifications as read
export async function PATCH(req: NextRequest) {
  try {
    // Get current user
    const currentUser = await getCurrentUser(req);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Parse request body
    const body = await req.json();
    const { ids, all } = body;

    if (all) {
      // Mark all notifications as read
      await prisma.notification.updateMany({
        where: {
          userId: currentUser.id,
          read: false,
        },
        data: {
          read: true,
        },
      });

      return NextResponse.json({
        message: "All notifications marked as read",
      });
    } else if (ids && Array.isArray(ids) && ids.length > 0) {
      // Mark specific notifications as read
      await prisma.notification.updateMany({
        where: {
          id: {
            in: ids,
          },
          userId: currentUser.id,
        },
        data: {
          read: true,
        },
      });

      return NextResponse.json({
        message: `${ids.length} notification(s) marked as read`,
      });
    } else {
      return NextResponse.json(
        { error: "Invalid request. Provide ids or all=true" },
        { status: 400 },
      );
    }
  } catch (error) {
    console.error("Error marking notifications as read:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
