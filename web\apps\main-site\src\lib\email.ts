import nodemailer from "nodemailer";
import type { SendMailOptions, SentMessageInfo } from "nodemailer";

// Email configuration based on credentials in root directory
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST || "smtp.gmail.com",
  port: Number(process.env.EMAIL_PORT) || 465,
  secure: process.env.EMAIL_SECURE === "true",
  auth: {
    user: process.env.EMAIL_USER || "",
    pass: process.env.EMAIL_PASSWORD || "",
  },
});

export interface EmailData {
  to: string;
  subject: string;
  text: string;
  html?: string;
  replyTo?: string;
}

/**
 * Send an email using configured email credentials
 * @param emailData The email data containing recipient, subject, and content
 * @returns A promise that resolves to true if the email was sent successfully
 */
export async function sendEmail({
  to,
  subject,
  text,
  html,
  replyTo,
}: EmailData): Promise<boolean> {
  try {
    const mailOptions: SendMailOptions = {
      from: `"Bank of Styx" <${process.env.EMAIL_USER}>`,
      to,
      subject,
      text,
      html: html || text,
    };

    if (replyTo) {
      mailOptions.replyTo = replyTo;
    }

    const info: SentMessageInfo = await transporter.sendMail(mailOptions);

    console.log(`Email sent: ${info.messageId}`);
    return true;
  } catch (error) {
    console.error("Error sending email:", error);
    return false;
  }
}
