"use client";

export default function Footer() {
  return (
    <footer className="relative w-full z-20">
      {/* Overlay background that covers the entire width */}
      <div className="absolute inset-0 bg-secondary"></div>

      {/* Content container that's centered in the main content area */}
      <div className="relative mx-auto py-6">
        {/* Add left padding on desktop to account for navigation width */}
        <div className="container mx-auto px-4 md:pl-[calc(10rem+1.5rem)] md:pr-4">
          {/* Centered content with max-width to reduce empty space */}
          <div className="max-w-3xl mx-auto">
            {/* Two column layout that doesn't stack on mobile */}
            <div className="flex flex-row justify-center text-center">
              {/* Nation website links */}
              <div className="w-1/2 px-2">
                <h3 className="text-lg font-semibold mb-4">Nation Websites</h3>
                <ul className="space-y-2">
                  <li>
                    <a
                      href="#"
                      className="text-gray-300 hover:text-text-primary transition"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      La Maga Demonio&apos;s
                    </a>
                  </li>
                  <li>
                    <a
                      href="#"
                      className="text-gray-300 hover:text-text-primary transition"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Pirate Rinfair
                    </a>
                  </li>
                  <li>
                    <a
                      href="#"
                      className="text-gray-300 hover:text-text-primary transition"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Community Portal
                    </a>
                  </li>
                </ul>
              </div>

              {/* Social media links */}
              <div className="w-1/2 px-2">
                <h3 className="text-lg font-semibold mb-4">Social Media</h3>
                <ul className="space-y-2">
                  <li>
                    <a
                      href="#"
                      className="text-gray-300 hover:text-text-primary transition"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Facebook Group
                    </a>
                  </li>
                  <li>
                    <a
                      href="#"
                      className="text-gray-300 hover:text-text-primary transition"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Discord Server
                    </a>
                  </li>
                </ul>
              </div>
            </div>

            <div className="mt-8 pt-6 border-t border-gray-700 text-center text-sm text-text-secondary">
              <p>
                &copy; {new Date().getFullYear()} Bank of Styx. All rights
                reserved.
              </p>
              <p className="mt-2">Do as you will, yet harm none</p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
