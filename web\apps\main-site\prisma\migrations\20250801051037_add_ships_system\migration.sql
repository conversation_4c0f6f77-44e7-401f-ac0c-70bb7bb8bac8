-- AlterTable
ALTER TABLE `user` ADD COLUMN `isLandSteward` BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE `ships` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `description` TEXT NOT NULL,
    `slogan` VARCHAR(500) NULL,
    `logo` VARCHAR(191) NULL,
    `tags` JSON NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'active',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `captainId` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `ships_name_key`(`name`),
    INDEX `ships_captainId_idx`(`captainId`),
    INDEX `ships_status_idx`(`status`),
    PRIMARY <PERSON>EY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ship_members` (
    `id` VARCHAR(191) NOT NULL,
    `role` VARCHAR(255) NOT NULL DEFAULT 'Member',
    `status` VARCHAR(191) NOT NULL DEFAULT 'active',
    `joinedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `leftAt` DATETIME(3) NULL,
    `userId` VARCHAR(191) NOT NULL,
    `shipId` VARCHAR(191) NOT NULL,

    INDEX `ship_members_userId_idx`(`userId`),
    INDEX `ship_members_shipId_idx`(`shipId`),
    INDEX `ship_members_status_idx`(`status`),
    UNIQUE INDEX `ship_members_userId_shipId_key`(`userId`, `shipId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `captain_applications` (
    `id` VARCHAR(191) NOT NULL,
    `shipName` VARCHAR(255) NOT NULL,
    `description` TEXT NOT NULL,
    `tags` JSON NULL,
    `logoPath` VARCHAR(191) NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'pending',
    `rejectionReason` TEXT NULL,
    `previouslyRejected` BOOLEAN NOT NULL DEFAULT false,
    `appliedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `reviewedAt` DATETIME(3) NULL,
    `userId` VARCHAR(191) NOT NULL,
    `reviewedById` VARCHAR(191) NULL,

    INDEX `captain_applications_userId_idx`(`userId`),
    INDEX `captain_applications_status_idx`(`status`),
    INDEX `captain_applications_reviewedById_idx`(`reviewedById`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ship_join_requests` (
    `id` VARCHAR(191) NOT NULL,
    `type` VARCHAR(191) NOT NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'pending',
    `message` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `respondedAt` DATETIME(3) NULL,
    `userId` VARCHAR(191) NOT NULL,
    `shipId` VARCHAR(191) NOT NULL,
    `requestedById` VARCHAR(191) NOT NULL,

    INDEX `ship_join_requests_userId_idx`(`userId`),
    INDEX `ship_join_requests_shipId_idx`(`shipId`),
    INDEX `ship_join_requests_requestedById_idx`(`requestedById`),
    INDEX `ship_join_requests_status_idx`(`status`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `ships` ADD CONSTRAINT `ships_captainId_fkey` FOREIGN KEY (`captainId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ship_members` ADD CONSTRAINT `ship_members_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ship_members` ADD CONSTRAINT `ship_members_shipId_fkey` FOREIGN KEY (`shipId`) REFERENCES `ships`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `captain_applications` ADD CONSTRAINT `captain_applications_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `captain_applications` ADD CONSTRAINT `captain_applications_reviewedById_fkey` FOREIGN KEY (`reviewedById`) REFERENCES `user`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ship_join_requests` ADD CONSTRAINT `ship_join_requests_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ship_join_requests` ADD CONSTRAINT `ship_join_requests_shipId_fkey` FOREIGN KEY (`shipId`) REFERENCES `ships`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ship_join_requests` ADD CONSTRAINT `ship_join_requests_requestedById_fkey` FOREIGN KEY (`requestedById`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
