// Enhanced image processing utilities for the V2 upload system
import sharp from "sharp";

export interface ImageDimensions {
  width: number;
  height: number;
}

export interface ProcessImageOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: "jpeg" | "png" | "webp";
  fit?: "contain" | "cover" | "fill" | "inside" | "outside";
}

/**
 * Process an image buffer with resizing and optimization
 * @param buffer - The image buffer to process
 * @param options - Processing options
 * @returns Promise<Buffer> - The processed image buffer
 */
export async function processImage(
  buffer: Buffer,
  options: ProcessImageOptions = {},
): Promise<Buffer> {
  const {
    maxWidth = 1200,
    maxHeight = 900,
    quality = 85,
    format = "jpeg",
    fit = "inside",
  } = options;

  try {
    let sharpInstance = sharp(buffer);

    // Resize if dimensions are specified
    if (maxWidth || maxHeight) {
      sharpInstance = sharpInstance.resize(maxWidth, maxHeight, {
        fit,
        withoutEnlargement: true,
      });
    }

    // Apply format and quality
    switch (format) {
      case "jpeg":
        sharpInstance = sharpInstance.jpeg({ quality });
        break;
      case "png":
        sharpInstance = sharpInstance.png({ quality });
        break;
      case "webp":
        sharpInstance = sharpInstance.webp({ quality });
        break;
    }

    return await sharpInstance.toBuffer();
  } catch (error) {
    console.error("Error processing image:", error);
    throw new Error("Failed to process image");
  }
}

/**
 * Get image dimensions from a buffer
 * @param buffer - The image buffer
 * @returns Promise<ImageDimensions> - The image dimensions
 */
export async function getImageDimensions(
  buffer: Buffer,
): Promise<ImageDimensions> {
  try {
    const metadata = await sharp(buffer).metadata();
    return {
      width: metadata.width || 0,
      height: metadata.height || 0,
    };
  } catch (error) {
    console.error("Error getting image dimensions:", error);
    throw new Error("Failed to get image dimensions");
  }
}

/**
 * Create a thumbnail from an image buffer
 * @param buffer - The original image buffer
 * @param width - Thumbnail width (default: 150)
 * @param height - Thumbnail height (default: 150)
 * @returns Promise<Buffer> - The thumbnail buffer
 */
export async function createThumbnail(
  buffer: Buffer,
  width = 150,
  height = 150,
): Promise<Buffer> {
  try {
    return await sharp(buffer)
      .resize(width, height, {
        fit: "cover",
        position: "center",
      })
      .jpeg({ quality: 80 })
      .toBuffer();
  } catch (error) {
    console.error("Error creating thumbnail:", error);
    throw new Error("Failed to create thumbnail");
  }
}

/**
 * Optimize an image for web delivery
 * @param buffer - The original image buffer
 * @param uploadType - The type of upload (affects optimization settings)
 * @returns Promise<Buffer> - The optimized image buffer
 */
export async function optimizeForWeb(
  buffer: Buffer,
  uploadType: string,
): Promise<Buffer> {
  const optimizationSettings: { [key: string]: ProcessImageOptions } = {
    avatar: {
      maxWidth: 200,
      maxHeight: 200,
      quality: 90,
      format: "jpeg",
      fit: "cover",
    },
    news: {
      maxWidth: 1200,
      maxHeight: 800,
      quality: 85,
      format: "jpeg",
      fit: "inside",
    },
    product: {
      maxWidth: 800,
      maxHeight: 600,
      quality: 90,
      format: "jpeg",
      fit: "inside",
    },
    deposit: {
      maxWidth: 1000,
      maxHeight: 1000,
      quality: 90,
      format: "jpeg",
      fit: "inside",
    },
    event: {
      maxWidth: 1000,
      maxHeight: 667,
      quality: 85,
      format: "jpeg",
      fit: "inside",
    },
  };

  const settings =
    optimizationSettings[uploadType] || optimizationSettings.news;
  return await processImage(buffer, settings);
}
