import { prisma } from "@/lib/prisma";
import { TicketStatus } from "@prisma/client";

// ============================================================================
// PRODUCT TICKET FUNCTIONS
// ============================================================================

/**
 * Generate individual tickets when a product is created or inventory is updated
 */
export async function generateTicketsForProduct(
  productId: string,
  quantity: number,
) {
  const tickets = Array(quantity)
    .fill(null)
    .map(() => ({
      productId,
      status: TicketStatus.AVAILABLE,
    }));

  return await prisma.ticket.createMany({
    data: tickets,
  });
}

/**
 * Count available tickets for a product
 */
export async function getAvailableTicketCount(
  productId: string,
): Promise<number> {
  return await prisma.ticket.count({
    where: {
      productId,
      status: TicketStatus.AVAILABLE,
    },
  });
}

/**
 * Update product inventory and sync tickets
 */
export async function updateProductInventory(
  productId: string,
  newInventory: number,
) {
  return await prisma.$transaction(async (tx) => {
    // Get current ticket count
    const currentTicketCount = await tx.ticket.count({
      where: { productId },
    });

    if (newInventory > currentTicketCount) {
      // Add more tickets
      const additionalTickets = newInventory - currentTicketCount;
      const tickets = Array(additionalTickets)
        .fill(null)
        .map(() => ({
          productId,
          status: TicketStatus.AVAILABLE,
        }));

      await tx.ticket.createMany({
        data: tickets,
      });
    } else if (newInventory < currentTicketCount) {
      // Remove excess tickets (only AVAILABLE ones)
      const ticketsToRemove = currentTicketCount - newInventory;
      const availableTickets = await tx.ticket.findMany({
        where: { productId, status: TicketStatus.AVAILABLE },
        take: ticketsToRemove,
        orderBy: { createdAt: "desc" },
      });

      if (availableTickets.length < ticketsToRemove) {
        throw new Error("Cannot reduce inventory below sold/held tickets");
      }

      await tx.ticket.deleteMany({
        where: {
          id: { in: availableTickets.map((t) => t.id) },
        },
      });
    }

    // Update product inventory
    return await tx.product.update({
      where: { id: productId },
      data: { inventory: newInventory },
    });
  });
}

// ============================================================================
// TICKET HOLD FUNCTIONS
// ============================================================================

/**
 * Create hold with specific tickets for cart item
 */
export async function createHoldWithSpecificTickets(
  userId: string,
  productId: string,
  quantity: number,
) {
  return await prisma.$transaction(async (tx) => {
    // Find available tickets for this product
    const availableTickets = await tx.ticket.findMany({
      where: {
        productId,
        status: TicketStatus.AVAILABLE,
      },
      take: quantity,
    });

    if (availableTickets.length < quantity) {
      throw new Error("Not enough tickets available");
    }

    // Create cart if needed
    let cart = await tx.cart.findFirst({
      where: { userId },
    });

    if (!cart) {
      cart = await tx.cart.create({
        data: { userId },
      });
    }

    // Create cart item
    const cartItem = await tx.cartItem.create({
      data: {
        cartId: cart.id,
        productId,
        quantity,
      },
    });

    // Create the hold
    const hold = await tx.ticketHold.create({
      data: {
        userId,
        cartItemId: cartItem.id,
        expiresAt: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
      },
    });

    // Update tickets to mark them as held
    await tx.ticket.updateMany({
      where: {
        id: { in: availableTickets.map((ticket) => ticket.id) },
      },
      data: {
        status: TicketStatus.HELD,
        holdId: hold.id,
      },
    });

    return {
      cartItem,
      hold,
      heldTickets: availableTickets,
    };
  });
}

/**
 * Release tickets from hold
 */
export async function releaseTicketsFromHold(holdId: string) {
  return await prisma.$transaction(async (tx) => {
    // Find all tickets in this hold
    const heldTickets = await tx.ticket.findMany({
      where: {
        holdId,
        status: TicketStatus.HELD,
      },
    });

    if (heldTickets.length === 0) {
      return { released: 0 };
    }

    // Release the tickets
    await tx.ticket.updateMany({
      where: { holdId },
      data: {
        status: TicketStatus.AVAILABLE,
        holdId: null,
      },
    });

    // Update the hold record
    await tx.ticketHold.update({
      where: { id: holdId },
      data: {
        cartItemId: null, // Disconnect from cart item
      },
    });

    return { released: heldTickets.length };
  });
}

/**
 * Release expired holds
 */
export async function releaseExpiredHoldTickets() {
  const now = new Date();

  return await prisma.$transaction(async (tx) => {
    // Find expired holds
    const expiredHolds = await tx.ticketHold.findMany({
      where: {
        expiresAt: { lt: now },
      },
    });

    let ticketsReleased = 0;

    // Release tickets for each expired hold
    for (const hold of expiredHolds) {
      const result = await tx.ticket.updateMany({
        where: {
          holdId: hold.id,
          status: TicketStatus.HELD,
        },
        data: {
          status: TicketStatus.AVAILABLE,
          holdId: null,
        },
      });

      ticketsReleased += result.count;
    }

    return {
      holdsReleased: expiredHolds.length,
      ticketsReleased,
    };
  });
}

/**
 * Convert held tickets to sold when purchase completes
 */
export async function convertHeldTicketsToSold(
  orderId: string,
  userId: string,
  cartId: string,
) {
  return await prisma.$transaction(async (tx) => {
    // Find all tickets held by this user through cart items
    const cartItems = await tx.cartItem.findMany({
      where: {
        cartId,
        ticketHold: { userId },
      },
      include: { ticketHold: true },
    });

    const holdIds = cartItems
      .map((item) => item.ticketHold?.id)
      .filter((id) => id !== undefined) as string[];

    if (holdIds.length === 0) {
      return { converted: 0 };
    }

    // Convert tickets from HELD to SOLD
    const result = await tx.ticket.updateMany({
      where: {
        holdId: { in: holdIds },
        status: TicketStatus.HELD,
      },
      data: {
        status: TicketStatus.SOLD,
        holdId: null,
        orderId,
      },
    });

    return { converted: result.count };
  });
}

// ============================================================================
// VOLUNTEER SLOT FUNCTIONS
// ============================================================================

/**
 * Generate individual volunteer slots when shift is created
 */
export async function generateSlotsForShift(
  shiftId: string,
  maxVolunteers: number,
) {
  const slots = Array(maxVolunteers)
    .fill(null)
    .map(() => ({
      shiftId,
      status: TicketStatus.AVAILABLE,
    }));

  return await prisma.volunteerSlot.createMany({
    data: slots,
  });
}

/**
 * Count available volunteer slots for a shift
 */
export async function getAvailableSlotCount(shiftId: string): Promise<number> {
  return await prisma.volunteerSlot.count({
    where: {
      shiftId,
      status: TicketStatus.AVAILABLE,
    },
  });
}

/**
 * Update shift capacity and sync slots
 */
export async function updateShiftCapacity(
  shiftId: string,
  newMaxVolunteers: number,
) {
  return await prisma.$transaction(async (tx) => {
    // Get current slot count
    const currentSlotCount = await tx.volunteerSlot.count({
      where: { shiftId },
    });

    if (newMaxVolunteers > currentSlotCount) {
      // Add more slots
      const additionalSlots = newMaxVolunteers - currentSlotCount;
      const slots = Array(additionalSlots)
        .fill(null)
        .map(() => ({
          shiftId,
          status: TicketStatus.AVAILABLE,
        }));

      await tx.volunteerSlot.createMany({
        data: slots,
      });
    } else if (newMaxVolunteers < currentSlotCount) {
      // Remove excess slots (only AVAILABLE ones)
      const slotsToRemove = currentSlotCount - newMaxVolunteers;
      const availableSlots = await tx.volunteerSlot.findMany({
        where: { shiftId, status: TicketStatus.AVAILABLE },
        take: slotsToRemove,
        orderBy: { createdAt: "desc" },
      });

      if (availableSlots.length < slotsToRemove) {
        throw new Error("Cannot reduce capacity below assigned slots");
      }

      await tx.volunteerSlot.deleteMany({
        where: {
          id: { in: availableSlots.map((s) => s.id) },
        },
      });
    }

    // Update shift maxVolunteers
    return await tx.volunteerShift.update({
      where: { id: shiftId },
      data: { maxVolunteers: newMaxVolunteers },
    });
  });
}

// ============================================================================
// VOLUNTEER SLOT HOLD FUNCTIONS
// ============================================================================

/**
 * Create hold for volunteer slots
 */
export async function createVolunteerSlotHold(
  userId: string,
  shiftId: string,
  slotsNeeded = 1,
) {
  return await prisma.$transaction(async (tx) => {
    // Find available slots for this shift
    const availableSlots = await tx.volunteerSlot.findMany({
      where: {
        shiftId,
        status: TicketStatus.AVAILABLE,
      },
      take: slotsNeeded,
    });

    if (availableSlots.length < slotsNeeded) {
      throw new Error("Not enough volunteer slots available");
    }

    // Create the hold
    const hold = await tx.volunteerSlotHold.create({
      data: {
        userId,
        expiresAt: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
      },
    });

    // Update slots to mark them as held
    await tx.volunteerSlot.updateMany({
      where: {
        id: { in: availableSlots.map((slot) => slot.id) },
      },
      data: {
        status: TicketStatus.HELD,
        holdId: hold.id,
      },
    });

    return {
      hold,
      heldSlots: availableSlots,
    };
  });
}

/**
 * Release volunteer slot hold
 */
export async function releaseVolunteerSlotHold(holdId: string) {
  return await prisma.$transaction(async (tx) => {
    const result = await tx.volunteerSlot.updateMany({
      where: {
        holdId,
        status: TicketStatus.HELD,
      },
      data: {
        status: TicketStatus.AVAILABLE,
        holdId: null,
      },
    });

    return { released: result.count };
  });
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get product availability statistics
 */
export async function getProductAvailabilityStats(productId: string) {
  const [total, available, held, sold, cancelled] = await Promise.all([
    prisma.ticket.count({ where: { productId } }),
    prisma.ticket.count({
      where: { productId, status: TicketStatus.AVAILABLE },
    }),
    prisma.ticket.count({ where: { productId, status: TicketStatus.HELD } }),
    prisma.ticket.count({ where: { productId, status: TicketStatus.SOLD } }),
    prisma.ticket.count({
      where: { productId, status: TicketStatus.CANCELLED },
    }),
  ]);

  return {
    total,
    available,
    held,
    sold,
    cancelled,
  };
}

/**
 * Check if product has enough available tickets
 */
export async function isProductAvailable(
  productId: string,
  requestedQuantity = 1,
): Promise<boolean> {
  const availableCount = await getAvailableTicketCount(productId);
  return availableCount >= requestedQuantity;
}
