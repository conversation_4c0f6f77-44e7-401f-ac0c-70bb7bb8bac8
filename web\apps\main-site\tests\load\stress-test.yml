config:
  target: 'http://192.168.1.87:3000'
  phases:
    # Stress test to find breaking point
    - duration: 60
      arrivalRate: 50
    - duration: 60
      arrivalRate: 100
    - duration: 60  
      arrivalRate: 200
    - duration: 60
      arrivalRate: 300
  variables:
    userCount: 1000

scenarios:
  - name: "High Load Mixed Operations"
    weight: 100
    flow:
      # Rapid fire requests
      - get:
          url: "/"
      - get:
          url: "/api/products"
      - get:
          url: "/api/events"
      - get:
          url: "/news"
      
      # Concurrent API calls
      - parallel:
        - get:
            url: "/api/notifications"
        - get:
            url: "/api/bank/statistics"
        - get:
            url: "/api/product-categories"
      
      # Database intensive operations
      - get:
          url: "/api/bank/transactions?limit=100"
      - think: 0.1-0.5