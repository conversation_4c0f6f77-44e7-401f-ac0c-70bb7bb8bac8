import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../../lib/prisma";
import { verifyToken } from "../../../../../lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export const GET = async (
  req: NextRequest,
  { params }: { params: { id: string } },
) => {
  try {
    const id = params.id;

    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Find the pay code
    const payCode = await prisma.payCode.findUnique({
      where: { id },
      include: {
        createdBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        redeemedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
      },
    });

    if (!payCode) {
      return NextResponse.json(
        { error: "Pay code not found" },
        { status: 404 },
      );
    }

    // Check if the user is the creator of the pay code or the redeemer
    if (payCode.createdById !== userId && payCode.redeemedById !== userId) {
      return NextResponse.json(
        { error: "You can only view your own pay codes" },
        { status: 403 },
      );
    }

    // Format dates for response
    const formattedPayCode = {
      ...payCode,
      createdAt: payCode.createdAt.toISOString(),
      expiresAt: payCode.expiresAt.toISOString(),
      redeemedAt: payCode.redeemedAt ? payCode.redeemedAt.toISOString() : null,
    };

    return NextResponse.json(formattedPayCode);
  } catch (error) {
    console.error("Error getting pay code:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};

export const PATCH = async (
  req: NextRequest,
  { params }: { params: { id: string } },
) => {
  try {
    const id = params.id;

    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Get request body
    const { status } = await req.json();

    // Validate input
    if (!status || !["active", "paused", "cancelled"].includes(status)) {
      return NextResponse.json(
        { error: "Valid status is required (active, paused, cancelled)" },
        { status: 400 },
      );
    }

    // Find the pay code
    const payCode = await prisma.payCode.findUnique({
      where: { id },
    });

    if (!payCode) {
      return NextResponse.json(
        { error: "Pay code not found" },
        { status: 404 },
      );
    }

    // Check if the user is the creator of the pay code
    if (payCode.createdById !== userId) {
      return NextResponse.json(
        { error: "You can only update your own pay codes" },
        { status: 403 },
      );
    }

    // Check if the pay code is already redeemed or expired
    if (["redeemed", "expired"].includes(payCode.status)) {
      return NextResponse.json(
        { error: `Pay code is already ${payCode.status}` },
        { status: 400 },
      );
    }

    // Update the pay code
    const updatedPayCode = await prisma.payCode.update({
      where: { id },
      data: { status },
      include: {
        createdBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        redeemedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
      },
    });

    // Format dates for response
    const formattedPayCode = {
      ...updatedPayCode,
      createdAt: updatedPayCode.createdAt.toISOString(),
      expiresAt: updatedPayCode.expiresAt.toISOString(),
      redeemedAt: updatedPayCode.redeemedAt
        ? updatedPayCode.redeemedAt.toISOString()
        : null,
    };

    return NextResponse.json(formattedPayCode);
  } catch (error) {
    console.error("Error updating pay code:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};

export const DELETE = async (
  req: NextRequest,
  { params }: { params: { id: string } },
) => {
  try {
    const id = params.id;

    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Find the pay code
    const payCode = await prisma.payCode.findUnique({
      where: { id },
    });

    if (!payCode) {
      return NextResponse.json(
        { error: "Pay code not found" },
        { status: 404 },
      );
    }

    // Check if the user is the creator of the pay code
    if (payCode.createdById !== userId) {
      return NextResponse.json(
        { error: "You can only delete your own pay codes" },
        { status: 403 },
      );
    }

    // Check if the pay code is already redeemed
    if (payCode.status === "redeemed") {
      return NextResponse.json(
        { error: "Cannot delete a redeemed pay code" },
        { status: 400 },
      );
    }

    // Delete the pay code
    await prisma.payCode.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting pay code:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
