"use client";

import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { useAuth } from "./AuthContext";
import { useUserState } from "./UserStateContext";
import {
  getUserNotifications,
  markNotificationsAsRead,
  deleteNotification,
  Notification,
} from "../services/notificationService";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useSSE } from "../hooks/useSSE";

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  error: Error | null;
  markAsRead: (ids: string[]) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
  refreshNotifications: () => void;
  isNotificationPanelOpen: boolean;
  openNotificationPanel: () => void;
  closeNotificationPanel: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined,
);

export const NotificationProvider = ({ children }: { children: ReactNode }) => {
  const { user, isAuthenticated } = useAuth();
  const { updateNotificationTimestamp } = useUserState();
  const [isNotificationPanelOpen, setIsNotificationPanelOpen] = useState(false);
  const queryClient = useQueryClient();

  // Initialize SSE connection for real-time notifications
  useSSE({
    onMessage: (data) => {
      if (data.type === "notification") {
        console.log(
          "[NotificationContext] Received notification via SSE:",
          data.notification.title,
        );
        // The notification will be automatically added to the cache by the useSSE hook
      }
    },
    enabled: isAuthenticated, // Auto-connect when authenticated
  });

  // Fetch notifications with optimized polling as fallback
  const {
    data: notifications = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: ["notifications"],
    queryFn: async () => {
      return getUserNotifications();
    },
    enabled: isAuthenticated,
    // Use longer polling interval since we have SSE for real-time updates
    refetchInterval: 30000, // Poll every 30 seconds as fallback
    // Prevent unnecessary refetches if data is fresh
    staleTime: 5000,
  });

  // Calculate unread count
  const unreadCount = notifications.filter(
    (notification) => !notification.read,
  ).length;

  // Mark notifications as read
  const markAsRead = async (ids: string[]) => {
    if (ids.length === 0) return;

    try {
      await markNotificationsAsRead({ ids });
      queryClient.invalidateQueries({ queryKey: ["notifications"] });
    } catch (error) {
      console.error("Error marking notifications as read:", error);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      await markNotificationsAsRead({ all: true });
      queryClient.invalidateQueries({ queryKey: ["notifications"] });
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
    }
  };

  // Delete a notification
  const handleDeleteNotification = async (id: string) => {
    try {
      await deleteNotification(id);
      queryClient.invalidateQueries({ queryKey: ["notifications"] });
    } catch (error) {
      console.error("Error deleting notification:", error);
    }
  };

  // Open notification panel
  const openNotificationPanel = () => {
    setIsNotificationPanelOpen(true);
  };

  // Close notification panel
  const closeNotificationPanel = () => {
    setIsNotificationPanelOpen(false);
  };

  // Trigger immediate notification refresh
  const refreshNotifications = async () => {
    await updateNotificationTimestamp();
    queryClient.invalidateQueries({ queryKey: ["notifications"] });
  };

  // Auto-refresh when user changes
  useEffect(() => {
    if (isAuthenticated && user?.id) {
      // Only invalidate the query, don't update timestamp here
      queryClient.invalidateQueries({ queryKey: ["notifications"] });
    }
  }, [isAuthenticated, user?.id, queryClient]);

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        isLoading,
        error,
        markAsRead,
        markAllAsRead,
        deleteNotification: handleDeleteNotification,
        refreshNotifications,
        isNotificationPanelOpen,
        openNotificationPanel,
        closeNotificationPanel,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error(
      "useNotifications must be used within a NotificationProvider",
    );
  }
  return context;
};
