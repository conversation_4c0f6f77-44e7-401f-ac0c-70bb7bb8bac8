# Non-Technical User Guides

## Overview

This guide is designed for community administrators and non-technical staff who need to manage the Bank of Styx website on a day-to-day basis. It covers basic administration tasks, troubleshooting, and when to escalate issues to technical support.

## 🎯 Who This Guide Is For

- Community administrators
- Content managers
- Customer support staff
- Anyone who needs to manage the website but isn't a programmer

## Daily Website Administration

### Morning Checklist (5 minutes)

#### 1. Website Accessibility Check
1. **Open your web browser**
2. **Navigate to your website** (e.g., bankofstyx.com)
3. **Check these pages load properly:**
   - [ ] Homepage
   - [ ] Login page
   - [ ] News section
   - [ ] Banking dashboard (after logging in)

#### 2. Quick System Health Check
1. **Log into the admin dashboard**
   - Go to [yoursite.com/admin]
   - Use your admin credentials
   - Look for any red warning messages

2. **Check recent activity**
   - Review new user registrations
   - Check recent transactions
   - Look for any error reports

#### 3. User Support Review
1. **Check support tickets**
   - Go to Admin → Support Tickets
   - Review any new or urgent tickets
   - Respond to user inquiries

2. **Monitor user feedback**
   - Check Discord/community channels
   - Review any reported issues
   - Note any recurring problems

### Content Management Tasks

#### Managing News Articles
1. **Creating a News Article**
   - Go to Admin → News → Create Article
   - Fill in the title and content
   - Select appropriate category
   - Set featured status if needed
   - Click "Publish"

2. **Editing Existing Articles**
   - Go to Admin → News → Manage Articles
   - Find the article to edit
   - Click "Edit"
   - Make changes and click "Update"

3. **Managing Categories**
   - Go to Admin → News → Categories
   - Add new categories as needed
   - Edit existing category names
   - Delete unused categories

#### User Account Management
1. **Viewing User Accounts**
   - Go to Admin → Users
   - Search for specific users
   - View account details and activity

2. **Helping Users with Account Issues**
   - Reset passwords: Admin → Users → [User] → Reset Password
   - Verify accounts: Admin → Users → [User] → Verify Account
   - Adjust balances: Admin → Banking → Adjust Balance (use carefully!)

3. **Managing User Roles**
   - Admin users: Can access admin dashboard
   - Cashier users: Can process transactions
   - Regular users: Standard access
   - Volunteer leads: Can manage volunteer activities

#### Event Management
1. **Creating Events**
   - Go to Admin → Events → Create Event
   - Fill in event details (name, date, description)
   - Set capacity limits
   - Assign categories
   - Publish the event

2. **Managing Event Registrations**
   - View who's registered: Admin → Events → [Event] → Registrations
   - Add manual registrations if needed
   - Handle cancellations and refunds

#### Shop and Product Management
1. **Adding Products**
   - Go to Admin → Shop → Products → Add Product
   - Upload product images
   - Set prices and inventory
   - Write product descriptions
   - Publish the product

2. **Managing Orders**
   - View orders: Admin → Shop → Orders
   - Update order status
   - Handle refunds and cancellations
   - Print shipping labels (if applicable)

### Banking System Administration

#### Daily Banking Tasks
1. **Review Pending Transactions**
   - Go to Admin → Banking → Pending Transactions
   - Approve legitimate deposits
   - Investigate suspicious activity
   - Process withdrawals

2. **Monitor Account Balances**
   - Check for negative balances
   - Investigate large transactions
   - Verify deposit documentation

3. **Cashier Support**
   - Help cashiers with transaction issues
   - Resolve payment code problems
   - Handle customer complaints

#### Pay Code Management
1. **Generating Pay Codes**
   - Go to Banking → Pay Codes → Generate
   - Set amount and expiration
   - Share code with recipient
   - Monitor redemption

2. **Troubleshooting Pay Codes**
   - Check if code is valid
   - Verify expiration dates
   - Help users redeem codes
   - Cancel unused codes

## Identifying and Escalating Technical Issues

### When to Escalate to Technical Support

#### Immediate Escalation (Call/Text Right Away)
- **Website is completely down** - users can't access the site
- **Login system not working** - nobody can log in
- **Banking system errors** - transactions failing or balances wrong
- **Security concerns** - suspicious activity or potential breach
- **Database errors** - error messages mentioning database problems

#### Same-Day Escalation (Email/Ticket)
- **Slow website performance** - pages taking more than 10 seconds to load
- **Broken features** - specific functions not working properly
- **Email system issues** - emails not being sent
- **Payment processing problems** - shop checkout not working
- **File upload failures** - users can't upload images or documents

#### Next-Business-Day Escalation
- **Minor display issues** - formatting problems, missing images
- **Feature requests** - users asking for new functionality
- **Content management difficulties** - trouble updating pages
- **User experience improvements** - suggestions for better usability

### How to Report Issues Effectively

#### Information to Gather Before Calling
1. **What exactly happened?**
   - What were you trying to do?
   - What did you expect to happen?
   - What actually happened instead?

2. **When did it happen?**
   - Exact time and date
   - How long has it been happening?
   - Is it still happening now?

3. **Who is affected?**
   - Just you?
   - Specific users?
   - All users?

4. **Screenshots and Error Messages**
   - Take screenshots of any error messages
   - Copy exact error text
   - Note which browser you're using

#### Issue Report Template
```
Subject: [URGENT/HIGH/MEDIUM/LOW] - Brief description

What happened:
[Detailed description of the issue]

When it happened:
[Date and time]

Who is affected:
[You, specific users, all users]

Steps to reproduce:
1. [First step]
2. [Second step]
3. [Error occurs]

Error messages:
[Copy exact error text or attach screenshot]

Browser/Device:
[Chrome, Firefox, Safari, Mobile, etc.]

Additional notes:
[Any other relevant information]
```

### Basic Troubleshooting Steps

#### Before Calling Technical Support, Try These:

1. **Refresh the Page**
   - Press F5 or Ctrl+R (Cmd+R on Mac)
   - Sometimes temporary glitches resolve themselves

2. **Clear Browser Cache**
   - Chrome: Settings → Privacy → Clear browsing data
   - Firefox: Settings → Privacy → Clear Data
   - Safari: Develop → Empty Caches

3. **Try a Different Browser**
   - If it works in another browser, it's likely a browser-specific issue
   - Try Chrome, Firefox, Safari, or Edge

4. **Check Your Internet Connection**
   - Try visiting other websites
   - Test your internet speed
   - Restart your router if needed

5. **Try Incognito/Private Mode**
   - This helps identify if browser extensions are causing issues
   - Chrome: Ctrl+Shift+N
   - Firefox: Ctrl+Shift+P

#### If These Don't Work
- Document what you tried
- Include this information when contacting technical support
- Don't try anything more advanced - leave it to the experts

## Maintenance Schedules and Procedures

### Daily Maintenance (10 minutes)
- [ ] Check website accessibility
- [ ] Review admin dashboard for alerts
- [ ] Check support tickets
- [ ] Monitor user activity levels
- [ ] Review recent transactions

### Weekly Maintenance (30 minutes)
- [ ] Review user feedback and complaints
- [ ] Check for content that needs updating
- [ ] Review system performance
- [ ] Update news and announcements
- [ ] Clean up old support tickets

### Monthly Maintenance (1 hour)
- [ ] Review user accounts for inactive users
- [ ] Update event calendar
- [ ] Review and update shop inventory
- [ ] Check for broken links or outdated content
- [ ] Review security logs with technical team

### Quarterly Maintenance (2 hours)
- [ ] Full content audit
- [ ] User access review
- [ ] Policy and procedure updates
- [ ] Training needs assessment
- [ ] System performance review

## User Access Management

### Adding New Admin Users
1. **Go to Admin → Users → Create User**
2. **Fill in user details**
   - Username and email
   - Temporary password
   - Select "Admin" role
3. **Send login credentials securely**
4. **Have them change password on first login**

### Removing User Access
1. **For departing staff:**
   - Go to Admin → Users → [User]
   - Change role to "Regular User"
   - Or deactivate account entirely

2. **For security concerns:**
   - Immediately deactivate account
   - Contact technical support
   - Document the reason

### Managing Permissions
- **Admin**: Full access to everything
- **Cashier**: Banking and transaction access
- **Content Manager**: News and event management
- **Volunteer Lead**: Volunteer system access
- **Regular User**: Standard user features only

## Emergency Procedures

### Website Emergency Contacts
```
Primary Technical Contact:
Name: [Your technical person]
Phone: [Phone number]
Email: [Email address]
Available: [Hours/days]

Secondary Technical Contact:
Name: [Backup technical person]
Phone: [Phone number]
Email: [Email address]
Available: [Hours/days]

Hosting Provider:
Company: [Hosting company name]
Support Phone: [Support number]
Account Number: [Your account number]
```

### Emergency Communication Plan
1. **Assess the situation**
   - Is it affecting all users or just some?
   - Is it a security issue?
   - How urgent is it?

2. **Contact technical support**
   - Use the contact information above
   - Provide clear, detailed information
   - Ask for estimated resolution time

3. **Communicate with users**
   - Post on Discord/social media
   - Send email if possible
   - Update website status page
   - Be honest about the issue and timeline

4. **Document everything**
   - What happened
   - When it happened
   - What was done to fix it
   - How to prevent it in the future

### Sample User Communication Messages

#### For Website Downtime
```
"We're currently experiencing technical difficulties with the Bank of Styx website. 
Our technical team is working to resolve the issue as quickly as possible. 
We expect to have the site back online within [timeframe]. 
We apologize for any inconvenience and will update you as soon as we have more information."
```

#### For Banking System Issues
```
"We're aware of an issue affecting banking transactions on the website. 
For your security, we've temporarily disabled banking features while we investigate. 
Your account balances are safe and secure. 
We expect to restore full functionality within [timeframe]. 
Thank you for your patience."
```

#### For Resolved Issues
```
"The technical issues affecting the Bank of Styx website have been resolved. 
All systems are now functioning normally. 
If you continue to experience any problems, please contact support. 
Thank you for your patience during this time."
```

## Advanced Troubleshooting Guide

### Common Issues and Solutions

#### Issue: Users Can't Log In
**Symptoms:**
- Login page loads but login fails
- Users get "Invalid credentials" errors
- Login button doesn't respond

**Quick Checks:**
1. **Try logging in yourself** with a known good account
2. **Check if it's affecting all users** or just specific ones
3. **Look for error messages** in the admin dashboard

**Solutions:**
- If affecting all users: Contact technical support immediately
- If affecting specific users: Try resetting their password
- If login page won't load: Check if website is down

#### Issue: Banking Balances Not Updating
**Symptoms:**
- Transactions processed but balances don't change
- Users report incorrect balances
- Cashier transactions not reflecting

**Quick Checks:**
1. **Check recent transactions** in Admin → Banking → Transactions
2. **Verify cashier reports** match system records
3. **Look for pending transactions** that might be stuck

**Solutions:**
- Don't attempt to manually adjust balances
- Document specific cases with user IDs and amounts
- Contact technical support with detailed information
- Advise users to avoid making transactions until resolved

#### Issue: Website Running Slowly
**Symptoms:**
- Pages take more than 10 seconds to load
- Users complain about slow performance
- Admin dashboard is sluggish

**Quick Checks:**
1. **Test from different devices** and internet connections
2. **Check if it's specific pages** or the entire site
3. **Note the time of day** (peak usage times may be slower)

**Solutions:**
- If affecting everyone: Contact technical support
- If only affecting some users: Advise them to clear browser cache
- If only during peak times: May be normal, but document for review

#### Issue: File Uploads Not Working
**Symptoms:**
- Users can't upload profile pictures
- News article images won't upload
- Error messages about file size or type

**Quick Checks:**
1. **Try uploading a small image yourself** (under 1MB)
2. **Check if it's specific file types** or all files
3. **Note any error messages** exactly as they appear

**Solutions:**
- Advise users to try smaller files (under 5MB)
- Ensure files are common formats (JPG, PNG, GIF)
- If problem persists, contact technical support

### User Account Troubleshooting

#### Password Reset Issues
**When users can't reset passwords:**

1. **Check their email address** is correct in the system
2. **Ask them to check spam/junk folders**
3. **Try sending a test email** from admin panel
4. **If emails aren't working**, contact technical support

**Manual password reset process:**
1. Go to Admin → Users
2. Find the user account
3. Click "Reset Password"
4. Provide new temporary password securely
5. Instruct user to change password on first login

#### Account Verification Problems
**When new users can't verify accounts:**

1. **Check if verification email was sent**
2. **Verify email address is correct**
3. **Check if account is already verified**
4. **Manually verify if needed**: Admin → Users → [User] → Verify Account

#### Discord Integration Issues
**When Discord login doesn't work:**

1. **Check if user has Discord account**
2. **Verify they're using correct Discord credentials**
3. **Check if Discord integration is working** (try it yourself)
4. **If broken for everyone**, contact technical support immediately

### Content Management Troubleshooting

#### News Article Problems
**When articles won't publish:**

1. **Check all required fields** are filled
2. **Verify article content** isn't too long
3. **Check if images are uploaded** properly
4. **Try saving as draft first**, then publishing

**When articles display incorrectly:**

1. **Check formatting** in the editor
2. **Verify images are showing** correctly
3. **Test on different devices** and browsers
4. **Re-edit and save** if formatting is broken

#### Event Management Issues
**When events won't save:**

1. **Check date and time** are in the future
2. **Verify capacity limits** are reasonable numbers
3. **Ensure all required fields** are completed
4. **Check for conflicting events** at same time

**When registrations aren't working:**

1. **Check if event is published** and visible
2. **Verify capacity hasn't been reached**
3. **Check if registration deadline** has passed
4. **Test registration process** yourself

### Banking System Troubleshooting

#### Pay Code Issues
**When pay codes won't generate:**

1. **Check amount is valid** (positive number)
2. **Verify expiration date** is in the future
3. **Ensure you have permission** to generate codes
4. **Try with smaller amount** to test

**When pay codes won't redeem:**

1. **Check if code is still valid** (not expired)
2. **Verify code hasn't been used** already
3. **Check if user has permission** to redeem
4. **Try the code yourself** if possible

#### Transaction Problems
**When transactions fail:**

1. **Check user account status** (not suspended)
2. **Verify sufficient balance** for withdrawals
3. **Check transaction limits** haven't been exceeded
4. **Look for duplicate transactions**

### Performance Monitoring

#### Daily Performance Checks
**What to monitor daily:**

1. **Page load times** - Should be under 5 seconds
2. **User complaints** - Note any performance issues
3. **Error messages** - Document any new errors
4. **Peak usage times** - Note when site is busiest

**How to check performance:**
1. **Time how long pages take** to load
2. **Test on mobile devices** as well as desktop
3. **Check different sections** of the site
4. **Note any specific slow pages**

#### When to Escalate Performance Issues
- **Pages taking over 10 seconds** to load
- **Multiple users complaining** about speed
- **Specific features not working** due to slowness
- **Error messages appearing** frequently

---

*Remember: When in doubt, ask for help. It's better to escalate an issue unnecessarily than to miss a critical problem.*
