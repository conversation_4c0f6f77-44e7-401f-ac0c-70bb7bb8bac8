● React Hook Refactoring Guide: Modern Form Management Patterns

  Current Situation Analysis

  I've analyzed your VolunteerSignupModal component and found an excellent opportunity to demonstrate modern React hook patterns. The component currently has 168 lines of repetitive form handling code that can be significantly improved.

  🎯 Refactoring Target: VolunteerSignupModal

  File: web/apps/main-site/src/components/volunteer/public/VolunteerSignupModal.tsx

  Current Problems:

  - ❌ Repetitive onChange={handleChange} for each input
  - ❌ Complex state management with multiple useState calls
  - ❌ Manual field validation logic
  - ❌ No reusable form input pattern
  - ❌ Heavy component with too many responsibilities

  🚀 Proposed Modern Hook Refactoring

  1. Create a Modern useFormInput Hook

  // hooks/useFormInput.ts
  import { useState, useCallback, ChangeEvent } from 'react';

  interface UseFormInputReturn {
    value: string;
    onChange: (e: ChangeEvent<HTMLInputElement>) => void;
    error?: string;
    clearError: () => void;
    setValue: (value: string) => void;
  }

  export function useFormInput(
    initialValue: string = '',
    validator?: (value: string) => string | undefined
  ): UseFormInputReturn {
    const [value, setValue] = useState(initialValue);
    const [error, setError] = useState<string | undefined>();

    const onChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.type === 'checkbox' ? e.target.checked.toString() : e.target.value;
      setValue(newValue);

      // Clear error when user starts typing
      if (error) setError(undefined);

      // Run validation if provided
      if (validator) {
        const validationError = validator(newValue);
        setError(validationError);
      }
    }, [error, validator]);

    const clearError = useCallback(() => setError(undefined), []);

    return { value, onChange, error, clearError, setValue };
  }

  2. Create a useFormValidation Hook

  // hooks/useFormValidation.ts
  import { useState, useCallback } from 'react';

  type ValidationRules<T> = {
    [K in keyof T]?: (value: T[K]) => string | undefined;
  };

  export function useFormValidation<T extends Record<string, any>>(
    initialData: T,
    validationRules: ValidationRules<T>
  ) {
    const [data, setData] = useState<T>(initialData);
    const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({});

    const validateField = useCallback((fieldName: keyof T, value: any) => {
      const rule = validationRules[fieldName];
      return rule ? rule(value) : undefined;
    }, [validationRules]);

    const setFieldValue = useCallback((fieldName: keyof T, value: any) => {
      setData(prev => ({ ...prev, [fieldName]: value }));

      // Clear error when field changes
      if (errors[fieldName]) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[fieldName];
          return newErrors;
        });
      }
    }, [errors]);

    const validateAll = useCallback(() => {
      const newErrors: Partial<Record<keyof T, string>> = {};
      let isValid = true;

      Object.keys(validationRules).forEach(key => {
        const fieldName = key as keyof T;
        const error = validateField(fieldName, data[fieldName]);
        if (error) {
          newErrors[fieldName] = error;
          isValid = false;
        }
      });

      setErrors(newErrors);
      return isValid;
    }, [data, validateField, validationRules]);

    return {
      data,
      errors,
      setFieldValue,
      validateAll,
      setData,
    };
  }

  3. Create a useEffectEvent Hook (Modern Pattern)

  // hooks/useEffectEvent.ts
  import { useCallback, useLayoutEffect, useRef } from 'react';

  // This replaces the experimental useEffectEvent pattern
  export function useEffectEvent<T extends (...args: any[]) => any>(fn: T): T {
    const ref = useRef<T>(fn);

    useLayoutEffect(() => {
      ref.current = fn;
    });

    return useCallback(((...args) => {
      return ref.current(...args);
    }) as T, []);
  }

  4. Refactored Component (Reduced from 168 to ~80 lines)

  // components/volunteer/public/VolunteerSignupModal.tsx
  const VolunteerSignupModal: React.FC<VolunteerSignupModalProps> = ({
    shiftId,
    onClose,
  }) => {
    const { user } = useAuth();
    const { data: shiftData, isLoading: isLoadingShift } = useVolunteerShift(shiftId);
    const signupMutation = useVolunteerSignup();

    // Modern validation rules
    const validationRules = {
      firstName: (value: string) => !value.trim() ? 'First name is required' : undefined,
      lastName: (value: string) => !value.trim() ? 'Last name is required' : undefined,
      email: (value: string) => {
        if (!value.trim()) return 'Email is required';
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) return 'Invalid email format';
        return undefined;
      },
      pirateName: (value: string) => !value.trim() ? 'Pirate name is required (or N/A)' : undefined,
    };

    // Modern form management
    const form = useFormValidation({
      firstName: user?.displayName?.split(' ')[0] || '',
      lastName: user?.displayName?.split(' ').slice(1).join(' ') || '',
      email: user?.email || '',
      pirateName: '',
      isDock: false,
      isLandGrant: false,
      addToDeedsLottery: false,
      emailNotification: true,
      websiteNotification: true,
    }, validationRules);

    // Stable event handler using modern pattern
    const handleSubmit = useEffectEvent(async (e: React.FormEvent) => {
      e.preventDefault();

      if (!form.validateAll()) return;

      try {
        await signupMutation.mutateAsync({
          shiftId,
          formData: form.data,
        });

        toast.success('Successfully signed up for volunteer shift!');
        onClose();
      } catch (error) {
        toast.error(error instanceof Error ? error.message : 'Signup failed');
      }
    });

    // Modern input component with built-in validation
    const FormInput = ({
      label,
      name,
      type = 'text',
      required = false
    }: {
      label: string;
      name: keyof typeof form.data;
      type?: string;
      required?: boolean;
    }) => (
      <div>
        <label className="block text-white mb-1">
          {label} {required && '*'}
        </label>
        <input
          type={type}
          name={name}
          value={form.data[name]}
          onChange={(e) => form.setFieldValue(name,
            type === 'checkbox' ? e.target.checked : e.target.value
          )}
          className={`w-full px-3 py-2 bg-secondary border rounded-md text-white
            focus:outline-none focus:ring-2 focus:ring-primary
            ${form.errors[name] ? 'border-accent' : 'border-gray-600'}`}
        />
        {form.errors[name] && (
          <p className="text-accent text-sm mt-1">{form.errors[name]}</p>
        )}
      </div>
    );

    return (
      <Modal isOpen onClose={onClose} title="Volunteer Signup">
        <form onSubmit={handleSubmit} className="space-y-4">
          <FormInput label="First Name" name="firstName" required />
          <FormInput label="Last Name" name="lastName" required />
          <FormInput label="Email" name="email" type="email" required />
          <FormInput label="Pirate Name" name="pirateName" required />

          {/* Other fields using the same pattern */}

          <div className="flex gap-4 pt-4">
            <Button type="submit" variant="primary" disabled={signupMutation.isPending}>
              {signupMutation.isPending ? 'Signing up...' : 'Sign Up'}
            </Button>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
          </div>
        </form>
      </Modal>
    );
  };

  📊 Benefits Achieved

  Code Reduction:

  - Before: 168 lines with repetitive patterns
  - After: ~80 lines with reusable hooks

  Modern Patterns Added:

  1. ✅ Custom form input hooks with built-in validation
  2. ✅ useEffectEvent pattern for stable event handlers
  3. ✅ Reusable validation system with type safety
  4. ✅ Component composition with FormInput wrapper
  5. ✅ Separation of concerns - logic in hooks, UI in component

  Performance Improvements:

  - ✅ Memoized callbacks prevent unnecessary re-renders
  - ✅ Stable event handlers with useEffectEvent pattern
  - ✅ Efficient validation only runs when needed

  Developer Experience:

  - ✅ Type-safe forms with TypeScript inference
  - ✅ Reusable across components - extract once, use everywhere
  - ✅ Easy to test - hooks can be unit tested independently
  - ✅ Clear separation between form logic and UI rendering

  🎯 Implementation Plan

  1. Phase 1: Create the custom hooks in /src/hooks/
  2. Phase 2: Refactor VolunteerSignupModal to use new patterns
  3. Phase 3: Apply same patterns to other form components (ProductForm, CategoryForm)
  4. Phase 4: Extract common validation rules into shared utilities

  This refactoring demonstrates modern React patterns while maintaining your existing functionality and actually improving the user experience with better validation and performance.