# Authentication System

## Overview
The authentication system uses a dual credential approach with JWT tokens and supports both traditional email/password and Discord OAuth.

## Key Patterns

### Dual Credential System
The system maintains both new credential records and legacy user password hashes for backward compatibility:

```typescript
// New credential-based approach
const credential = await prisma.userCredential.findFirst({
  where: {
    type: "email",
    identifier: email,
  },
  include: {
    user: true,
  },
});

// Legacy fallback
const user = await prisma.user.findUnique({
  where: { email },
});
```

### JWT Token Structure
- **Secret**: Uses `process.env.JWT_SECRET` or fallback development key
- **Expiration**: 7 days
- **Payload**: `{ id: user.id, email: user.email }`

### Auth Token for SSE
SSE connections require auth tokens passed via query parameter since EventSource doesn't support custom headers:

```typescript
// Query parameter auth (primary for SSE)
const url = new URL(req.url);
token = url.searchParams.get("auth_token");

// Fallback to Authorization header
const authHeader = req.headers.get("authorization");
if (authHeader && authHeader.startsWith("Bearer ")) {
  token = authHeader.split(" ")[1];
}
```

### User Data Structure
The login response includes comprehensive user data:

```typescript
{
  user: {
    id, username, displayName, email, avatar, balance,
    isEmailVerified,
    preferences: {
      defaultView,
      notifications: { transfers, deposits, withdrawals, newsAndEvents }
    },
    connectedAccounts: { discord, discordId, facebook, facebookId },
    merchant: { status, merchantId, slug },
    auctions: { hasCreated, auctionCount },
    roles: { admin, editor, banker, chatModerator, volunteerCoordinator, leadManager, salesManager }
  },
  token
}
```

### Discord OAuth Integration
- Uses Discord OAuth with profile sync
- Stores Discord ID and connection status
- Supports avatar synchronization from Discord

### CORS Handling
All API routes include CORS headers via middleware:

```typescript
response.headers.set("Access-Control-Allow-Origin", "*");
response.headers.set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization");
```

## Important Files
- `src/app/api/auth/login/route.ts` - Main login endpoint
- `src/lib/auth.ts` - Token verification utilities
- `middleware.ts` - CORS and request handling
- `src/app/api/auth/discord/` - Discord OAuth flow