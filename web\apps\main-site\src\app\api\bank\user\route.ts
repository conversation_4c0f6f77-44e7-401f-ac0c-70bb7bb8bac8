import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../lib/prisma";
import jwt from "jsonwebtoken";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
const JWT_SECRET = process.env.JWT_SECRET || "your-development-secret-key";

// Helper function to verify token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

export const GET = async (req: NextRequest) => {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { id: decoded.id as string },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Return user data (excluding password)
    return NextResponse.json({
      id: user.id,
      username: user.username,
      displayName: user.displayName,
      email: user.email,
      avatar: user.avatar,
      balance: user.balance,
      isEmailVerified: user.isEmailVerified,
      preferences: {
        defaultView: user.defaultView,
        notifications: {
          transfers: user.notifyTransfers,
          deposits: user.notifyDeposits,
          withdrawals: user.notifyWithdrawals,
          newsAndEvents: user.notifyNewsEvents,
        },
      },
      connectedAccounts: {
        discord: user.discordConnected,
        discordId: user.discordId,
        facebook: user.facebookConnected,
        facebookId: user.facebookId,
      },
      merchant: {
        status: user.merchantStatus,
        merchantId: user.merchantId,
        slug: user.merchantSlug,
      },
      auctions: {
        hasCreated: user.hasCreatedAuctions,
        auctionCount: user.auctionCount,
      },
      roles: {
        admin: user.isAdmin,
        editor: user.isEditor,
        banker: user.isBanker,
        chatModerator: user.isChatModerator,
        volunteerCoordinator: user.isVolunteerCoordinator,
        leadManager: user.isLeadManager,
      },
    });
  } catch (error) {
    console.error("Error fetching user data:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
