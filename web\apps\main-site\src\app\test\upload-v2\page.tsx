"use client";

import React, { useState } from "react";
import UniversalImageUploader from "@/components/common/UniversalImageUploader";
import AvatarUploaderV2 from "@/components/upload/AvatarUploaderV2";
import NewsImageUploaderV2 from "@/components/upload/NewsImageUploaderV2";
import ProductImageUploader from "@/components/upload/ProductImageUploader";
import DepositReceiptUploader from "@/components/upload/DepositReceiptUploader";
import EventImageUploader from "@/components/upload/EventImageUploader";
import { UploadResponse, UploadType } from "@/types/upload";
import { uploadConfig } from "@/lib/uploadConfig";

export default function UploadTestPage() {
  const [uploadResults, setUploadResults] = useState<UploadResponse[]>([]);
  const [selectedType, setSelectedType] = useState<UploadType>("avatar");

  const handleUploadComplete = (response: UploadResponse) => {
    console.log("Upload complete:", response);
    setUploadResults((prev) => [response, ...prev]);
  };

  const handleUploadStart = () => {
    console.log("Upload started");
  };

  return (
    <div className="max-w-4xl mx-auto p-8 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Upload System V2 Test Page
        </h1>
        <p className="text-gray-600">
          Test the new unified upload system with different upload types
        </p>
      </div>

      {/* Test Configuration Display */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Upload Configuration</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Object.entries(uploadConfig).map(([type, config]) => (
            <div key={type} className="bg-white p-4 rounded border">
              <h3 className="font-medium text-gray-900 capitalize">{type}</h3>
              <p className="text-sm text-gray-600">
                Max: {(config.maxSize / (1024 * 1024)).toFixed(1)}MB
              </p>
              <p className="text-xs text-gray-500">
                Types: {config.allowedTypes.join(", ")}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Universal Uploader Test */}
      <div className="bg-white p-6 rounded-lg border">
        <h2 className="text-xl font-semibold mb-4">
          Universal Image Uploader Test
        </h2>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Upload Type:
          </label>
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value as UploadType)}
            className="border rounded px-3 py-2"
          >
            <option value="avatar">Avatar</option>
            <option value="news">News</option>
            <option value="event">Event</option>
            <option value="product">Product</option>
            <option value="deposit">Deposit</option>
          </select>
        </div>

        <UniversalImageUploader
          uploadType={selectedType}
          onUploadComplete={handleUploadComplete}
          onUploadStart={handleUploadStart}
          entityId="test-entity-123"
          options={uploadConfig[selectedType]}
          className="max-w-md"
        />
      </div>

      {/* Avatar Uploader V2 Test */}
      <div className="bg-white p-6 rounded-lg border">
        <h2 className="text-xl font-semibold mb-4">Avatar Uploader V2 Test</h2>
        <AvatarUploaderV2
          userId="test-user-123"
          onUploadComplete={handleUploadComplete}
          userInitials="TU"
          className="max-w-md mx-auto"
        />
      </div>

      {/* News Image Uploader Test */}
      <div className="bg-white p-6 rounded-lg border">
        <h2 className="text-xl font-semibold mb-4">
          News Image Uploader V2 Test
        </h2>
        <NewsImageUploaderV2
          articleId="test-article-123"
          onUploadComplete={handleUploadComplete}
          onUploadStart={handleUploadStart}
          className="max-w-md mx-auto"
        />
      </div>

      {/* Product Image Uploader Test */}
      <div className="bg-white p-6 rounded-lg border">
        <h2 className="text-xl font-semibold mb-4">
          Product Image Uploader Test
        </h2>
        <ProductImageUploader
          productId="test-product-123"
          onUploadComplete={handleUploadComplete}
          onUploadStart={handleUploadStart}
          className="max-w-md mx-auto"
        />
      </div>

      {/* Event Image Uploader Test */}
      <div className="bg-white p-6 rounded-lg border">
        <h2 className="text-xl font-semibold mb-4">
          Event Image Uploader Test
        </h2>
        <EventImageUploader
          eventId="test-event-123"
          onUploadComplete={handleUploadComplete}
          onUploadStart={handleUploadStart}
          imageType="featured"
          className="max-w-md mx-auto"
        />
      </div>

      {/* Deposit Receipt Uploader Test */}
      <div className="bg-white p-6 rounded-lg border">
        <h2 className="text-xl font-semibold mb-4">
          Deposit Receipt Uploader Test
        </h2>
        <DepositReceiptUploader
          depositId="test-deposit-123"
          onUploadComplete={handleUploadComplete}
          onUploadStart={handleUploadStart}
          className="max-w-md mx-auto"
        />
      </div>

      {/* Upload Results */}
      <div className="bg-white p-6 rounded-lg border">
        <h2 className="text-xl font-semibold mb-4">Upload Results</h2>
        {uploadResults.length === 0 ? (
          <p className="text-gray-500 italic">No uploads yet</p>
        ) : (
          <div className="space-y-3">
            {uploadResults.map((result, index) => (
              <div
                key={index}
                className={`p-4 rounded border-l-4 ${
                  result.success
                    ? "border-green-500 bg-green-50"
                    : "border-red-500 bg-red-50"
                }`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <span
                      className={`inline-block px-2 py-1 rounded text-xs font-medium ${
                        result.success
                          ? "bg-green-100 text-green-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {result.success ? "Success" : "Error"}
                    </span>
                    {result.message && (
                      <p className="mt-1 text-sm text-gray-700">
                        {result.message}
                      </p>
                    )}
                  </div>
                  <span className="text-xs text-gray-500">
                    {new Date().toLocaleTimeString()}
                  </span>
                </div>

                {result.success && result.file && (
                  <div className="mt-2 grid grid-cols-2 gap-2 text-xs text-gray-600">
                    <div>ID: {result.file.id}</div>
                    <div>Type: {result.file.uploadType}</div>
                    <div>Size: {(result.file.size / 1024).toFixed(1)}KB</div>
                    <div>URL: {result.file.url}</div>
                    {result.file.dimensions && (
                      <div className="col-span-2">
                        Dimensions: {result.file.dimensions.width}x
                        {result.file.dimensions.height}
                      </div>
                    )}
                  </div>
                )}

                {result.error && (
                  <div className="mt-2 text-sm text-red-700">
                    Error: {result.error}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* API Test Section */}
      <div className="bg-blue-50 p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">
          Phase 1 Completion Status
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="font-medium text-green-800 mb-2">✅ Completed</h3>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• Unified API endpoint (/api/uploads/v2)</li>
              <li>• Type-based routing and validation</li>
              <li>• Enhanced backend services</li>
              <li>• Progress tracking system</li>
              <li>• Universal frontend components</li>
              <li>• Database schema enhancements</li>
              <li>• Image processing pipeline</li>
            </ul>
          </div>
          <div>
            <h3 className="font-medium text-blue-800 mb-2">
              🔄 Ready for Phase 2
            </h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Component migration can begin</li>
              <li>• Gradual rollout strategy</li>
              <li>• Test each migration</li>
              <li>• Rollback capability maintained</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
