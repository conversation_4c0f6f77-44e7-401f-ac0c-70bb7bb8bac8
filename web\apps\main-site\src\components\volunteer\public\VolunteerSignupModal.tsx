"use client";

import React, { useState, useEffect } from "react";
import { format } from "date-fns";
import { toast } from "react-hot-toast";
import { useAuth } from "@/contexts/AuthContext";
import { But<PERSON>, Modal } from "@bank-of-styx/ui";
import {
  useVolunteerShift,
  useVolunteerSignup,
  useUserShip,
  VolunteerSignupFormData,
  Ship,
} from "@/hooks/usePublicVolunteer";
import { ShipSearch } from "./ShipSearch";
import { useFormValidation } from "@/hooks/useFormValidation";
import { useEffectEvent } from "@/hooks/useEffectEvent";

interface VolunteerSignupModalProps {
  shiftId: string;
  onClose: () => void;
}

export const VolunteerSignupModal: React.FC<VolunteerSignupModalProps> = ({
  shiftId,
  onClose,
}) => {
  const { user } = useAuth();
  const { data: shiftData, isLoading: isLoadingShift } = useVolunteerShift(shiftId);
  const { data: userShipData } = useUserShip();
  const signupMutation = useVolunteerSignup();

  const [stayingWithShip, setStayingWithShip] = useState<Ship | null>(userShipData?.ship || null);
  const [landGrantCreditShip, setLandGrantCreditShip] = useState<Ship | null>(null);

  // Modern validation rules
  const validationRules = {
    firstName: (value: string) => !value.trim() ? 'First name is required' : undefined,
    lastName: (value: string) => !value.trim() ? 'Last name is required' : undefined,
    email: (value: string) => {
      if (!value.trim()) return 'Email is required';
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) return 'Invalid email format';
      return undefined;
    },
    pirateName: (value: string) => !value.trim() ? 'Pirate name is required (or N/A)' : undefined,
    stayingWith: (value: string) => !value.trim() && !stayingWithShip ? 'This field is required (or N/A)' : undefined,
    landGrantCredit: (value: string) => {
      const form = formRef.current;
      if ((form?.isDock || form?.isLandGrant) && !value.trim() && !landGrantCreditShip) {
        return 'Please specify who gets credit for these hours';
      }
      return undefined;
    },
    checkedAutofill: (value: boolean) => !value ? 'Please confirm you\'ve checked your information' : undefined,
  };

  // Modern form management
  const form = useFormValidation<VolunteerSignupFormData>({
    firstName: user?.displayName?.split(' ')[0] || '',
    lastName: user?.displayName?.split(' ').slice(1).join(' ') || '',
    email: user?.email || '',
    pirateName: '',
    stayingWith: userShipData?.ship?.name || '',
    stayingWithShipId: userShipData?.ship?.id,
    isDock: false,
    isLandGrant: false,
    landGrantCredit: '',
    landGrantCreditShipId: undefined,
    checkedAutofill: false,
    pronouns: '',
    addToDeedsLottery: false,
    emailNotification: true,
    websiteNotification: true,
  }, validationRules);

  // Keep a ref for validation rules that need access to current form state
  const formRef = React.useRef(form.data);
  formRef.current = form.data;

  // Format date for display
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "EEEE, MMMM d, yyyy");
  };

  // Pre-fill form with user data if available
  useEffect(() => {
    if (user) {
      form.setData(prev => ({
        ...prev,
        firstName: user.displayName?.split(' ')[0] || prev.firstName,
        lastName: user.displayName?.split(' ').slice(1).join(' ') || prev.lastName,
        email: user.email || prev.email,
      }));
    }
  }, [user, form]);

  // Update form data when ship selections change
  useEffect(() => {
    if (userShipData?.ship && !stayingWithShip) {
      setStayingWithShip(userShipData.ship);
    }
  }, [userShipData, stayingWithShip]);

  useEffect(() => {
    form.setData(prev => ({
      ...prev,
      stayingWith: stayingWithShip?.name || '',
      stayingWithShipId: stayingWithShip?.id,
    }));
  }, [stayingWithShip, form]);

  useEffect(() => {
    form.setData(prev => ({
      ...prev,
      landGrantCredit: landGrantCreditShip?.name || '',
      landGrantCreditShipId: landGrantCreditShip?.id,
    }));
  }, [landGrantCreditShip, form]);

  // Stable event handler using modern pattern
  const handleSubmit = useEffectEvent(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!form.validateAll()) return;

    try {
      await signupMutation.mutateAsync({
        shiftId,
        formData: form.data,
      });

      toast.success('Successfully signed up for volunteer shift!');
      onClose();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Signup failed');
    }
  });

  // Modern input component with built-in validation
  const FormInput = ({
    label,
    name,
    type = 'text',
    required = false,
    disabled = false
  }: {
    label: string;
    name: keyof typeof form.data;
    type?: string;
    required?: boolean;
    disabled?: boolean;
  }) => (
    <div>
      <label className="block text-white mb-1">
        {label} {required && '*'}
      </label>
      {type === 'checkbox' ? (
        <label className="flex items-center text-white">
          <input
            type="checkbox"
            name={name}
            checked={!!form.data[name]}
            onChange={(e) => form.setFieldValue(name, e.target.checked)}
            disabled={disabled}
            className={`mr-2 h-4 w-4 rounded border-gray-600 text-primary focus:ring-primary ${
              form.errors[name] ? 'border-accent' : ''
            }`}
          />
          {label}
        </label>
      ) : (
        <input
          type={type}
          name={name}
          value={form.data[name]?.toString() || ''}
          onChange={(e) => form.setFieldValue(name, e.target.value)}
          disabled={disabled}
          className={`w-full px-3 py-2 bg-secondary border rounded-md text-white
            focus:outline-none focus:ring-2 focus:ring-primary
            ${form.errors[name] ? 'border-accent' : 'border-gray-600'}`}
        />
      )}
      {form.errors[name] && (
        <p className="text-accent text-sm mt-1">{form.errors[name]}</p>
      )}
    </div>
  );

  if (isLoadingShift) {
    return (
      <Modal isOpen onClose={onClose} title="Loading..." size="sm">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          <p className="ml-2 text-white">Loading shift details...</p>
        </div>
      </Modal>
    );
  }

  const shift = shiftData?.shift;
  if (!shift) {
    return null;
  }

  return (
    <Modal isOpen onClose={onClose} title="Volunteer Signup" size="lg">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-white mb-2">
          You are signing up for... {shift.title} on {formatDate(shift.startTime)}
        </h3>
        <p className="text-gray-400">
          For NS £{shift.category.payRate.toFixed(2)}/hr
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <FormInput label="First Name" name="firstName" required />
        <FormInput label="Last Name" name="lastName" required />
        <FormInput label="E-mail" name="email" type="email" required />
        <FormInput label="Pronouns/Preferred Name" name="pronouns" />
        <FormInput
          label="Pirate Name (Don't have one yet? Say NA)"
          name="pirateName"
          required
        />

        {/* Staying With - Ship Search */}
        <div>
          <label className="block text-white mb-1">
            Who you are staying with, If Open Camping say "N/A" *
          </label>
          <ShipSearch
            value={stayingWithShip}
            onChange={setStayingWithShip}
            placeholder="Search for ship or type N/A"
            allowNone={true}
            error={form.errors.stayingWith}
          />
          {form.errors.stayingWith && (
            <p className="text-accent text-sm mt-1">{form.errors.stayingWith}</p>
          )}
        </div>

        {/* Land Grant/Dock */}
        <div>
          <label className="block text-white mb-2">
            Land Grant/Dock - Are these Land Grant/Dock hours? (click yes or
            no. Unsure? Click "no")
          </label>
          <div className="flex space-x-4">
            <label className="flex items-center text-white">
              <span className="mr-2">Dock:</span>
              <input
                type="checkbox"
                name="isDock"
                checked={form.data.isDock}
                onChange={(e) => form.setFieldValue('isDock', e.target.checked)}
                className="h-4 w-4 rounded border-gray-600 text-primary focus:ring-primary"
              />
            </label>
            <label className="flex items-center text-white">
              <span className="mr-2">Land Grant:</span>
              <input
                type="checkbox"
                name="isLandGrant"
                checked={form.data.isLandGrant}
                onChange={(e) => form.setFieldValue('isLandGrant', e.target.checked)}
                className="h-4 w-4 rounded border-gray-600 text-primary focus:ring-primary"
              />
            </label>
          </div>
        </div>

        {/* Land Grant Credit */}
        <div
          className={
            !form.data.isDock && !form.data.isLandGrant ? "opacity-50" : ""
          }
        >
          <label className="block text-white mb-1">
            Who gets credit for your Land Grant/Dock hours? (N/A if not Land
            Grant Hours)
          </label>
          <ShipSearch
            value={landGrantCreditShip}
            onChange={setLandGrantCreditShip}
            placeholder="Search for ship or type N/A"
            allowNone={true}
            disabled={!form.data.isDock && !form.data.isLandGrant}
            error={form.errors.landGrantCredit}
          />
          {form.errors.landGrantCredit && (
            <p className="text-accent text-sm mt-1">
              {form.errors.landGrantCredit}
            </p>
          )}
        </div>

        <FormInput 
          label="Add these hours to my Deeds Card Lottery" 
          name="addToDeedsLottery" 
          type="checkbox" 
        />

        {/* Notifications */}
        <div className="space-y-2">
          <label className="block text-white">Notifications</label>
          <div className="flex flex-col space-y-2">
            <FormInput 
              label="Get notified by email 24 hours before" 
              name="emailNotification" 
              type="checkbox" 
            />
            <FormInput 
              label="Get a notification on the website 24 hours before" 
              name="websiteNotification" 
              type="checkbox" 
            />
          </div>
        </div>

        <FormInput 
          label="Did you check to make sure Autofill hasn't entered garbage?" 
          name="checkedAutofill" 
          type="checkbox" 
          required 
        />

        <div className="flex justify-end space-x-3 pt-4">
          <Button type="button" onClick={onClose} variant="outline">
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={signupMutation.isPending}
          >
            {signupMutation.isPending ? 'Signing up...' : 'Sign Up'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
