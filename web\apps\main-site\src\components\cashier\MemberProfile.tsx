import React from "react";
import { BankUser } from "../../services/bankService";

export interface MemberProfileProps {
  member: BankUser;
}

export const MemberProfile: React.FC<MemberProfileProps> = ({ member }) => {
  return (
    <div className="bg-secondary-light rounded-lg shadow-md p-6 border border-gray-600 mb-6">
      <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
        {/* Avatar */}
        <div className="flex-shrink-0">
          <img
            src={member.avatar || "/images/avatars/default.png"}
            alt={member.displayName}
            className="w-24 h-24 rounded-full object-cover border-2 border-primary"
          />
        </div>

        {/* Basic Info */}
        <div className="flex-grow">
          <h2 className="text-2xl font-bold text-white">
            {member.displayName}
          </h2>
          <p className="text-gray-400 mb-2">@{member.username}</p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <p className="text-gray-400 text-sm">Email:</p>
              <p className="text-white">{member.email}</p>
            </div>
            <div>
              <p className="text-gray-400 text-sm">Email Verified:</p>
              <p className="text-white">
                {member.isEmailVerified ? (
                  <span className="text-success">Yes</span>
                ) : (
                  <span className="text-error">No</span>
                )}
              </p>
            </div>
            <div>
              <p className="text-gray-400 text-sm">Current Balance:</p>
              <p className="text-success font-bold">
                NS {member.balance.toFixed(0)}
              </p>
            </div>
            <div>
              <p className="text-gray-400 text-sm">Account Type:</p>
              <p className="text-white">
                {member.roles?.admin
                  ? "Admin"
                  : member.roles?.banker
                  ? "Banker"
                  : member.roles?.editor
                  ? "Editor"
                  : member.merchant?.status === "approved"
                  ? "Merchant"
                  : "Standard"}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Connected Accounts */}
      <div className="mt-6 pt-6 border-t border-gray-600">
        <h3 className="text-lg font-semibold text-white mb-3">
          Connected Accounts
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center">
            <div
              className={`w-3 h-3 rounded-full mr-2 ${
                member.connectedAccounts.discord ? "bg-success" : "bg-error"
              }`}
            ></div>
            <span className="text-white">Discord</span>
            {member.connectedAccounts.discordId && (
              <span className="text-gray-400 ml-2">
                ({member.connectedAccounts.discordId})
              </span>
            )}
          </div>
          <div className="flex items-center">
            <div
              className={`w-3 h-3 rounded-full mr-2 ${
                member.connectedAccounts.facebook ? "bg-success" : "bg-error"
              }`}
            ></div>
            <span className="text-white">Facebook</span>
            {member.connectedAccounts.facebookId && (
              <span className="text-gray-400 ml-2">
                ({member.connectedAccounts.facebookId})
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Merchant Info (if applicable) */}
      {member.merchant && member.merchant.status !== "none" && (
        <div className="mt-6 pt-6 border-t border-gray-600">
          <h3 className="text-lg font-semibold text-white mb-3">
            Merchant Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-gray-400 text-sm">Merchant Status:</p>
              <p
                className={`font-medium ${
                  member.merchant.status === "approved"
                    ? "text-success"
                    : member.merchant.status === "pending"
                    ? "text-warning"
                    : "text-error"
                }`}
              >
                {member.merchant.status.charAt(0).toUpperCase() +
                  member.merchant.status.slice(1)}
              </p>
            </div>
            {member.merchant.merchantId && (
              <div>
                <p className="text-gray-400 text-sm">Merchant ID:</p>
                <p className="text-white">{member.merchant.merchantId}</p>
              </div>
            )}
            {member.merchant.slug && (
              <div>
                <p className="text-gray-400 text-sm">Merchant Slug:</p>
                <p className="text-white">{member.merchant.slug}</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Preferences */}
      <div className="mt-6 pt-6 border-t border-gray-600">
        <h3 className="text-lg font-semibold text-white mb-3">
          Account Preferences
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-gray-400 text-sm">Default Dashboard View:</p>
            <p className="text-white capitalize">
              {member.preferences.defaultView}
            </p>
          </div>
          <div>
            <p className="text-gray-400 text-sm">Notification Preferences:</p>
            <ul className="text-white">
              <li className="flex items-center">
                <div
                  className={`w-2 h-2 rounded-full mr-2 ${
                    member.preferences.notifications.transfers
                      ? "bg-success"
                      : "bg-error"
                  }`}
                ></div>
                <span>Transfers</span>
              </li>
              <li className="flex items-center">
                <div
                  className={`w-2 h-2 rounded-full mr-2 ${
                    member.preferences.notifications.deposits
                      ? "bg-success"
                      : "bg-error"
                  }`}
                ></div>
                <span>Deposits</span>
              </li>
              <li className="flex items-center">
                <div
                  className={`w-2 h-2 rounded-full mr-2 ${
                    member.preferences.notifications.withdrawals
                      ? "bg-success"
                      : "bg-error"
                  }`}
                ></div>
                <span>Withdrawals</span>
              </li>
              <li className="flex items-center">
                <div
                  className={`w-2 h-2 rounded-full mr-2 ${
                    member.preferences.notifications.newsAndEvents
                      ? "bg-success"
                      : "bg-error"
                  }`}
                ></div>
                <span>News & Events</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
