import React, { useState, useEffect } from "react";
import { Transaction } from "../../services/bankService";
import { useTransactions } from "../../hooks/useBank";
import { TransactionCard } from "./TransactionCard";

interface TransactionsManagerProps {
  initialTransactions?: Transaction[];
}

export const TransactionsManager: React.FC<TransactionsManagerProps> = ({
  initialTransactions,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");
  const [filteredTransactions, setFilteredTransactions] = useState<
    Transaction[]
  >([]);
  const [selectedTransaction, setSelectedTransaction] =
    useState<Transaction | null>(null);
  const [viewMode, setViewMode] = useState<"card" | "table">("card");

  // Use the hook to fetch transactions if not provided
  const { data: fetchedTransactions = [], isLoading } = useTransactions({});

  // Use either provided transactions or fetched ones
  const transactions = initialTransactions || fetchedTransactions;

  // Apply filters whenever dependencies change
  useEffect(() => {
    let filtered = [...transactions];

    // Filter by search term (user, description, ID)
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (t) =>
          t.id.toLowerCase().includes(term) ||
          t.sender?.username?.toLowerCase().includes(term) ||
          false ||
          t.sender?.displayName?.toLowerCase().includes(term) ||
          false ||
          t.recipient?.username?.toLowerCase().includes(term) ||
          false ||
          t.recipient?.displayName?.toLowerCase().includes(term) ||
          false ||
          t.description?.toLowerCase().includes(term) ||
          false,
      );
    }

    // Filter by transaction type
    if (selectedType) {
      filtered = filtered.filter((t) => t.type === selectedType);
    }

    // Filter by date range
    if (startDate) {
      const start = new Date(startDate);
      filtered = filtered.filter((t) => new Date(t.createdAt) >= start);
    }

    if (endDate) {
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999); // End of the day
      filtered = filtered.filter((t) => new Date(t.createdAt) <= end);
    }

    setFilteredTransactions(filtered);
  }, [transactions, searchTerm, selectedType, startDate, endDate]);

  const handleViewDetails = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
  };

  const handleCloseDetails = () => {
    setSelectedTransaction(null);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + " " + date.toLocaleTimeString();
  };

  const getStatusClass = (status: string) => {
    switch (status) {
      case "completed":
        return "text-success";
      case "pending":
        return "text-warning";
      case "failed":
      case "rejected":
        return "text-error";
      default:
        return "text-white";
    }
  };

  const getAmountClass = (type: string) => {
    switch (type) {
      case "deposit":
        return "text-success";
      case "withdrawal":
        return "text-error";
      default:
        return "text-white";
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "deposit":
        return "Deposit";
      case "withdrawal":
        return "Withdrawal";
      case "transfer":
        return "Transfer";
      case "donation":
        return "Donation";
      case "pay-code":
        return "Pay Code";
      default:
        return type.charAt(0).toUpperCase() + type.slice(1);
    }
  };

  return (
    <div className="w-full">
      {!selectedTransaction ? (
        <div>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
            <h3 className="text-xl font-semibold text-white mb-2 sm:mb-0">
              Transaction Search
            </h3>

            {/* View toggle buttons */}
            <div className="flex space-x-2">
              <button
                onClick={() => setViewMode("card")}
                className={`flex items-center px-3 py-1 rounded-md ${
                  viewMode === "card"
                    ? "bg-primary text-white"
                    : "bg-secondary text-gray-400 hover:bg-secondary-dark"
                }`}
                aria-label="Card view"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                  />
                </svg>
                <span className="ml-1 hidden sm:inline">Cards</span>
              </button>
              <button
                onClick={() => setViewMode("table")}
                className={`flex items-center px-3 py-1 rounded-md ${
                  viewMode === "table"
                    ? "bg-primary text-white"
                    : "bg-secondary text-gray-400 hover:bg-secondary-dark"
                }`}
                aria-label="Table view"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </svg>
                <span className="ml-1 hidden sm:inline">Table</span>
              </button>
            </div>
          </div>

          <div className="bg-secondary-dark p-4 rounded-lg border border-gray-600 mb-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-gray-400 mb-1">Search</label>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search by ID, user, or description"
                  className="w-full bg-secondary border border-gray-600 rounded-md p-2 text-white"
                />
              </div>
              <div>
                <label className="block text-gray-400 mb-1">Type</label>
                <select
                  value={selectedType || ""}
                  onChange={(e) => setSelectedType(e.target.value || null)}
                  className="w-full bg-secondary border border-gray-600 rounded-md p-2 text-white"
                >
                  <option value="">All Types</option>
                  <option value="deposit">Deposits</option>
                  <option value="withdrawal">Withdrawals</option>
                  <option value="transfer">Transfers</option>
                  <option value="donation">Donations</option>
                  <option value="pay-code">Pay Codes</option>
                </select>
              </div>
              <div>
                <label className="block text-gray-400 mb-1">Start Date</label>
                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-full bg-secondary border border-gray-600 rounded-md p-2 text-white"
                />
              </div>
              <div>
                <label className="block text-gray-400 mb-1">End Date</label>
                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="w-full bg-secondary border border-gray-600 rounded-md p-2 text-white"
                />
              </div>
            </div>
          </div>

          {isLoading ? (
            <div className="bg-secondary-dark p-4 rounded-lg border border-gray-600 text-center">
              <p className="text-gray-400">Loading transactions...</p>
            </div>
          ) : filteredTransactions.length === 0 ? (
            <div className="bg-secondary-dark p-4 rounded-lg border border-gray-600 text-center">
              <p className="text-gray-400">No transactions found.</p>
            </div>
          ) : viewMode === "card" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredTransactions.map((transaction) => (
                <TransactionCard
                  key={transaction.id}
                  transaction={transaction}
                  onSelect={handleViewDetails}
                />
              ))}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-secondary-dark">
                    <th className="p-3 text-left text-white">ID</th>
                    <th className="p-3 text-left text-white">Date</th>
                    <th className="p-3 text-left text-white">Type</th>
                    <th className="p-3 text-left text-white">User</th>
                    <th className="p-3 text-left text-white">Amount</th>
                    <th className="p-3 text-left text-white">Status</th>
                    <th className="p-3 text-left text-white">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredTransactions.map((transaction) => (
                    <tr
                      key={transaction.id}
                      className="border-t border-gray-600 hover:bg-secondary-dark"
                    >
                      <td className="p-3 text-white">{transaction.id}</td>
                      <td className="p-3 text-white">
                        {formatDate(transaction.createdAt)}
                      </td>
                      <td className="p-3 text-white">
                        {getTypeLabel(transaction.type)}
                      </td>
                      <td className="p-3 text-white">
                        {transaction.sender?.displayName ||
                          transaction.recipient?.displayName ||
                          "Unknown"}
                      </td>
                      <td className={`p-3 ${getAmountClass(transaction.type)}`}>
                        NS {transaction.amount.toFixed(0)}
                      </td>
                      <td
                        className={`p-3 ${getStatusClass(transaction.status)}`}
                      >
                        {transaction.status.charAt(0).toUpperCase() +
                          transaction.status.slice(1)}
                      </td>
                      <td className="p-3">
                        <button
                          onClick={() => handleViewDetails(transaction)}
                          className="px-3 py-1 bg-primary text-white rounded-md hover:bg-primary-light"
                        >
                          Details
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      ) : (
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold text-white">
              Transaction Details: {selectedTransaction.id}
            </h3>
            <button
              onClick={handleCloseDetails}
              className="px-3 py-1 bg-secondary text-white rounded-md hover:bg-secondary-light"
            >
              Back to List
            </button>
          </div>

          <div className="bg-secondary-dark p-4 rounded-lg border border-gray-600">
            <h4 className="font-semibold text-white mb-3">
              Transaction Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-400">ID:</span>
                  <span className="text-white">{selectedTransaction.id}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Type:</span>
                  <span className="text-white">
                    {getTypeLabel(selectedTransaction.type)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Amount:</span>
                  <span className={getAmountClass(selectedTransaction.type)}>
                    NS {selectedTransaction.amount.toFixed(0)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Status:</span>
                  <span className={getStatusClass(selectedTransaction.status)}>
                    {selectedTransaction.status.charAt(0).toUpperCase() +
                      selectedTransaction.status.slice(1)}
                  </span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-400">Created:</span>
                  <span className="text-white">
                    {formatDate(selectedTransaction.createdAt)}
                  </span>
                </div>
                {selectedTransaction.processedAt && (
                  <div className="flex justify-between">
                    <span className="text-gray-400">Processed:</span>
                    <span className="text-white">
                      {formatDate(selectedTransaction.processedAt)}
                    </span>
                  </div>
                )}
                {selectedTransaction.processedBy && (
                  <div className="flex justify-between">
                    <span className="text-gray-400">Processed By:</span>
                    <span className="text-white">
                      {selectedTransaction.processedBy.displayName}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div className="bg-secondary-dark p-4 rounded-lg border border-gray-600">
              <h4 className="font-semibold text-white mb-3">
                {selectedTransaction.type === "transfer" ? "Sender" : "User"}
              </h4>
              {selectedTransaction.sender ? (
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Username:</span>
                    <span className="text-white">
                      {selectedTransaction.sender.username}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Display Name:</span>
                    <span className="text-white">
                      {selectedTransaction.sender.displayName}
                    </span>
                  </div>
                </div>
              ) : (
                <p className="text-gray-400">No sender information available</p>
              )}
            </div>

            {selectedTransaction.type === "transfer" && (
              <div className="bg-secondary-dark p-4 rounded-lg border border-gray-600">
                <h4 className="font-semibold text-white mb-3">Recipient</h4>
                {selectedTransaction.recipient ? (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Username:</span>
                      <span className="text-white">
                        {selectedTransaction.recipient.username}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Display Name:</span>
                      <span className="text-white">
                        {selectedTransaction.recipient.displayName}
                      </span>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-400">
                    No recipient information available
                  </p>
                )}
              </div>
            )}
          </div>

          <div className="mt-6 bg-secondary-dark p-4 rounded-lg border border-gray-600">
            <h4 className="font-semibold text-white mb-3">
              Description & Notes
            </h4>
            <div className="space-y-4">
              <div>
                <span className="text-gray-400 block mb-1">Description:</span>
                <span className="text-white block">
                  {selectedTransaction.description || "No description provided"}
                </span>
              </div>
              {selectedTransaction.note && (
                <div>
                  <span className="text-gray-400 block mb-1">Note:</span>
                  <span className="text-white block">
                    {selectedTransaction.note}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TransactionsManager;
