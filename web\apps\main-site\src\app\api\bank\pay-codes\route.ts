import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../lib/prisma";
import { verifyToken } from "../../../../lib/auth";
import crypto from "crypto";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// Helper function to generate a unique pay code
function generatePayCode() {
  // Generate a random string and format it as BOS-XXXX-XXXX
  const randomBytes = crypto.randomBytes(4);
  const firstPart = randomBytes.toString("hex").substring(0, 4).toUpperCase();
  const secondPart = crypto
    .randomBytes(4)
    .toString("hex")
    .substring(0, 4)
    .toUpperCase();
  return `BOS-${firstPart}-${secondPart}`;
}

export const GET = async (req: NextRequest) => {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Parse query parameters for optional sorting/pagination
    const { searchParams } = new URL(req.url);
    const limit = searchParams.get("limit")
      ? parseInt(searchParams.get("limit") as string)
      : 50;
    const page = searchParams.get("page")
      ? parseInt(searchParams.get("page") as string)
      : 1;
    const status = searchParams.get("status");
    const sortBy = searchParams.get("sortBy") || "createdAt";
    const sortOrder = searchParams.get("sortOrder") || "desc";

    // Build the query
    const skip = (page - 1) * limit;
    const where: any = {
      OR: [{ createdById: userId }, { redeemedById: userId }],
    };

    // Add status filter if provided
    if (status) {
      where.status = status;
    }

    // Query the database for pay codes
    const payCodes = await prisma.payCode.findMany({
      where,
      include: {
        createdBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        redeemedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
      },
      orderBy: {
        [sortBy]: sortOrder,
      },
      skip,
      take: limit,
    });

    // Get total count for pagination
    const totalCount = await prisma.payCode.count({ where });

    // Format dates as ISO strings for JSON serialization
    const formattedCodes = payCodes.map((code) => ({
      ...code,
      createdAt: code.createdAt.toISOString(),
      expiresAt: code.expiresAt.toISOString(),
      redeemedAt: code.redeemedAt ? code.redeemedAt.toISOString() : null,
    }));

    return NextResponse.json({
      data: formattedCodes,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching pay codes:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};

export const POST = async (req: NextRequest) => {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Get request body
    const { amount, expiresAt, maxUses } = await req.json();

    // Validate input
    if (!amount || !expiresAt) {
      return NextResponse.json(
        { error: "Amount and expiresAt are required" },
        { status: 400 },
      );
    }

    if (amount <= 0) {
      return NextResponse.json(
        { error: "Amount must be greater than 0" },
        { status: 400 },
      );
    }

    // Generate a unique pay code
    let code = generatePayCode();
    let codeExists = true;

    // Ensure the code is unique
    while (codeExists) {
      const existingCode = await prisma.payCode.findUnique({
        where: { code },
      });

      if (!existingCode) {
        codeExists = false;
      } else {
        code = generatePayCode();
      }
    }

    // Create the pay code and transaction record in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the pay code
      const newPayCode = await tx.payCode.create({
        data: {
          code,
          amount,
          expiresAt: new Date(expiresAt),
          status: "active",
          createdById: userId,
          maxUses: maxUses || null,
        },
        include: {
          createdBy: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatar: true,
            },
          },
        },
      });

      // Create a transaction record for tracking purposes
      // Note: This is just a record, no balance changes needed for the creator
      const transaction = await tx.transaction.create({
        data: {
          amount: amount,
          type: "paycode_create",
          status: "completed",
          description: `Pay code created: ${code}`,
          senderId: userId,
          payCodeId: newPayCode.id,
        },
        include: {
          sender: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatar: true,
            },
          },
        },
      });

      return { payCode: newPayCode, transaction };
    });

    // Format dates for response
    const formattedPayCode = {
      ...result.payCode,
      createdAt: result.payCode.createdAt.toISOString(),
      expiresAt: result.payCode.expiresAt.toISOString(),
      redeemedAt: result.payCode.redeemedAt
        ? result.payCode.redeemedAt.toISOString()
        : null,
      transaction: {
        ...result.transaction,
        createdAt: result.transaction.createdAt.toISOString(),
        updatedAt: result.transaction.updatedAt.toISOString(),
      },
    };

    return NextResponse.json(formattedPayCode);
  } catch (error) {
    console.error("Error creating pay code:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
