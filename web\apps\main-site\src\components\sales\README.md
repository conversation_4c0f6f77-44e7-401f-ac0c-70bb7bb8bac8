# Sales Management Components

Staff-facing components for managing products, categories, and sales operations.

## Layout & Dashboard

- **SalesDashboardLayout.tsx** - Main layout component for sales management dashboard
- **SalesDashboardMain.tsx** - Main dashboard view with sales overview and metrics

## Product Management

- **ProductForm.tsx** - Form component for creating and editing products
- **ProductList.tsx** - List view of products with management actions

## Category Management

- **CategoryForm.tsx** - Form component for creating and editing product categories
- **CategoryList.tsx** - List view of product categories with management options

## Order Management

- **OrderList.tsx** - List view of customer orders with status and management controls

## Exports

- **index.ts** - Component exports for sales module

These components provide staff with comprehensive tools for managing the e-commerce aspects of the platform including inventory, categories, and order fulfillment.
