import React from "react";

interface CreatePayCodeConfirmationModalProps {
  amount: string;
  maxUses: string;
  expiryDays: string;
  onConfirm: () => void;
  onCancel: () => void;
}

export const CreatePayCodeConfirmationModal: React.FC<
  CreatePayCodeConfirmationModalProps
> = ({ amount, maxUses, expiryDays, onConfirm, onCancel }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-secondary-light rounded-lg p-6 max-w-md w-full">
        <h3 className="text-xl font-bold text-white mb-4">
          Confirm Pay-Code Creation
        </h3>
        <p className="mb-4 text-white">
          You are about to create a Pay-Code that will allow others to pay you{" "}
          <span className="font-bold">NS {amount}</span>.
        </p>
        <p className="mb-4 text-white">
          This code can be used <span className="font-bold">{maxUses}</span>{" "}
          time{parseInt(maxUses) !== 1 ? "s" : ""}.
        </p>
        <p className="mb-4 text-white">
          This code will expire in {expiryDays} day
          {parseInt(expiryDays) !== 1 ? "s" : ""}.
        </p>
        <p className="mb-4 text-white">
          <strong>Note:</strong> Creating this code will not deduct any amount
          from your balance. You will receive payment when others redeem this
          code.
        </p>
        <div className="flex justify-end space-x-4">
          <button
            onClick={onCancel}
            className="px-4 py-2 border border-gray-600 rounded-md text-white hover:bg-secondary"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
          >
            Confirm
          </button>
        </div>
      </div>
    </div>
  );
};
