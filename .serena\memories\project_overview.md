# Bank of Styx Event Website - Project Overview

## Project Purpose
The Bank of Styx website is a comprehensive event-based banking and management platform built for serving approximately 1,000 users with 30-40 concurrent users (peaks up to 100). It combines banking functionality with event management, volunteer systems, news management, and e-commerce capabilities.

## Core Systems
1. **Banking System**: Account management, transactions, pay codes, real-time balance updates
2. **Authentication**: Multi-method auth (email/password, Discord OAuth) with JWT sessions
3. **Real-time Updates**: SSE-based notifications and live data updates
4. **News System**: Article management with categories and rich text editing
5. **Event Management**: Event creation, categories, and calendar integration
6. **Volunteer System**: Shift management, categories, hour tracking, payments
7. **Shopping System**: Product management, cart with hold system, Stripe checkout
8. **Support Tickets**: Priority-based ticket system with assignment and notifications
9. **Ticket Hold System**: 15-minute holds with auto-expiration to prevent overselling

## Architecture Type
- **Monorepo Structure**: Uses PNPM workspaces
- **Next.js Application**: App Router architecture (v13.4.2)
- **Database**: MySQL 8.0 with Prisma ORM
- **Package Management**: PNPM 8.6.0

## Target Environment
- Serves ~1000 users
- 30-40 concurrent users (peaks of 100)
- Windows development environment
- Real-time features via Server-Sent Events