# Bank of Styx Web Monorepo

This is the main web application monorepo for the Bank of Styx banking platform, built with Next.js, React, and TypeScript.

## Structure

- **apps/** - Main applications including the Next.js site and API modules
- **packages/** - Shared packages and libraries used across applications
- **node_modules/** - Dependencies managed by PNPM
- **package.json** - Root package configuration and workspace scripts
- **pnpm-workspace.yaml** - PNPM workspace configuration

## Getting Started

```bash
pnpm install
pnpm dev
```

This monorepo uses PNPM workspaces to manage dependencies and build processes across multiple packages.
