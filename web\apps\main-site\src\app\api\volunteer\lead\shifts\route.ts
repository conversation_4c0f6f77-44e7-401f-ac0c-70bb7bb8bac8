import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/volunteer/lead/shifts - Get shifts for the lead manager's category
export async function GET(req: NextRequest) {
  try {
    // Check if user is authenticated and has lead manager role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasLeadRole = await userHasRole(req, "leadManager");
    if (!hasLeadRole) {
      return NextResponse.json(
        { error: "Unauthorized - Lead Manager role required" },
        { status: 403 },
      );
    }

    // Get the lead manager's category ID
    const leadManagerCategoryId = user.leadManagerCategoryId;
    if (!leadManagerCategoryId) {
      return NextResponse.json(
        { error: "No category assigned to this lead manager" },
        { status: 404 },
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const dateFilter = url.searchParams.get("date");

    // Build the where clause for filtering
    const whereClause: any = {
      categoryId: leadManagerCategoryId,
    };

    // Add date filter if provided
    if (dateFilter) {
      const filterDate = new Date(dateFilter);
      const nextDay = new Date(filterDate);
      nextDay.setDate(nextDay.getDate() + 1);

      whereClause.startTime = {
        gte: filterDate,
        lt: nextDay,
      };
    }

    // Get shifts for the category with assignments
    const shifts = await prisma.volunteerShift.findMany({
      where: whereClause,
      include: {
        assignments: {
          include: {
            user: {
              select: {
                id: true,
                displayName: true,
                avatar: true,
                email: true,
              },
            },
            hours: true,
          },
        },
      },
      orderBy: {
        startTime: "asc",
      },
    });

    // Calculate vacancies and format the response
    const formattedShifts = shifts.map((shift) => {
      const vacancies = shift.maxVolunteers - shift.assignments.length;

      return {
        ...shift,
        vacancies,
      };
    });

    return NextResponse.json({ shifts: formattedShifts });
  } catch (error) {
    console.error("Error fetching lead shifts:", error);
    return NextResponse.json(
      { error: "Failed to fetch shifts data" },
      { status: 500 },
    );
  }
}
