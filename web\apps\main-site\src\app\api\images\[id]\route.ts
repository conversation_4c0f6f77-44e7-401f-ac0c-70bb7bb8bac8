import { NextResponse } from "next/server";
import fs from "fs";
import path from "path";
import { prisma } from "@/lib/prisma";

export async function GET(
  request: Request,
  { params }: { params: { id: string } },
) {
  try {
    // Find image by ID
    const image = await prisma.uploadedImage.findUnique({
      where: { id: params.id },
    });

    if (!image) {
      return new NextResponse("Image not found", { status: 404 });
    }

    // Get file path
    const fullPath = path.join(process.cwd(), image.path);

    // Check if file exists
    if (!fs.existsSync(fullPath)) {
      return new NextResponse("Image file missing", { status: 404 });
    }

    // Read and serve file
    const fileBuffer = fs.readFileSync(fullPath);

    return new NextResponse(fileBuffer, {
      headers: {
        "Content-Type": image.mimeType,
        "Cache-Control": "public, max-age=86400",
      },
    });
  } catch (error) {
    console.error("Image serving error:", error);

    // Provide more detailed error message in development
    const isDev = process.env.NODE_ENV === "development";
    const errorMessage =
      isDev && error instanceof Error
        ? `Server error: ${error.message}`
        : "Server error";

    return new NextResponse(errorMessage, {
      status: 500,
      headers: {
        "Content-Type": "text/plain",
      },
    });
  }
}
