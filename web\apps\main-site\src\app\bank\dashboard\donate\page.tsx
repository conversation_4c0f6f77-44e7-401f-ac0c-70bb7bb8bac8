"use client";
import Link from "next/link";

import React, { useState } from "react";
import { DashboardLayout } from "../../../../components/bank";
import {
  useBankUser,
  useRecentDonations,
  useCreateTransaction,
} from "../../../../hooks/useBank";
import { toast } from "react-hot-toast";

export default function DonatePage() {
  const [amount, setAmount] = useState("");
  const [note, setNote] = useState("");
  const [anonymous, setAnonymous] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  // Helper function to add to the current amount
  const addToAmount = (valueToAdd: number) => {
    const currentAmount = amount === "" ? 0 : parseFloat(amount);
    const newAmount = currentAmount + valueToAdd;
    setAmount(newAmount.toString());
  };

  // Fetch user data and recent donations
  const { data: bankUser, isLoading: isLoadingUser } = useBankUser();
  const { data: recentDonations, isLoading: isLoadingDonations } =
    useRecentDonations(5);
  const createTransaction = useCreateTransaction();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setShowConfirmation(true);
  };

  const handleConfirm = async () => {
    try {
      await createTransaction.mutateAsync({
        type: "donation",
        amount: parseFloat(amount),
        recipient: "Community Fund",
        note: anonymous ? "Anonymous donation" : note || undefined,
      });

      // Show success message
      toast.success(`Successfully donated NS ${amount} to the Community Fund`);

      // Reset form
      setAmount("");
      setNote("");
      setAnonymous(false);
      setShowConfirmation(false);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "An error occurred";
      toast.error(errorMessage);
      setShowConfirmation(false);
    }
  };

  const handleCancel = () => {
    setShowConfirmation(false);
  };

  return (
    <DashboardLayout>
      <div className="mb-3">
        <Link
          href="/bank/dashboard"
          className="text-primary hover:text-primary-light"
        >
          &larr; Back to Dashboard
        </Link>
      </div>
      <div className="bg-secondary-light rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-2xl font-bold text-white mb-6">
          Donate to Community Fund
        </h2>

        <div className="mb-6">
          <p className="text-gray-400">
            Current Balance:{" "}
            <span className="font-bold text-success">
              {isLoadingUser
                ? "Loading..."
                : `NS ${bankUser?.balance.toFixed(0)}`}
            </span>
          </p>
        </div>

        <div className="bg-secondary border border-gray-600 rounded-lg p-4 mb-6">
          <h3 className="font-bold text-white mb-2">
            About the Community Fund
          </h3>
          <p className="text-gray-400 mb-3">
            The Community Fund supports various initiatives within the Pirate
            Rinfair community, including:
          </p>
          <ul className="text-gray-400 space-y-1 mb-3">
            <li>• Community events and celebrations</li>
            <li>• Infrastructure improvements</li>
            <li>• Support for new members</li>
            <li>• Emergency assistance for community members in need</li>
          </ul>
          <p className="text-gray-400">
            100% of donations go directly to community projects. Thank you for
            your support!
          </p>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label
                htmlFor="amount"
                className="block text-white font-medium mb-2"
              >
                Donation Amount
              </label>
              <div className="relative flex items-center">
                <span className="absolute left-3 text-gray-400 select-none pointer-events-none">
                  NS
                </span>
                <input
                  type="number"
                  id="amount"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="0"
                  min="1"
                  step="1"
                  max={bankUser?.balance || 0}
                  required
                />
              </div>
              <div className="flex justify-end mt-1">
                <div className="flex space-x-2">
                  {[5, 10, 25, 50, 100].map((value) => (
                    <button
                      key={value}
                      type="button"
                      onClick={() => addToAmount(value)}
                      className="text-xs bg-secondary hover:bg-hover text-white px-2 py-1 rounded"
                    >
                      NS {value}
                    </button>
                  ))}
                  <button
                    type="button"
                    onClick={() => addToAmount(1000)}
                    className="text-xs bg-primary hover:bg-primary-dark text-white px-2 py-1 rounded"
                  >
                    1 Todd
                  </button>
                </div>
              </div>
            </div>

            <div>
              <label
                htmlFor="note"
                className="block text-white font-medium mb-2"
              >
                Note <span className="text-gray-400 text-sm">(Optional)</span>
              </label>
              <input
                type="text"
                id="note"
                value={note}
                onChange={(e) => setNote(e.target.value)}
                className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Add a message with your donation"
              />
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="anonymous"
                checked={anonymous}
                onChange={(e) => setAnonymous(e.target.checked)}
                className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
              />
              <label htmlFor="anonymous" className="ml-2 block text-white">
                Make this donation anonymous
              </label>
            </div>

            <div className="flex justify-end space-x-4 pt-4">
              <button
                type="button"
                onClick={() => {
                  setAmount("");
                  setNote("");
                  setAnonymous(false);
                }}
                className="px-4 py-2 border border-gray-600 rounded-md text-white hover:bg-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
              >
                Donate
              </button>
            </div>
          </div>
        </form>
      </div>

      <div className="bg-secondary-light rounded-lg shadow-md p-6">
        <h2 className="text-lg font-semibold text-white mb-4">
          Your Recent Donations
        </h2>

        {isLoadingDonations ? (
          <div className="space-y-4">
            <div className="animate-pulse h-16 bg-gray-600 rounded w-full"></div>
            <div className="animate-pulse h-16 bg-gray-600 rounded w-full"></div>
            <div className="animate-pulse h-16 bg-gray-600 rounded w-full"></div>
          </div>
        ) : recentDonations && recentDonations.length > 0 ? (
          <div className="divide-y divide-gray-600">
            {recentDonations.map((donation) => (
              <div key={donation.id} className="py-4">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="text-white font-medium">
                      {donation.description}
                    </div>
                    <div className="text-sm text-gray-400 mt-1">
                      {new Date(donation.createdAt).toLocaleDateString()}
                    </div>
                    {donation.note && (
                      <div className="text-sm text-gray-400 mt-1 italic">
                        &quot;{donation.note}&quot;
                      </div>
                    )}
                  </div>
                  <div className="font-bold text-accent">
                    NS {Math.abs(donation.amount).toFixed(0)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-6 text-gray-400">
            No recent donations.
          </div>
        )}
      </div>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-secondary-light rounded-lg p-6 max-w-md w-full">
            <h3 className="text-xl font-bold text-white mb-4">
              Confirm Donation
            </h3>
            <p className="mb-4 text-white">
              You are about to donate{" "}
              <span className="font-bold">NS {amount}</span> to the Community
              Fund.
            </p>
            {note && <p className="mb-4 text-white">Note: {note}</p>}
            {anonymous && (
              <p className="mb-4 text-gray-400 italic">
                This donation will be anonymous.
              </p>
            )}
            <div className="flex justify-end space-x-4">
              <button
                onClick={handleCancel}
                className="px-4 py-2 border border-gray-600 rounded-md text-white hover:bg-secondary"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirm}
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
}
