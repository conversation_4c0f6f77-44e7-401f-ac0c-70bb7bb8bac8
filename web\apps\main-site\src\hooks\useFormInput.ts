import { useState, useCallback, ChangeEvent } from 'react';

interface UseFormInputReturn {
  value: string;
  onChange: (e: ChangeEvent<HTMLInputElement>) => void;
  error?: string;
  clearError: () => void;
  setValue: (value: string) => void;
}

export function useFormInput(
  initialValue: string = '',
  validator?: (value: string) => string | undefined
): UseFormInputReturn {
  const [value, setValue] = useState(initialValue);
  const [error, setError] = useState<string | undefined>();

  const onChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.type === 'checkbox' ? e.target.checked.toString() : e.target.value;
    setValue(newValue);

    // Clear error when user starts typing
    if (error) setError(undefined);

    // Run validation if provided
    if (validator) {
      const validationError = validator(newValue);
      setError(validationError);
    }
  }, [error, validator]);

  const clearError = useCallback(() => setError(undefined), []);

  return { value, onChange, error, clearError, setValue };
}