"use client";

import React, { useState, useEffect } from "react";

interface ScrollToTopProps {
  /**
   * Bottom padding in pixels
   */
  bottomPadding?: number;
  /**
   * Right padding in pixels
   */
  rightPadding?: number;
  /**
   * Scroll threshold in pixels - when to show the button
   */
  scrollThreshold?: number;
}

export const ScrollToTop: React.FC<ScrollToTopProps> = ({
  bottomPadding = 20,
  rightPadding = 20,
  scrollThreshold = 300,
}) => {
  const [isVisible, setIsVisible] = useState(false);

  // Check scroll position and update visibility
  useEffect(() => {
    const toggleVisibility = () => {
      if (window.scrollY > scrollThreshold) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    // Initial check
    toggleVisibility();

    // Add scroll event listener
    window.addEventListener("scroll", toggleVisibility);

    // Clean up
    return () => window.removeEventListener("scroll", toggleVisibility);
  }, [scrollThreshold]);

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <>
      {isVisible && (
        <button
          onClick={scrollToTop}
          className="fixed z-50 bg-primary hover:bg-primary-dark text-white rounded-full p-3 shadow-lg transition-all duration-300 transform hover:scale-110"
          style={{ bottom: `${bottomPadding}px`, right: `${rightPadding}px` }}
          aria-label="Scroll to top"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 10l7-7m0 0l7 7m-7-7v18"
            />
          </svg>
        </button>
      )}
    </>
  );
};
