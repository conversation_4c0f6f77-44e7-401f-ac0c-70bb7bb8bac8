import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/volunteer/lead/dashboard - Get dashboard data for the lead manager
export async function GET(req: NextRequest) {
  try {
    // Check if user is authenticated and has lead manager role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasLeadRole = await userHasRole(req, "leadManager");
    if (!hasLeadRole) {
      return NextResponse.json(
        { error: "Unauthorized - Lead Manager role required" },
        { status: 403 },
      );
    }

    // Get the lead manager's category ID
    const leadManagerCategoryId = user.leadManagerCategoryId;

    // If no category ID is assigned, try to find a category where this user is assigned as lead manager
    let categoryId = leadManagerCategoryId;

    if (!categoryId) {
      console.log(
        `User ${user.id} has lead manager role but no leadManagerCategoryId. Attempting to find assigned category.`,
      );

      // Try to find a category where this user is assigned as lead manager
      const assignedCategory = await prisma.volunteerCategory.findFirst({
        where: {
          leadManagerId: user.id,
        },
      });

      if (assignedCategory) {
        console.log(
          `Found category ${assignedCategory.id} assigned to user ${user.id}`,
        );
        categoryId = assignedCategory.id;

        // Update the user record to set the correct leadManagerCategoryId
        await prisma.user.update({
          where: { id: user.id },
          data: {
            leadManagerCategoryId: assignedCategory.id,
          },
        });
        console.log(
          `Updated user ${user.id} with leadManagerCategoryId ${assignedCategory.id}`,
        );
      } else {
        console.log(
          `No category found where user ${user.id} is assigned as lead manager`,
        );
        return NextResponse.json(
          { error: "No category assigned to this lead manager" },
          { status: 404 },
        );
      }
    }

    // Get current date for upcoming shifts calculation
    const now = new Date();

    // Get the lead manager's category with event details
    const category = await prisma.volunteerCategory.findUnique({
      where: { id: categoryId },
      include: {
        event: {
          select: {
            id: true,
            name: true,
            startDate: true,
            endDate: true,
            status: true,
          },
        },
      },
    });

    if (!category) {
      console.log(`Category ${categoryId} not found for user ${user.id}`);
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 },
      );
    }

    // Get shifts for the category
    const shifts = await prisma.volunteerShift.count({
      where: {
        categoryId: categoryId,
      },
    });

    // Get upcoming shifts count
    const upcomingShifts = await prisma.volunteerShift.count({
      where: {
        categoryId: categoryId,
        startTime: {
          gte: now,
        },
      },
    });

    // Get total volunteers assigned to this category's shifts
    // Use findMany with distinct and then count the results to avoid type issues
    const volunteerAssignments = await prisma.volunteerAssignment.findMany({
      where: {
        shift: {
          categoryId: categoryId,
        },
      },
      distinct: ["userId"],
      select: {
        userId: true,
      },
    });
    const volunteers = volunteerAssignments.length;

    // Get completed shifts count
    const completedShifts = await prisma.volunteerAssignment.count({
      where: {
        shift: {
          categoryId: categoryId,
        },
        status: "completed",
      },
    });

    // Get pending payments count
    const pendingPayments = await prisma.volunteerHours.count({
      where: {
        assignment: {
          shift: {
            categoryId: categoryId,
          },
        },
        paymentStatus: "pending",
      },
    });

    return NextResponse.json({
      category,
      stats: {
        totalShifts: shifts,
        upcomingShifts,
        totalVolunteers: volunteers,
        completedShifts,
        pendingPayments,
      },
    });
  } catch (error) {
    console.error("Error fetching lead dashboard data:", error);

    // Provide more detailed error information for debugging
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorDetails =
      error instanceof Error && (error as any).details
        ? (error as any).details
        : {};

    return NextResponse.json(
      {
        error: "Failed to fetch dashboard data",
        message: errorMessage,
        details: errorDetails,
      },
      { status: 500 },
    );
  }
}
