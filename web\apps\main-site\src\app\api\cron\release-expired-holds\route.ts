import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { TicketStatus } from "@prisma/client";
import { releaseExpiredEventCapacityHolds } from "@/lib/event-capacity-system";

// POST /api/cron/release-expired-holds - Background job to release expired holds
export async function POST(req: NextRequest) {
  try {
    // Verify secret key for security
    const authHeader = req.headers.get("authorization");
    if (authHeader !== `Bearer ${process.env.CRON_SECRET_KEY}`) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const now = new Date();

    // Release expired event capacity holds first
    const eventCapacityResult = await releaseExpiredEventCapacityHolds();

    return await prisma.$transaction(async (tx) => {
      // Find expired ticket holds
      const expiredTicketHolds = await tx.ticketHold.findMany({
        where: {
          expiresAt: { lt: now },
        },
      });

      // Find expired volunteer slot holds
      const expiredVolunteerHolds = await tx.volunteerSlotHold.findMany({
        where: {
          expiresAt: { lt: now },
        },
      });

      let ticketsReleased = 0;
      let slotsReleased = 0;

      // Release tickets from expired holds
      if (expiredTicketHolds.length > 0) {
        const result = await tx.ticket.updateMany({
          where: {
            holdId: { in: expiredTicketHolds.map((h) => h.id) },
            status: TicketStatus.HELD,
          },
          data: {
            status: TicketStatus.AVAILABLE,
            holdId: null,
          },
        });
        ticketsReleased = result.count;
      }

      // Release volunteer slots from expired holds
      if (expiredVolunteerHolds.length > 0) {
        const result = await tx.volunteerSlot.updateMany({
          where: {
            holdId: { in: expiredVolunteerHolds.map((h) => h.id) },
            status: TicketStatus.HELD,
          },
          data: {
            status: TicketStatus.AVAILABLE,
            holdId: null,
          },
        });
        slotsReleased = result.count;
      }

      return NextResponse.json({
        eventCapacityHoldsProcessed: eventCapacityResult.holdsReleased,
        eventCapacityReleased: eventCapacityResult.capacityReleased,
        ticketHoldsProcessed: expiredTicketHolds.length,
        volunteerHoldsProcessed: expiredVolunteerHolds.length,
        ticketsReleased,
        slotsReleased,
        timestamp: now,
      });
    });
  } catch (error) {
    console.error("Error releasing expired holds:", error);
    return NextResponse.json(
      { error: "Failed to release expired holds" },
      { status: 500 },
    );
  }
}
