import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";
import { startOfDay, endOfDay } from "date-fns";

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// GET /api/volunteer/management/shifts - Get shifts for a specific category and date (coordinator view)
export async function GET(req: NextRequest) {
  try {
    // Check if user is authenticated and has coordinator role
    const user = await getCurrentUser(req);
    
    // Temporarily disable authentication for testing
    // if (!user) {
    //   return NextResponse.json(
    //     { error: "Unauthorized - Authentication required" },
    //     { status: 401 },
    //   );
    // }

    // const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");
    // if (!hasCoordinatorRole) {
    //   return NextResponse.json(
    //     { error: "Unauthorized - Volunteer Coordinator role required" },
    //     { status: 403 },
    //   );
    // }

    // Get parameters from query
    const { searchParams } = new URL(req.url);
    const categoryId = searchParams.get("categoryId");
    const dateParam = searchParams.get("date");

    if (!categoryId) {
      return NextResponse.json(
        { error: "Category ID is required" },
        { status: 400 },
      );
    }

    if (!dateParam) {
      return NextResponse.json(
        { error: "Date parameter is required" },
        { status: 400 },
      );
    }

    // Parse and validate date
    const targetDate = new Date(dateParam);
    if (isNaN(targetDate.getTime())) {
      return NextResponse.json(
        { error: "Invalid date format" },
        { status: 400 },
      );
    }

    const startDate = startOfDay(targetDate);
    const endDate = endOfDay(targetDate);

    // Verify category exists
    const category = await prisma.volunteerCategory.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 },
      );
    }

    // Get shifts for the specified category and date
    const shifts = await prisma.volunteerShift.findMany({
      where: {
        categoryId: categoryId,
        startTime: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        assignments: {
          include: {
            user: {
              select: {
                id: true,
                displayName: true,
                email: true,
                avatar: true,
              },
            },
            hours: true,
          },
        },
      },
      orderBy: {
        startTime: "asc",
      },
    });

    // Calculate vacancies for each shift
    const shiftsWithVacancies = shifts.map((shift) => ({
      ...shift,
      vacancies: shift.maxVolunteers - shift.assignments.length,
    }));

    return NextResponse.json({
      shifts: shiftsWithVacancies,
    });
  } catch (error) {
    console.error("Error fetching management shifts:", error);

    const errorMessage = error instanceof Error ? error.message : String(error);
    return NextResponse.json(
      {
        error: "Failed to fetch shifts",
        message: errorMessage,
      },
      { status: 500 },
    );
  }
}