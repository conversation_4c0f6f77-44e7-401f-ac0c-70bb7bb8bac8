/**
 * API service for image uploads
 */
import fetchClient from "@/lib/fetchClient";

/**
 * Upload a file to the server with database tracking
 * @param file The file to upload
 * @param entityType Optional entity type (e.g., 'news', 'avatar')
 * @param entityId Optional entity ID (e.g., article ID, user ID)
 * @returns Response with the uploaded file details
 */
export async function uploadImage(
  file: File,
  entityType?: string,
  entityId?: string,
): Promise<{
  id: string;
  url: string;
  filename: string;
  originalUrl: string;
}> {
  try {
    const formData = new FormData();
    formData.append("file", file);

    if (entityType) {
      formData.append("entityType", entityType);
    }

    if (entityId) {
      formData.append("entityId", entityId);
    }

    // For FormData, we need to use the fetch API directly but still leverage our auth token
    // which will be automatically added by the fetchClient
    return fetchClient.request<{
      id: string;
      url: string;
      filename: string;
      originalUrl: string;
    }>("/api/uploads", {
      method: "POST",
      body: formData,
      // Don't set Content-Type header for FormData as the browser will set it with the boundary
    });
  } catch (error) {
    console.error("Error uploading image:", error);
    throw error;
  }
}

/**
 * Upload multiple images at once with database tracking
 * @param files Array of files to upload
 * @param entityType Optional entity type (e.g., 'news', 'avatar')
 * @param entityId Optional entity ID (e.g., article ID, user ID)
 * @returns Array of uploaded file details
 */
export async function uploadMultipleImages(
  files: File[],
  entityType?: string,
  entityId?: string,
): Promise<
  {
    id: string;
    url: string;
    filename: string;
    originalUrl: string;
  }[]
> {
  try {
    // Upload all images in parallel
    const uploadPromises = files.map((file) =>
      uploadImage(file, entityType, entityId),
    );
    return await Promise.all(uploadPromises);
  } catch (error) {
    console.error("Error uploading multiple images:", error);
    throw error;
  }
}
