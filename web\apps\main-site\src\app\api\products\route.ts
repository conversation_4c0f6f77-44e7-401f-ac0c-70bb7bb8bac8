import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/products - List all products (public)
export async function GET(req: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(req.url);
    const categoryId = searchParams.get("categoryId");
    const eventId = searchParams.get("eventId");
    const isActive = searchParams.get("isActive") === "true";

    // Build filter
    const filter: any = {};
    if (categoryId) filter.categoryId = categoryId;
    if (eventId) filter.eventId = eventId;
    if (searchParams.has("isActive")) filter.isActive = isActive;
    
    // Hide free products from public listings (they should only be accessible via redemption codes)
    filter.isFree = false;

    // Get products with filtering
    const products = await prisma.product.findMany({
      where: filter,
      include: {
        category: true,
        event: {
          select: {
            id: true,
            name: true,
            startDate: true,
            endDate: true,
            capacity: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({ products });
  } catch (error) {
    console.error("Error fetching products:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
