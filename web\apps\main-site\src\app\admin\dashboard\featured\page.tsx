"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../../../../contexts/AuthContext";
import { AdminDashboardLayout } from "../../../../components/admin";
import {
  getFeaturedContent,
  toggleFeaturedStatus,
  FeaturedContent,
} from "../../../../services/adminService";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

export default function FeaturedContentPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [activeTab, setActiveTab] = useState<"home" | "news">("home");
  const queryClient = useQueryClient();

  // Fetch featured content
  const {
    data: featuredContent,
    isLoading: isLoadingFeatured,
    error,
  } = useQuery({
    queryKey: ["featuredContent"],
    queryFn: () => getFeaturedContent(),
    enabled: !!user?.roles?.admin, // Only fetch if user is admin
  });

  // Mutation for toggling featured status
  const toggleFeaturedMutation = useMutation({
    mutationFn: ({ id, type }: { id: string; type: "news" | "hero" }) =>
      toggleFeaturedStatus(id, type),
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ["featuredContent"] });
    },
  });

  // Check authorization after auth is loaded
  useEffect(() => {
    // Only proceed with checks if auth loading is complete
    if (!isLoading) {
      if (!user || !user.roles?.admin) {
        router.push("/");
      } else {
        // User is authorized
        setIsAuthorized(true);
      }
    }
  }, [user, router, isLoading]);

  // Filter content by type
  const heroContent =
    featuredContent?.filter((item) => item.type === "hero") || [];
  const newsContent =
    featuredContent?.filter((item) => item.type === "news") || [];

  // Handle toggle featured status
  const handleToggleFeatured = (id: string, type: "news" | "hero") => {
    toggleFeaturedMutation.mutate({ id, type });
  };

  // Show loading state or nothing while checking authorization
  if (isLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <p className="text-white text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <AdminDashboardLayout>
      <div className="space-y-6">
        <div className="bg-secondary-light rounded-lg shadow-md p-6 border border-gray-600">
          <h2 className="text-2xl font-bold mb-6 text-white">
            Featured Content Management
          </h2>

          <div className="border-b border-gray-600 mb-6">
            <nav className="-mb-px flex space-x-6">
              <button
                onClick={() => setActiveTab("home")}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === "home"
                    ? "border-primary text-primary"
                    : "border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300"
                }`}
              >
                Home Page
              </button>
              <button
                onClick={() => setActiveTab("news")}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === "news"
                    ? "border-primary text-primary"
                    : "border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300"
                }`}
              >
                News
              </button>
            </nav>
          </div>

          {isLoadingFeatured ? (
            <div className="text-center py-8">
              <p className="text-white">Loading featured content...</p>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-red-400">
                Error loading featured content. Please try again later.
              </p>
            </div>
          ) : (
            <>
              {activeTab === "home" && (
                <div>
                  <p className="text-gray-400 mb-6">
                    Manage featured content on the home page. Select items to
                    feature in each section.
                  </p>

                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-white">
                        Hero Banner
                      </h3>
                      {heroContent.length > 0 ? (
                        <div className="bg-secondary p-4 rounded-lg border border-gray-600">
                          {heroContent.map((hero) => (
                            <div
                              key={hero.id}
                              className="flex items-center justify-between"
                            >
                              <div>
                                <p className="text-white font-medium">
                                  Current Hero: {hero.title}
                                </p>
                                <p className="text-gray-400 text-sm">
                                  Last updated:{" "}
                                  {new Date(
                                    hero.updatedAt,
                                  ).toLocaleDateString()}
                                </p>
                              </div>
                              <button
                                className="bg-primary hover:bg-primary-dark text-white px-3 py-1 rounded-md text-sm"
                                onClick={() =>
                                  handleToggleFeatured(hero.id, "hero")
                                }
                              >
                                Change
                              </button>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="bg-secondary p-4 rounded-lg border border-gray-600">
                          <p className="text-gray-400">
                            No hero banners found.
                          </p>
                        </div>
                      )}
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-white">
                        Featured News
                      </h3>
                      <div className="bg-secondary p-4 rounded-lg border border-gray-600">
                        {newsContent.length > 0 ? (
                          <div className="flex flex-col space-y-4">
                            {newsContent.map((article) => (
                              <div
                                key={article.id}
                                className="flex items-center justify-between"
                              >
                                <div className="flex items-center">
                                  <input
                                    type="checkbox"
                                    className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                                    checked={article.featured}
                                    onChange={() =>
                                      handleToggleFeatured(article.id, "news")
                                    }
                                  />
                                  <span className="ml-2 text-white">
                                    {article.title}
                                  </span>
                                </div>
                                <span className="text-gray-400 text-sm">
                                  {article.featured
                                    ? "Featured"
                                    : "Not Featured"}
                                </span>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-gray-400">
                            No news articles found.
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === "news" && (
                <div>
                  <p className="text-gray-400 mb-6">
                    Manage featured news articles on the news page.
                  </p>

                  {newsContent.length > 0 ? (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-600">
                        <thead className="bg-secondary">
                          <tr>
                            <th
                              scope="col"
                              className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                            >
                              Article
                            </th>
                            <th
                              scope="col"
                              className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                            >
                              Last Updated
                            </th>
                            <th
                              scope="col"
                              className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                            >
                              Featured
                            </th>
                            <th
                              scope="col"
                              className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                            >
                              Actions
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-secondary-light divide-y divide-gray-600">
                          {newsContent.map((article) => (
                            <tr key={article.id}>
                              <td className="px-6 py-4">
                                <div className="flex items-center">
                                  <div className="flex-shrink-0 h-10 w-10">
                                    <img
                                      className="h-10 w-10 rounded-md object-cover"
                                      src={
                                        article.image ||
                                        "/images/news/default.jpg"
                                      }
                                      alt={article.title}
                                    />
                                  </div>
                                  <div className="ml-4">
                                    <div className="text-sm font-medium text-white">
                                      {article.title}
                                    </div>
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-400">
                                  {new Date(
                                    article.updatedAt,
                                  ).toLocaleDateString()}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <input
                                  type="checkbox"
                                  className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                                  checked={article.featured}
                                  onChange={() =>
                                    handleToggleFeatured(article.id, "news")
                                  }
                                />
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button
                                  className="text-primary hover:text-primary-light"
                                  onClick={() =>
                                    handleToggleFeatured(article.id, "news")
                                  }
                                >
                                  Toggle Featured
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="bg-secondary p-4 rounded-lg border border-gray-600">
                      <p className="text-gray-400">No news articles found.</p>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </AdminDashboardLayout>
  );
}
