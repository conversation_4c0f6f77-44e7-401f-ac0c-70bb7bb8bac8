"use client";

import React, { useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface BreadcrumbItem {
  label: string;
  path: string;
  originalPath: string;
}

interface BreadcrumbsProps {
  className?: string;
  homeLabel?: string;
  separator?: React.ReactNode;
  toggleMobileNav?: () => void;
  excludePaths?: string[]; // Paths to exclude from breadcrumbs
}

export default function Breadcrumbs({
  className = "",
  homeLabel = "Home",
  separator = "/",
  toggleMobileNav,
  excludePaths = [],
}: BreadcrumbsProps) {
  const pathname = usePathname();

  // Add a useEffect to handle sessionStorage for client-side rendering
  useEffect(() => {
    // Check if current path should be excluded
    const shouldExclude = excludePaths.some((excludePath) =>
      pathname.startsWith(excludePath),
    );

    // Only update sessionStorage if the path is not excluded
    if (!shouldExclude && typeof window !== "undefined") {
      sessionStorage.setItem("lastValidBreadcrumbPath", pathname);
    }
  }, [pathname, excludePaths]);

  // Check if we're on the home page
  const isHomePage = pathname === "/";

  // On home page, only show the container with hamburger menu on mobile view
  if (isHomePage) {
    return (
      <nav
        aria-label="Breadcrumb"
        className={`text-sm flex items-center justify-end pt-0 mr-2 ${className}`}
      >
        {/* Hamburger button - only visible on mobile */}
        {toggleMobileNav && (
          <button
            className="md:hidden ml-auto p-1.5 border-2 border-primary rounded-md hover:bg-secondary-light transition-colors"
            onClick={toggleMobileNav}
            aria-label="Toggle navigation"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              className="w-6 h-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          </button>
        )}
      </nav>
    );
  }

  // Check if current path should be excluded from breadcrumbs
  const shouldExcludePath = excludePaths.some((excludePath) =>
    pathname.startsWith(excludePath),
  );

  // If path should be excluded, use the last non-excluded path from session storage
  // or default to home if none exists
  if (shouldExcludePath) {
    // We're on an excluded path, so don't update the breadcrumbs
    // Instead, try to get the last valid path from sessionStorage
    // Use a try-catch block to handle potential sessionStorage errors
    let lastValidPath = "/";
    try {
      if (typeof window !== "undefined") {
        const storedPath = sessionStorage.getItem("lastValidBreadcrumbPath");
        if (storedPath) {
          lastValidPath = storedPath;
        }
      }
    } catch (error) {
      console.error("Error accessing sessionStorage:", error);
    }
    const lastValidPathSegments = lastValidPath.split("/").filter(Boolean);

    // Create breadcrumb items from the last valid path
    const breadcrumbs: BreadcrumbItem[] = [
      { label: homeLabel, path: "/", originalPath: "/" },
      ...lastValidPathSegments.map((segment, index) => {
        // Create a path up to this segment
        let path = `/${lastValidPathSegments.slice(0, index + 1).join("/")}`;

        // Special handling for admin and sales breadcrumbs
        if (segment === "admin" && index === 0) {
          path = "/admin/dashboard";
        } else if (segment === "sales" && index === 0) {
          path = "/sales/dashboard";
        }

        // Format the label (capitalize first letter, replace hyphens with spaces)
        let label = segment.charAt(0).toUpperCase() + segment.slice(1);
        label = label.replace(/-/g, " ");

        // Handle dynamic routes with [param]
        if (segment.startsWith("[") && segment.endsWith("]")) {
          label = "Details";
        }

        return { label, path, originalPath: `/${lastValidPathSegments.slice(0, index + 1).join("/")}` };
      }),
    ];

    return (
      <nav
        aria-label="Breadcrumb"
        className={`text-sm flex items-center justify-between  py-0 ${className}`}
      >
        <ol className="flex items-center flex-wrap">
          {breadcrumbs.map((crumb, index) => {
            const isLast = index === breadcrumbs.length - 1;

            return (
              <li key={crumb.originalPath || crumb.path} className="flex items-center">
                {index > 0 && (
                  <span className="mx-2 text-gray-400">{separator}</span>
                )}

                {isLast ? (
                  <span className="font-medium text-white" aria-current="page">
                    {crumb.label}
                  </span>
                ) : (
                  <Link
                    href={crumb.path}
                    className="text-primary hover:text-primary-light hover:underline"
                  >
                    {crumb.label}
                  </Link>
                )}
              </li>
            );
          })}
        </ol>

        {/* Hamburger button - only visible on mobile, positioned on the right */}
        {toggleMobileNav && (
          <button
            className="md:hidden p-1.5 border-2 border-primary rounded-md hover:bg-secondary-light transition-colors"
            onClick={toggleMobileNav}
            aria-label="Toggle navigation"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              className="w-6 h-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          </button>
        )}
      </nav>
    );
  }

  // Split the pathname into segments
  const segments = pathname.split("/").filter(Boolean);

  // We're already handling sessionStorage in the useEffect hook

  // Create breadcrumb items
  const breadcrumbs: BreadcrumbItem[] = [
    { label: homeLabel, path: "/", originalPath: "/" },
    ...segments.map((segment, index) => {
      // Create a path up to this segment
      let path = `/${segments.slice(0, index + 1).join("/")}`;

      // Special handling for admin and sales breadcrumbs
      if (segment === "admin" && index === 0) {
        path = "/admin/dashboard";
      } else if (segment === "sales" && index === 0) {
        path = "/sales/dashboard";
      }

      // Format the label (capitalize first letter, replace hyphens with spaces)
      let label = segment.charAt(0).toUpperCase() + segment.slice(1);
      label = label.replace(/-/g, " ");

      // Handle dynamic routes with [param]
      if (segment.startsWith("[") && segment.endsWith("]")) {
        label = "Details";
      }

      return { label, path, originalPath: `/${segments.slice(0, index + 1).join("/")}` };
    }),
  ];

  return (
    <nav
      aria-label="Breadcrumb"
      className={`text-sm flex items-center justify-between px-2 py-0 ${className}`}
    >
      <ol className="flex items-center flex-wrap">
        {breadcrumbs.map((crumb, index) => {
          const isLast = index === breadcrumbs.length - 1;

          return (
            <li key={crumb.originalPath || crumb.path} className="flex items-center">
              {index > 0 && (
                <span className="mx-2 text-gray-400">{separator}</span>
              )}

              {isLast ? (
                <span className="font-medium text-white" aria-current="page">
                  {crumb.label}
                </span>
              ) : (
                <Link
                  href={crumb.path}
                  className="text-primary hover:text-primary-light hover:underline"
                >
                  {crumb.label}
                </Link>
              )}
            </li>
          );
        })}
      </ol>

      {/* Hamburger button - only visible on mobile, positioned on the right */}
      {toggleMobileNav && (
        <button
          className="md:hidden p-1.5 border-2 border-primary rounded-md hover:bg-secondary-light transition-colors"
          onClick={toggleMobileNav}
          aria-label="Toggle navigation"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            className="w-6 h-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 6h16M4 12h16M4 18h16"
            />
          </svg>
        </button>
      )}
    </nav>
  );
}
