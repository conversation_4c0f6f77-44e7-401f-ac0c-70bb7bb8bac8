"use client";
import Link from "next/link";
import { useState, useMemo, useEffect } from "react";
import { DashboardLayout, TransactionItem } from "../../../../components/bank";
import { SearchBar } from "@bank-of-styx/ui";
import { useAuth } from "../../../../contexts/AuthContext";
import { useTransactions } from "../../../../hooks/useBank";

// Transaction types
const TRANSACTION_TYPES = {
  TRANSFER: "transfer",
  DEPOSIT: "deposit",
  WITHDRAWAL: "withdrawal",
  PAY_CODE: "pay-code",
  DONATION: "donation",
};

interface CategoryItem {
  name: string;
  isDisabled?: boolean;
}

export default function TransactionsPage() {
  const { user } = useAuth();
  const [filter, setFilter] = useState<string>("");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [sortOrder, setSortOrder] = useState<"newest" | "oldest">("newest");
  const [selectedCategory, setSelectedCategory] =
    useState<string>("Categories");
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");
  const [showDateFilter, setShowDateFilter] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState(1);
  const transactionsPerPage = 10;

  // Create categories for the dropdown with consistent naming
  const categories = useMemo<CategoryItem[]>(() => {
    return [
      { name: "Categories", isDisabled: true },
      // Match UI categories to transaction types
      { name: "Transfers" },
      { name: "Deposits" },
      { name: "Withdrawals" },
      { name: "Pay-Codes" },
      { name: "Donations" },
    ];
  }, []);

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // Reset to first page when searching

    // If the search query is empty, don't apply any search filter
    if (!query.trim()) {
      onClear();
    }
  };

  // Handle search clear
  const onClear = () => {
    setSearchQuery("");
  };

  // Handle clear search
  const handleClearSearch = () => {
    onClear();
  };

  // Handle category change with proper type mapping
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setCurrentPage(1); // Reset to first page when changing category

    if (category === "Categories") {
      setFilter("");
    } else {
      // Map category names to transaction types
      const categoryMap: Record<string, string> = {
        Transfers: TRANSACTION_TYPES.TRANSFER,
        Deposits: TRANSACTION_TYPES.DEPOSIT,
        Withdrawals: TRANSACTION_TYPES.WITHDRAWAL,
        "Pay-Codes": TRANSACTION_TYPES.PAY_CODE,
        Donations: TRANSACTION_TYPES.DONATION,
      };

      // Set the filter and clear the search when changing category
      setFilter(categoryMap[category] || "");
    }
  };

  // Handle clear filters
  const handleClearFilters = () => {
    setSearchQuery("");
    setSelectedCategory("Categories");
    setFilter("");
    setSortOrder("newest");
    setStartDate("");
    setEndDate("");
    setCurrentPage(1); // Reset to first page when clearing filters
  };

  // Handle date filter toggle
  const toggleDateFilter = () => {
    setShowDateFilter(!showDateFilter);
    if (showDateFilter) {
      // Clear date filters when hiding
      setStartDate("");
      setEndDate("");
    }
  };

  // Handle date input change
  const handleDateChange = (field: "startDate" | "endDate", value: string) => {
    if (field === "startDate") {
      setStartDate(value);
    } else {
      setEndDate(value);
    }
    // Reset to first page when changing date filters
    setCurrentPage(1);
  };

  // Use the transactions API
  const {
    data: apiTransactions = [],
    isLoading,
    error,
  } = useTransactions({
    type: filter || undefined,
    search: searchQuery || undefined,
    startDate: startDate || undefined,
    endDate: endDate || undefined,
  });

  // The API already filters transactions based on type and search query
  // We'll use the apiTransactions directly
  const filteredTransactions = apiTransactions;

  // Log the filter and search parameters for debugging
  useEffect(() => {
    console.log("Filter:", filter);
    console.log("Search:", searchQuery);
    console.log("Sort Order:", sortOrder);
    console.log("Start Date:", startDate);
    console.log("End Date:", endDate);
    console.log("Transactions count:", filteredTransactions.length);
  }, [
    filter,
    searchQuery,
    sortOrder,
    startDate,
    endDate,
    filteredTransactions.length,
  ]);

  // Sort transactions
  const sortedTransactions = useMemo(() => {
    return [...filteredTransactions].sort((a, b) => {
      const dateA = new Date(a.createdAt).getTime();
      const dateB = new Date(b.createdAt).getTime();
      return sortOrder === "newest" ? dateB - dateA : dateA - dateB;
    });
  }, [filteredTransactions, sortOrder]);

  return (
    <DashboardLayout>
      <div className="mb-3">
        <Link
          href="/bank/dashboard"
          className="text-primary hover:text-primary-light"
        >
          &larr; Back to Dashboard
        </Link>
      </div>

      <div className="bg-secondary-light rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-2xl font-bold text-white mb-6">
          Transaction History
        </h2>

        <div className="mb-6">
          <SearchBar
            placeholder="Search transactions..."
            onSearch={handleSearch}
            onClear={handleClearSearch}
            showButton={true}
            initialValue={searchQuery}
            categories={categories}
            selectedCategory={selectedCategory}
            onCategoryChange={handleCategoryChange}
            className="text-sm md:text-base"
          />
        </div>

        <div className="flex flex-col sm:flex-row justify-between items-center mb-6 space-y-3 sm:space-y-0">
          {/* Date filter toggle button - aligned right on mobile */}
          <div className="w-full sm:w-auto flex justify-end sm:justify-start">
            <button
              onClick={toggleDateFilter}
              className="px-3 py-1 bg-secondary hover:bg-secondary-light text-white rounded-md text-sm transition-colors flex items-center"
            >
              <svg
                className="w-4 h-4 mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
              {showDateFilter ? "Hide Date Filter" : "Show Date Filter"}
            </button>
          </div>

          <div className="flex items-center justify-end w-full sm:w-auto space-x-3">
            {(searchQuery ||
              selectedCategory !== "Categories" ||
              sortOrder !== "newest" ||
              startDate ||
              endDate) && (
              <button
                onClick={handleClearFilters}
                className="px-3 py-1 bg-secondary hover:bg-secondary-light text-white rounded-md text-sm transition-colors"
              >
                Clear Filters
              </button>
            )}

            <select
              id="sortOrder"
              value={sortOrder}
              onChange={(e) =>
                setSortOrder(e.target.value as "newest" | "oldest")
              }
              className="px-3 py-1 border border-gray-600 rounded-md bg-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary text-sm"
            >
              <option value="newest">Newest first</option>
              <option value="oldest">Oldest first</option>
            </select>
          </div>
        </div>

        {/* Date filter section */}
        {showDateFilter && (
          <div className="mb-6 p-4 bg-secondary rounded-lg border border-gray-600">
            <h3 className="text-white font-medium mb-3">
              Filter by Date Range
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label
                  htmlFor="startDate"
                  className="block text-sm text-gray-400 mb-1"
                >
                  Start Date
                </label>
                <input
                  type="date"
                  id="startDate"
                  value={startDate}
                  onChange={(e) =>
                    handleDateChange("startDate", e.target.value)
                  }
                  className="w-full px-3 py-2 bg-secondary-dark border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
              <div>
                <label
                  htmlFor="endDate"
                  className="block text-sm text-gray-400 mb-1"
                >
                  End Date
                </label>
                <input
                  type="date"
                  id="endDate"
                  value={endDate}
                  onChange={(e) => handleDateChange("endDate", e.target.value)}
                  className="w-full px-3 py-2 bg-secondary-dark border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
            </div>
            <div className="mt-4 flex justify-end">
              <button
                onClick={toggleDateFilter}
                className="px-4 py-2 bg-secondary text-white rounded-md hover:bg-secondary-light transition-colors border border-primary"
              >
                Hide Date Filter
              </button>
            </div>
          </div>
        )}

        {isLoading ? (
          <div className="space-y-4">
            <div className="animate-pulse h-16 bg-gray-600 rounded w-full"></div>
            <div className="animate-pulse h-16 bg-gray-600 rounded w-full"></div>
            <div className="animate-pulse h-16 bg-gray-600 rounded w-full"></div>
            <div className="animate-pulse h-16 bg-gray-600 rounded w-full"></div>
          </div>
        ) : error ? (
          <div className="text-center py-12 text-error">
            Failed to load transactions. Please try again later.
          </div>
        ) : sortedTransactions.length > 0 ? (
          <>
            <div className="divide-y divide-gray-600">
              {sortedTransactions
                .slice(
                  (currentPage - 1) * transactionsPerPage,
                  currentPage * transactionsPerPage,
                )
                .map((transaction) => (
                  <TransactionItem
                    key={transaction.id}
                    transaction={transaction}
                    compact={true}
                    expandable={true}
                  />
                ))}
            </div>

            <div className="mt-6 flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
              <p className="text-sm text-gray-400">
                Showing{" "}
                {Math.min(
                  currentPage * transactionsPerPage,
                  sortedTransactions.length,
                )}{" "}
                of {sortedTransactions.length} transactions
              </p>

              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                  disabled={currentPage === 1}
                  className={`p-2 rounded-lg border ${
                    currentPage === 1
                      ? "border-gray-600 text-gray-600 cursor-not-allowed"
                      : "border-gray-600 text-white hover:bg-secondary-dark"
                  }`}
                  aria-label="Previous page"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                </button>
                <span className="text-white">
                  Page {currentPage} of{" "}
                  {Math.ceil(sortedTransactions.length / transactionsPerPage)}
                </span>
                <button
                  onClick={() =>
                    setCurrentPage((p) =>
                      Math.min(
                        Math.ceil(
                          sortedTransactions.length / transactionsPerPage,
                        ),
                        p + 1,
                      ),
                    )
                  }
                  disabled={
                    currentPage >=
                    Math.ceil(sortedTransactions.length / transactionsPerPage)
                  }
                  className={`p-2 rounded-lg border ${
                    currentPage >=
                    Math.ceil(sortedTransactions.length / transactionsPerPage)
                      ? "border-gray-600 text-gray-600 cursor-not-allowed"
                      : "border-gray-600 text-white hover:bg-secondary-dark"
                  }`}
                  aria-label="Next page"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-12 text-gray-400">
            <p>No transactions found matching your criteria.</p>
            <p className="mt-2 text-sm">
              Your transaction history will appear here once you make your first
              transaction.
            </p>
            <p className="mt-4 text-sm">
              <span className="bg-secondary px-3 py-1 rounded-md">
                Try using the Transfer, Deposit, or Withdraw features to create
                transactions.
              </span>
            </p>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
