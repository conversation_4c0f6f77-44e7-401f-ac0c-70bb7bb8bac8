import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";
import { sendEmail } from "@/lib/email";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET endpoint to retrieve a specific ticket
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;

    // Check if user is authenticated
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Check if user is admin or the ticket owner
    const isAdmin = await userHasRole(request, "admin");

    // Get the ticket with notes
    const ticket = await prisma.supportTicket.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        resolvedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        notes: {
          where: isAdmin
            ? {} // Admins can see all notes
            : { isInternal: false }, // Non-admins can only see public notes
          include: {
            author: {
              select: {
                id: true,
                username: true,
                displayName: true,
                avatar: true,
              },
            },
          },
          orderBy: {
            createdAt: "asc",
          },
        },
      },
    });

    if (!ticket) {
      return NextResponse.json({ error: "Ticket not found" }, { status: 404 });
    }

    // Check if user has permission to view this ticket
    if (!isAdmin && ticket.userId !== currentUser.id) {
      return NextResponse.json(
        { error: "You don't have permission to view this ticket" },
        { status: 403 },
      );
    }

    return NextResponse.json(ticket);
  } catch (error) {
    console.error("Error fetching ticket:", error);
    return NextResponse.json(
      { error: "Failed to fetch ticket" },
      { status: 500 },
    );
  }
}

// PATCH endpoint to update a ticket
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;

    // Check if user is authenticated and has admin role
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const isAdmin = await userHasRole(request, "admin");
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Admin privileges required" },
        { status: 403 },
      );
    }

    // Get the existing ticket
    const existingTicket = await prisma.supportTicket.findUnique({
      where: { id },
    });

    if (!existingTicket) {
      return NextResponse.json({ error: "Ticket not found" }, { status: 404 });
    }

    // Parse the request body
    const body = await request.json();
    const { status, priority, category, assignedToId, resolution } = body;

    // Prepare update data
    const updateData: any = {};

    if (status) {
      updateData.status = status;

      // If status is being set to resolved, add resolution info
      if (status === "resolved" && existingTicket.status !== "resolved") {
        updateData.resolvedById = currentUser.id;
        updateData.resolvedAt = new Date();
      }
    }

    if (priority) {
      updateData.priority = priority;
    }

    if (category) {
      updateData.category = category;
    }

    if (assignedToId) {
      updateData.assignedToId = assignedToId;
      updateData.assignedAt = new Date();
    }

    if (resolution) {
      updateData.resolution = resolution;
    }

    // Update the ticket
    const updatedTicket = await prisma.supportTicket.update({
      where: { id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        resolvedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
      },
    });

    // If the ticket is resolved, send an email to the user
    if (status === "resolved" && existingTicket.status !== "resolved") {
      const emailText = `
        Your support ticket has been resolved:
        
        Ticket ID: ${updatedTicket.id}
        Subject: ${updatedTicket.subject}
        
        Resolution:
        ${resolution || "Your issue has been resolved by our support team."}
        
        If you have any further questions, please reply to this email or create a new support ticket.
        
        Thank you for contacting Bank of Styx support.
      `;

      const emailHtml = `
        <h2>Your Support Ticket Has Been Resolved</h2>
        <p><strong>Ticket ID:</strong> ${updatedTicket.id}</p>
        <p><strong>Subject:</strong> ${updatedTicket.subject}</p>
        <hr>
        <h3>Resolution:</h3>
        <p>${(
          resolution || "Your issue has been resolved by our support team."
        ).replace(/\n/g, "<br>")}</p>
        <p>If you have any further questions, please reply to this email or create a new support ticket.</p>
        <p>Thank you for contacting Bank of Styx support.</p>
      `;

      await sendEmail({
        to: updatedTicket.email,
        subject: `Support Ticket Resolved: ${updatedTicket.subject}`,
        text: emailText,
        html: emailHtml,
      });
    }

    return NextResponse.json(updatedTicket);
  } catch (error) {
    console.error("Error updating ticket:", error);
    return NextResponse.json(
      { error: "Failed to update ticket" },
      { status: 500 },
    );
  }
}
