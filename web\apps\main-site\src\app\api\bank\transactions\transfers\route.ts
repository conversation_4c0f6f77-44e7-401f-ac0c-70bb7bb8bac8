import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../../lib/prisma";
import jwt from "jsonwebtoken";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
const JWT_SECRET = process.env.JWT_SECRET || "your-development-secret-key";

// Helper function to verify token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

export const GET = async (req: NextRequest) => {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Get limit from query parameters
    const url = new URL(req.url);
    const limitParam = url.searchParams.get("limit");
    const limit = limitParam ? parseInt(limitParam, 10) : 5;

    // Query the database for recent transfers
    const transfers = await prisma.transaction.findMany({
      where: {
        OR: [{ senderId: userId }, { recipientId: userId }],
        type: "transfer",
      },
      include: {
        sender: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        recipient: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
    });

    // Format dates as ISO strings for JSON serialization
    const formattedTransfers = transfers.map((transfer) => ({
      ...transfer,
      createdAt: transfer.createdAt.toISOString(),
      updatedAt: transfer.updatedAt.toISOString(),
      processedAt: transfer.processedAt
        ? transfer.processedAt.toISOString()
        : null,
    }));

    return NextResponse.json(formattedTransfers);
  } catch (error) {
    console.error("Error fetching transfers:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
