import React, { useState } from "react";
import { useProcessTransaction } from "../../hooks/useBank";
import { Transaction } from "../../services/bankService";
import { toast } from "react-hot-toast";
import { DepositCard } from "./DepositCard";
import {
  getStandardizedImageUrl,
  getImageErrorHandler,
} from "../../lib/imageDisplayUtils";

interface DepositsManagerProps {
  deposits: Transaction[];
  onRefresh: () => void;
}

export const DepositsManager: React.FC<DepositsManagerProps> = ({
  deposits,
  onRefresh,
}) => {
  const [selectedDeposit, setSelectedDeposit] = useState<Transaction | null>(
    null,
  );
  const [note, setNote] = useState("");
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [action, setAction] = useState<"approve" | "reject" | null>(null);
  const [viewMode, setViewMode] = useState<"card" | "table">("card");

  const processTransaction = useProcessTransaction();

  const handleSelectDeposit = (deposit: Transaction) => {
    setSelectedDeposit(deposit);
    setNote("");
  };

  const handleBackToList = () => {
    setSelectedDeposit(null);
    setNote("");
  };

  const handleApprove = () => {
    setAction("approve");
    setShowConfirmation(true);
  };

  const handleReject = () => {
    setAction("reject");
    setShowConfirmation(true);
  };

  const handleConfirm = async () => {
    if (!selectedDeposit || !action) return;

    try {
      await processTransaction.mutateAsync({
        id: selectedDeposit.id,
        data: {
          status: action === "approve" ? "approved" : "rejected",
          note: note || undefined,
        },
      });

      toast.success(
        `Deposit ${
          action === "approve" ? "approved" : "rejected"
        } successfully!`,
      );

      setSelectedDeposit(null);
      setShowConfirmation(false);
      setAction(null);
      setNote("");
      onRefresh();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "An error occurred";
      toast.error(errorMessage);
    }
  };

  const handleCancel = () => {
    setShowConfirmation(false);
    setAction(null);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + " " + date.toLocaleTimeString();
  };

  return (
    <div className="w-full">
      {!selectedDeposit ? (
        <div>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
            <h3 className="text-xl font-semibold text-white mb-2 sm:mb-0">
              Pending Deposits ({deposits.length})
            </h3>

            {/* View toggle buttons - Only on tablet and desktop */}
            <div className="flex space-x-2">
              <button
                onClick={() => setViewMode("card")}
                className={`flex items-center px-3 py-1 rounded-md ${
                  viewMode === "card"
                    ? "bg-primary text-white"
                    : "bg-secondary text-gray-400 hover:bg-secondary-dark"
                }`}
                aria-label="Card view"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                  />
                </svg>
                <span className="ml-1 hidden sm:inline">Cards</span>
              </button>
              <button
                onClick={() => setViewMode("table")}
                className={`flex items-center px-3 py-1 rounded-md ${
                  viewMode === "table"
                    ? "bg-primary text-white"
                    : "bg-secondary text-gray-400 hover:bg-secondary-dark"
                }`}
                aria-label="Table view"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </svg>
                <span className="ml-1 hidden sm:inline">Table</span>
              </button>
            </div>
          </div>

          {deposits.length === 0 ? (
            <div className="bg-secondary-dark p-4 rounded-lg border border-gray-600 text-center">
              <p className="text-gray-400">No pending deposits found.</p>
            </div>
          ) : viewMode === "card" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {deposits.map((deposit) => (
                <DepositCard
                  key={deposit.id}
                  deposit={deposit}
                  onSelect={handleSelectDeposit}
                />
              ))}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-secondary-dark">
                    <th className="p-3 text-left text-white">ID</th>
                    <th className="p-3 text-left text-white">User</th>
                    <th className="p-3 text-left text-white">Date</th>
                    <th className="p-3 text-left text-white">Amount</th>
                    <th className="p-3 text-left text-white">Description</th>
                    <th className="p-3 text-left text-white">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {deposits.map((deposit) => (
                    <tr
                      key={deposit.id}
                      className="border-t border-gray-600 hover:bg-secondary-dark"
                    >
                      <td className="p-3 text-white">{deposit.id}</td>
                      <td className="p-3 text-white">
                        {deposit.sender?.displayName || "Unknown"}
                      </td>
                      <td className="p-3 text-white">
                        {formatDate(deposit.createdAt)}
                      </td>
                      <td className="p-3 text-success">
                        NS {deposit.amount.toFixed(0)}
                      </td>
                      <td className="p-3 text-white">{deposit.description}</td>
                      <td className="p-3">
                        <button
                          onClick={() => handleSelectDeposit(deposit)}
                          className="px-3 py-1 bg-primary text-white rounded-md hover:bg-primary-light"
                        >
                          Review
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      ) : (
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold text-white">
              Review Deposit: {selectedDeposit.id}
            </h3>
            <button
              onClick={handleBackToList}
              className="px-3 py-1 bg-secondary text-white rounded-md hover:bg-secondary-light"
            >
              Back to List
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-secondary-dark p-4 rounded-lg border border-gray-600">
              <h4 className="font-semibold text-white mb-3">Deposit Details</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-400">User:</span>
                  <span className="text-white">
                    {selectedDeposit.sender?.displayName || "Unknown"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Amount:</span>
                  <span className="text-success">
                    NS {selectedDeposit.amount.toFixed(0)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Date:</span>
                  <span className="text-white">
                    {formatDate(selectedDeposit.createdAt)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Description:</span>
                  <span className="text-white">
                    {selectedDeposit.description}
                  </span>
                </div>
                {selectedDeposit.note && (
                  <div className="flex justify-between">
                    <span className="text-gray-400">User Note:</span>
                    <span className="text-white">{selectedDeposit.note}</span>
                  </div>
                )}
                {selectedDeposit.paymentMethod && (
                  <div className="flex justify-between">
                    <span className="text-gray-400">Payment Method:</span>
                    <span className="text-white">
                      {selectedDeposit.paymentMethod}
                    </span>
                  </div>
                )}
              </div>
            </div>

            <div className="bg-secondary-dark p-4 rounded-lg border border-gray-600">
              <h4 className="font-semibold text-white mb-3">Receipt</h4>
              {selectedDeposit && selectedDeposit.receiptImage ? (
                <div className="flex justify-center">
                  <div className="relative w-full">
                    <img
                      src={
                        selectedDeposit.receiptImage &&
                        selectedDeposit.receiptImage.includes("deposit")
                          ? `/uploads/deposits/${selectedDeposit.receiptImage
                              .split("/")
                              .pop()}`
                          : selectedDeposit.receiptImage
                      }
                      alt="Receipt"
                      className="max-w-full max-h-64 object-contain mx-auto"
                      onError={getImageErrorHandler(
                        selectedDeposit.receiptImage,
                      )}
                    />
                  </div>
                </div>
              ) : (
                <p className="text-gray-400 text-center">
                  No receipt image available
                </p>
              )}
            </div>
          </div>

          <div className="mt-6 bg-secondary-dark p-4 rounded-lg border border-gray-600">
            <h4 className="font-semibold text-white mb-3">Cashier Note</h4>
            <textarea
              value={note}
              onChange={(e) => setNote(e.target.value)}
              className="w-full bg-secondary border border-gray-600 rounded-md p-2 text-white"
              rows={3}
              placeholder="Add a note about this transaction..."
            />
          </div>

          <div className="mt-6 flex space-x-4">
            <button
              onClick={handleApprove}
              className="px-4 py-2 bg-success text-white rounded-md hover:bg-green-600 flex-1"
            >
              Approve Deposit
            </button>
            <button
              onClick={handleReject}
              className="px-4 py-2 bg-error text-white rounded-md hover:bg-red-600 flex-1"
            >
              Reject Deposit
            </button>
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-secondary-dark p-6 rounded-lg border border-gray-600 max-w-md w-full">
            <h3 className="text-xl font-semibold text-white mb-4">
              Confirm {action === "approve" ? "Approval" : "Rejection"}
            </h3>
            <p className="text-gray-400 mb-4">
              Are you sure you want to{" "}
              {action === "approve" ? "approve" : "reject"} this deposit?
              {action === "approve"
                ? " This will add funds to the user's account."
                : " This will decline the deposit request."}
            </p>
            <div className="flex space-x-4">
              <button
                onClick={handleConfirm}
                className={`px-4 py-2 text-white rounded-md flex-1 ${
                  action === "approve"
                    ? "bg-success hover:bg-green-600"
                    : "bg-error hover:bg-red-600"
                }`}
              >
                {action === "approve" ? "Approve" : "Reject"}
              </button>
              <button
                onClick={handleCancel}
                className="px-4 py-2 bg-secondary text-white rounded-md hover:bg-secondary-light flex-1"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DepositsManager;
