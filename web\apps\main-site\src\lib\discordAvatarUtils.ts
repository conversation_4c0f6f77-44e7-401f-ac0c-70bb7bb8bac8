/**
 * Utility functions for handling Discord avatar images
 */
import { saveImage } from "@/services/imageUploadService";

/**
 * Downloads a Discord avatar and saves it to local storage
 *
 * @param discordId Discord user ID
 * @param avatarHash Discord avatar hash
 * @param localUserId Our database user ID
 * @returns URL to the locally stored avatar
 */
export async function downloadAndSaveDiscordAvatar(
  discordId: string,
  avatarHash: string,
  localUserId: string,
): Promise<string> {
  try {
    // Construct Discord CDN URL
    const discordAvatarUrl = `https://cdn.discordapp.com/avatars/${discordId}/${avatarHash}.png?size=256`;

    console.log(`Downloading Discord avatar from: ${discordAvatarUrl}`);

    // Download the image
    const response = await fetch(discordAvatarUrl);
    if (!response.ok) {
      throw new Error(
        `Failed to download Discord avatar: ${response.statusText}`,
      );
    }

    // Get the image data as Array<PERSON>uffer
    const imageData = await response.arrayBuffer();

    // Check if we got valid image data
    if (!imageData || imageData.byteLength === 0) {
      throw new Error("Received empty image data from Discord CDN");
    }

    console.log(`Downloaded Discord avatar: ${imageData.byteLength} bytes`);

    // Convert to File object with a consistent filename format
    // This ensures we can find and update existing records
    const file = new File(
      [imageData],
      `discord-${discordId}-${avatarHash}.png`,
      { type: "image/png" },
    );

    // Save the image using our existing service
    const savedImage = await saveImage(file, {
      entityType: "avatar",
      entityId: localUserId,
      directory: "avatars",
    });

    console.log(`Saved Discord avatar to: ${savedImage.url}`);

    // Return the URL to the locally stored image
    return savedImage.url;
  } catch (error) {
    console.error("Error downloading Discord avatar:", error);
    // Don't silently fail - throw the error so we can handle it properly
    throw new Error(
      `Failed to download and save Discord avatar: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    );
  }
}
