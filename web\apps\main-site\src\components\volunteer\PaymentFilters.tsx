import React, { useState } from "react";
import { Button } from "@bank-of-styx/ui";
import { EventSelector, CategorySelector } from "@/components/volunteer";
import { PaymentFilters as FilterType } from "@/hooks/useVolunteerPayments";
import {
  useVolunteerUserSearch,
  VolunteerUser,
} from "@/hooks/useVolunteerUsers";

interface PaymentFiltersProps {
  onFilterChange: (filters: FilterType) => void;
  initialFilters?: FilterType;
}

export default function PaymentFilters({
  onFilterChange,
  initialFilters = {},
}: PaymentFiltersProps) {
  // Initialize state from props, but don't track initialFilters changes
  const [selectedEventId, setSelectedEventId] = useState<string | undefined>(
    initialFilters.eventId,
  );
  const [selectedCategoryId, setSelectedCategoryId] = useState<
    string | undefined
  >(initialFilters.categoryId);
  const [startDate, setStartDate] = useState<string | undefined>(
    initialFilters.startDate,
  );
  const [endDate, setEndDate] = useState<string | undefined>(
    initialFilters.endDate,
  );
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [selectedUserId, setSelectedUserId] = useState<string | undefined>(
    initialFilters.userId,
  );
  const [selectedUserName, setSelectedUserName] = useState<string>("");
  const [status, setStatus] = useState<string>(
    initialFilters.status || "pending",
  );

  // User search hook
  const { data: searchResults, refetch: searchUsers } =
    useVolunteerUserSearch(searchQuery);

  // Handle event selection
  const handleEventSelect = (eventId: string) => {
    setSelectedEventId(eventId);
    // Reset category when event changes
    setSelectedCategoryId(undefined);

    // Update filters
    const newFilters: FilterType = {};
    if (eventId) newFilters.eventId = eventId;
    if (startDate) newFilters.startDate = startDate;
    if (endDate) newFilters.endDate = endDate;
    if (selectedUserId) newFilters.userId = selectedUserId;
    if (status) newFilters.status = status;

    onFilterChange(newFilters);
  };

  // Handle category selection
  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategoryId(categoryId);

    // Update filters
    const newFilters: FilterType = {};
    if (selectedEventId) newFilters.eventId = selectedEventId;
    if (categoryId) newFilters.categoryId = categoryId;
    if (startDate) newFilters.startDate = startDate;
    if (endDate) newFilters.endDate = endDate;
    if (selectedUserId) newFilters.userId = selectedUserId;
    if (status) newFilters.status = status;

    onFilterChange(newFilters);
  };

  // Handle date changes
  const handleStartDateChange = (date: string) => {
    setStartDate(date);

    // Update filters
    const newFilters: FilterType = {};
    if (selectedEventId) newFilters.eventId = selectedEventId;
    if (selectedCategoryId) newFilters.categoryId = selectedCategoryId;
    if (date) newFilters.startDate = date;
    if (endDate) newFilters.endDate = endDate;
    if (selectedUserId) newFilters.userId = selectedUserId;
    if (status) newFilters.status = status;

    onFilterChange(newFilters);
  };

  const handleEndDateChange = (date: string) => {
    setEndDate(date);

    // Update filters
    const newFilters: FilterType = {};
    if (selectedEventId) newFilters.eventId = selectedEventId;
    if (selectedCategoryId) newFilters.categoryId = selectedCategoryId;
    if (startDate) newFilters.startDate = startDate;
    if (date) newFilters.endDate = date;
    if (selectedUserId) newFilters.userId = selectedUserId;
    if (status) newFilters.status = status;

    onFilterChange(newFilters);
  };

  // Handle user search
  const handleUserSearch = (query: string) => {
    setSearchQuery(query);

    // Clear selected user if search field is cleared
    if (!query) {
      setSelectedUserId(undefined);
      setSelectedUserName("");

      // Update filters to remove user filter
      const newFilters: FilterType = {};
      if (selectedEventId) newFilters.eventId = selectedEventId;
      if (selectedCategoryId) newFilters.categoryId = selectedCategoryId;
      if (startDate) newFilters.startDate = startDate;
      if (endDate) newFilters.endDate = endDate;
      if (status) newFilters.status = status;

      onFilterChange(newFilters);
    }

    // Only search if query is at least 2 characters
    if (query.length >= 2) {
      searchUsers();
    }
  };

  // Handle user selection
  const handleUserSelect = (userId: string, userName: string) => {
    setSelectedUserId(userId);
    setSelectedUserName(userName);
    setSearchQuery("");

    // Update filters
    const newFilters: FilterType = {};
    if (selectedEventId) newFilters.eventId = selectedEventId;
    if (selectedCategoryId) newFilters.categoryId = selectedCategoryId;
    if (startDate) newFilters.startDate = startDate;
    if (endDate) newFilters.endDate = endDate;
    if (userId) newFilters.userId = userId;
    if (status) newFilters.status = status;

    onFilterChange(newFilters);
  };

  // Handle status selection
  const handleStatusChange = (newStatus: string) => {
    setStatus(newStatus);

    // Update filters
    const newFilters: FilterType = {};
    if (selectedEventId) newFilters.eventId = selectedEventId;
    if (selectedCategoryId) newFilters.categoryId = selectedCategoryId;
    if (startDate) newFilters.startDate = startDate;
    if (endDate) newFilters.endDate = endDate;
    if (selectedUserId) newFilters.userId = selectedUserId;
    if (newStatus) newFilters.status = newStatus;

    onFilterChange(newFilters);
  };

  // Clear all filters
  const handleClearFilters = () => {
    setSelectedEventId(undefined);
    setSelectedCategoryId(undefined);
    setStartDate(undefined);
    setEndDate(undefined);
    setSelectedUserId(undefined);
    setSelectedUserName("");
    setStatus("pending");

    // Update filters with just the default status
    onFilterChange({ status: "pending" });
  };

  return (
    <div className="bg-secondary-light rounded-lg shadow-md p-4 border border-gray-600 mb-6">
      <h2 className="text-xl font-semibold text-white mb-4">Filter Payments</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        {/* Event Selector */}
        <div>
          <label className="block text-gray-300 mb-1">Event</label>
          <EventSelector
            onEventSelect={handleEventSelect}
            initialEventId={selectedEventId || ""}
          />
        </div>

        {/* Category Selector (only enabled if event is selected) */}
        <div>
          <label className="block text-gray-300 mb-1">Category</label>
          <CategorySelector
            eventId={selectedEventId || ""}
            onCategorySelect={handleCategorySelect}
            initialCategoryId={selectedCategoryId || ""}
            disabled={!selectedEventId}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        {/* Date Range */}
        <div>
          <label className="block text-gray-300 mb-1">Start Date</label>
          <input
            type="date"
            value={startDate || ""}
            onChange={(e) => handleStartDateChange(e.target.value)}
            className="w-full bg-secondary-dark text-white border border-gray-600 rounded p-2"
          />
        </div>
        <div>
          <label className="block text-gray-300 mb-1">End Date</label>
          <input
            type="date"
            value={endDate || ""}
            onChange={(e) => handleEndDateChange(e.target.value)}
            className="w-full bg-secondary-dark text-white border border-gray-600 rounded p-2"
          />
        </div>
      </div>

      {/* User Search */}
      <div className="mb-4">
        <label className="block text-gray-300 mb-1">Volunteer</label>
        <div className="relative">
          <input
            type="text"
            value={selectedUserName || searchQuery}
            onChange={(e) => handleUserSearch(e.target.value)}
            placeholder="Search for a volunteer..."
            className="w-full bg-secondary-dark text-white border border-gray-600 rounded p-2"
          />

          {/* Search Results Dropdown */}
          {searchQuery.length >= 2 && (
            <div className="absolute z-10 w-full mt-1 bg-secondary-dark border border-gray-600 rounded shadow-lg max-h-60 overflow-y-auto">
              {searchResults && searchResults.length > 0 ? (
                searchResults.map((user: VolunteerUser) => (
                  <div
                    key={user.id}
                    className="p-2 hover:bg-gray-700 cursor-pointer"
                    onClick={() => handleUserSelect(user.id, user.displayName)}
                  >
                    <div className="flex items-center">
                      {user.avatar && (
                        <img
                          src={user.avatar}
                          alt={user.displayName}
                          className="w-8 h-8 rounded-full mr-2"
                        />
                      )}
                      <div>
                        <div className="text-white">{user.displayName}</div>
                        <div className="text-gray-400 text-sm">
                          @{user.username}
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-2 text-gray-400">
                  No users found. Try a different search term.
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Status Filter */}
      <div className="mb-4">
        <label className="block text-gray-300 mb-1">Payment Status</label>
        <div className="flex flex-wrap gap-2">
          {["pending", "processing", "paid", "cancelled"].map(
            (statusOption) => (
              <button
                key={statusOption}
                className={`px-3 py-1 rounded text-sm ${
                  status === statusOption
                    ? "bg-primary text-white"
                    : "bg-secondary-dark text-gray-300"
                }`}
                onClick={() => handleStatusChange(statusOption)}
              >
                {statusOption.charAt(0).toUpperCase() + statusOption.slice(1)}
              </button>
            ),
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end mt-4">
        <Button
          variant="secondary"
          onClick={handleClearFilters}
          className="mr-2"
        >
          Clear Filters
        </Button>
      </div>
    </div>
  );
}
