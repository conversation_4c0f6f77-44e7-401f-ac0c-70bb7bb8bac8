"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "../../../../../contexts/AuthContext";
import {
  CashierDashboardLayout,
  MemberProfile,
  MemberTransactionStats,
  MemberTransactionHistory,
} from "../../../../../components/cashier";
import { useMemberDetails } from "../../../../../hooks/useBank";

export default function MemberDetailsPage({
  params,
}: {
  params: { id: string };
}) {
  const { user, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);

  // Get member details
  const {
    data: memberData,
    isLoading: isLoadingMember,
    error,
  } = useMemberDetails(params.id);

  // Check if user is authorized to access this page
  useEffect(() => {
    if (!authLoading && user) {
      if (user.roles?.banker || user.roles?.admin) {
        setIsAuthorized(true);
      } else {
        router.push("/bank/dashboard");
      }
    }
  }, [user, authLoading, router]);

  // Show loading state or nothing while checking authorization
  if (authLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <p className="text-white text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <CashierDashboardLayout>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-white mb-2">Member Details</h1>
          <p className="text-gray-400">
            View detailed information about this member.
          </p>
        </div>
        <Link
          href="/cashier/dashboard/members"
          className="bg-secondary hover:bg-secondary-dark text-white py-2 px-4 rounded-md transition-colors"
        >
          ← Back to Member Lookup
        </Link>
      </div>

      {isLoadingMember ? (
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="text-white mt-4">Loading member data...</p>
        </div>
      ) : error ? (
        <div className="bg-error/20 border border-error text-white p-6 rounded-lg">
          <h3 className="text-xl font-bold mb-2">Error Loading Member</h3>
          <p>
            {error instanceof Error
              ? error.message
              : "Failed to load member data"}
          </p>
          <Link
            href="/cashier/dashboard/members"
            className="inline-block mt-4 bg-secondary hover:bg-secondary-dark text-white py-2 px-4 rounded-md transition-colors"
          >
            Return to Member Lookup
          </Link>
        </div>
      ) : memberData ? (
        <>
          <MemberProfile member={memberData.user} />
          <MemberTransactionStats
            transactions={memberData.transactions}
            userId={memberData.user.id}
          />
          <MemberTransactionHistory
            transactions={memberData.transactions}
            userId={memberData.user.id}
          />
        </>
      ) : (
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600 text-center">
          <p className="text-white">No member data found.</p>
        </div>
      )}
    </CashierDashboardLayout>
  );
}
