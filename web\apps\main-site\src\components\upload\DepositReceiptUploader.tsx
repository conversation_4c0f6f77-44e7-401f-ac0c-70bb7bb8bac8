import React, { useState } from "react";
import UniversalImageUploader from "../common/UniversalImageUploader";
import { UploadResponse } from "@/types/upload";
import { uploadConfig } from "@/lib/uploadConfig";

interface DepositReceiptUploaderProps {
  depositId: string;
  onUploadComplete: (response: UploadResponse) => void;
  onUploadStart?: () => void;
  onReceiptProcessed?: (data: any) => void;
  allowPDF?: boolean;
  className?: string;
  label?: string;
  description?: string;
}

const DepositReceiptUploader: React.FC<DepositReceiptUploaderProps> = ({
  depositId,
  onUploadComplete,
  onUploadStart,
  onReceiptProcessed,
  allowPDF = true,
  className = "",
  label = "Upload Deposit Receipt",
  description = "Upload a clear photo or scan of your deposit receipt",
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [uploadedFile, setUploadedFile] = useState<any>(null);

  const handleUploadStart = () => {
    setIsUploading(true);
    onUploadStart?.();
  };

  const handleUploadComplete = async (response: UploadResponse) => {
    setIsUploading(false);

    if (response.success) {
      // Handle both new format (response.file.url) and legacy format (response.url)
      const fileUrl = response.file?.url || response.url;
      const fileData = response.file;

      if (fileUrl) {
        setPreviewUrl(fileUrl);
        setUploadedFile(fileData);
      }

      // Process receipt if callback is provided
      if (onReceiptProcessed && fileData) {
        try {
          setIsProcessing(true);
          // Here you would typically make an API call to process the receipt
          // For now, we'll simulate processing
          await new Promise((resolve) => setTimeout(resolve, 2000));

          const mockProcessedData = {
            receiptId: fileData.id,
            amount: null, // Would be extracted from receipt
            date: null, // Would be extracted from receipt
            merchant: null, // Would be extracted from receipt
            confidence: 0.95,
            processingTime: new Date().toISOString(),
          };

          onReceiptProcessed(mockProcessedData);
        } catch (error) {
          console.error("Error processing receipt:", error);
        } finally {
          setIsProcessing(false);
        }
      }
    }

    onUploadComplete(response);
  };

  const handleRemoveReceipt = () => {
    setPreviewUrl(null);
    setUploadedFile(null);
    // Call onUploadComplete with a "removed" status
    onUploadComplete({
      success: true,
      message: "Receipt removed",
    });
  };

  const getFileIcon = (fileType: string) => {
    if (fileType === "application/pdf") {
      return (
        <svg
          className="w-8 h-8 text-red-600"
          fill="currentColor"
          viewBox="0 0 24 24"
        >
          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
        </svg>
      );
    }
    return (
      <svg
        className="w-8 h-8 text-green-600"
        fill="currentColor"
        viewBox="0 0 24 24"
      >
        <path d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z" />
      </svg>
    );
  };

  // Filter allowed types based on allowPDF prop
  const allowedTypes = allowPDF
    ? uploadConfig.deposit.allowedTypes
    : uploadConfig.deposit.allowedTypes.filter(
        (type) => type !== "application/pdf",
      );

  return (
    <div className={`space-y-4 ${className}`}>
      <div>
        <h3 className="text-lg font-medium text-gray-900">{label}</h3>
        <p className="text-sm text-gray-500 mt-1">{description}</p>
      </div>

      {/* File Preview */}
      {previewUrl && uploadedFile && (
        <div className="relative border border-gray-300 rounded-lg p-4 bg-gray-50">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              {getFileIcon(uploadedFile.mimeType)}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {uploadedFile.filename || "Deposit Receipt"}
              </p>
              <p className="text-sm text-gray-500">
                {uploadedFile.mimeType === "application/pdf"
                  ? "PDF Document"
                  : "Image File"}{" "}
                • {(uploadedFile.size / 1024).toFixed(1)} KB
              </p>
              {uploadedFile.dimensions && (
                <p className="text-xs text-gray-400">
                  {uploadedFile.dimensions.width}x
                  {uploadedFile.dimensions.height}px
                </p>
              )}
            </div>
            <button
              type="button"
              onClick={handleRemoveReceipt}
              disabled={isUploading || isProcessing}
              className="flex-shrink-0 text-red-600 hover:text-red-800 disabled:opacity-50"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Image preview (only for images, not PDFs) */}
          {uploadedFile.mimeType.startsWith("image/") && (
            <div className="mt-3">
              <img
                src={previewUrl}
                alt="Receipt preview"
                className="max-w-full h-48 object-contain rounded border"
              />
            </div>
          )}
        </div>
      )}

      <UniversalImageUploader
        uploadType="deposit"
        entityId={depositId}
        onUploadComplete={handleUploadComplete}
        onUploadStart={handleUploadStart}
        options={{
          maxSize: uploadConfig.deposit.maxSize,
          allowedTypes,
          quality: uploadConfig.deposit.quality,
          processImage: uploadConfig.deposit.processImage,
        }}
      />

      <div className="text-sm text-gray-500 space-y-1">
        <p>
          • Max file size:{" "}
          {(uploadConfig.deposit.maxSize / (1024 * 1024)).toFixed(1)}MB
        </p>
        <p>
          • Accepted formats:{" "}
          {allowedTypes
            .map((type) => {
              const ext = type.split("/")[1];
              return ext === "pdf" ? "PDF" : ext.toUpperCase();
            })
            .join(", ")}
        </p>
        <p>• Make sure all text and numbers are clearly visible</p>
        <p>• Ensure the receipt is well-lit and not blurry</p>
        {allowPDF && <p>• PDF files are accepted for scanned receipts</p>}
      </div>

      {(isUploading || isProcessing) && (
        <div className="mt-2 text-sm text-blue-600 flex items-center">
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          {isUploading ? "Uploading receipt..." : "Processing receipt data..."}
        </div>
      )}
    </div>
  );
};

export default DepositReceiptUploader;
