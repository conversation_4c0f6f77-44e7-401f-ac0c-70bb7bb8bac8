"use client";

import React from "react";
import { useNotifications } from "../../contexts/NotificationContext";
import { Notification } from "../../services/notificationService";

interface NotificationItemProps {
  notification: Notification;
  timeAgo: string;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  timeAgo,
}) => {
  const { deleteNotification } = useNotifications();

  // Get icon based on notification category and type
  const getIcon = () => {
    // Default icon
    let icon = (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-6 w-6"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
    );

    // Use custom icon if provided
    if (notification.icon) {
      return (
        <img
          src={notification.icon}
          alt=""
          className="h-6 w-6 object-cover rounded-full"
        />
      );
    }

    // Category-specific icons
    switch (notification.category) {
      case "transaction":
        icon = (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        );
        break;
      case "auction":
        icon = (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
            />
          </svg>
        );
        break;
      case "news":
        icon = (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"
            />
          </svg>
        );
        break;
      case "admin":
        icon = (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"
            />
          </svg>
        );
        break;
      case "chat":
        icon = (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
            />
          </svg>
        );
        break;
    }

    return icon;
  };

  // Get priority color
  const getPriorityColor = () => {
    switch (notification.priority) {
      case "high":
        return "bg-red-500";
      case "medium":
        return "bg-yellow-500";
      case "low":
      default:
        return "bg-blue-500";
    }
  };

  // Handle click on notification
  const handleClick = () => {
    // Delete all notifications when clicked
    deleteNotification(notification.id);
  };

  // Render notification content
  const renderContent = () => {
    return (
      <div
        className={`flex p-3 rounded-lg ${
          notification.read ? "bg-secondary" : "bg-secondary-light"
        } hover:bg-secondary-dark transition-colors cursor-pointer`}
        onClick={handleClick}
      >
        <div className="flex-shrink-0 mr-3">
          <div className="relative">
            <div className="h-10 w-10 rounded-full bg-secondary-dark flex items-center justify-center">
              {getIcon()}
            </div>
            {!notification.read && (
              <span
                className={`absolute top-0 right-0 block h-2.5 w-2.5 rounded-full ${getPriorityColor()} ring-2 ring-secondary-dark`}
              ></span>
            )}
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-white truncate">
            {notification.title}
          </p>
          <p className="text-sm text-gray-400 line-clamp-2">
            {notification.message}
          </p>
          <p className="text-xs text-gray-500 mt-1">{timeAgo}</p>
        </div>
      </div>
    );
  };

  return renderContent();
};

export default NotificationItem;
