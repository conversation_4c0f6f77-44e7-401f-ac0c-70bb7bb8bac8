import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// POST /api/volunteer/management/payments/process - Process volunteer payments
export async function POST(req: NextRequest) {
  try {
    // Check if user is authenticated and has volunteer coordinator role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");
    if (!hasCoordinatorRole) {
      return NextResponse.json(
        { error: "Unauthorized - Coordinator role required" },
        { status: 403 },
      );
    }

    // Get request body
    const { paymentIds, note } = await req.json();

    if (!paymentIds || !Array.isArray(paymentIds) || paymentIds.length === 0) {
      return NextResponse.json(
        { error: "Invalid request - paymentIds array is required" },
        { status: 400 },
      );
    }

    // Process each payment in a transaction
    const results = await Promise.all(
      paymentIds.map(async (paymentId) => {
        try {
          // Process the payment in a transaction to ensure data consistency
          const result = await prisma.$transaction(async (tx) => {
            // Get the volunteer hours record
            const volunteerHours = await tx.volunteerHours.findUnique({
              where: { id: paymentId },
              include: {
                user: true,
                assignment: {
                  include: {
                    shift: {
                      include: {
                        event: true,
                        category: true,
                      },
                    },
                  },
                },
              },
            });

            if (!volunteerHours) {
              throw new Error(`Payment record ${paymentId} not found`);
            }

            if (volunteerHours.paymentStatus !== "pending") {
              throw new Error(
                `Payment ${paymentId} is not in pending status (current status: ${volunteerHours.paymentStatus})`,
              );
            }

            // Create a transaction record
            const transaction = await tx.transaction.create({
              data: {
                type: "volunteer_payment",
                amount: volunteerHours.paymentAmount,
                status: "completed",
                description: `Volunteer payment for ${volunteerHours.assignment.shift.event.name} - ${volunteerHours.assignment.shift.category.name}`,
                note:
                  note ||
                  `Payment for ${volunteerHours.hoursWorked} hours worked`,
                recipientId: volunteerHours.userId,
                processedById: user.id,
                processedAt: new Date(),
              },
            });

            // Update the volunteer hours record
            const updatedHours = await tx.volunteerHours.update({
              where: { id: paymentId },
              data: {
                paymentStatus: "paid",
                transactionId: transaction.id,
              },
            });

            // Update the user's balance
            await tx.user.update({
              where: { id: volunteerHours.userId },
              data: {
                balance: {
                  increment: volunteerHours.paymentAmount,
                },
              },
            });

            return {
              paymentId,
              transactionId: transaction.id,
              status: "success",
            };
          });

          return result;
        } catch (error) {
          console.error(`Error processing payment ${paymentId}:`, error);
          return {
            paymentId,
            status: "error",
            error: error instanceof Error ? error.message : "Unknown error",
          };
        }
      }),
    );

    // Count successes and failures
    const successCount = results.filter((r) => r.status === "success").length;
    const failureCount = results.length - successCount;

    return NextResponse.json({
      message: `Processed ${results.length} payments: ${successCount} successful, ${failureCount} failed`,
      results,
    });
  } catch (error) {
    console.error("Error processing volunteer payments:", error);
    return NextResponse.json(
      { error: "Failed to process volunteer payments" },
      { status: 500 },
    );
  }
}
