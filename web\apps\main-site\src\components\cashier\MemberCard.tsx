import React from "react";
import { UserSearchResult } from "../../services/bankService";

interface MemberCardProps {
  member: UserSearchResult;
  onSelect: (memberId: string) => void;
}

export const MemberCard: React.FC<MemberCardProps> = ({ member, onSelect }) => {
  return (
    <div
      className="bg-secondary rounded-lg shadow-md p-4 border border-gray-600 hover:border-primary transition-colors cursor-pointer flex flex-col"
      onClick={() => onSelect(member.id)}
    >
      <div className="flex items-center mb-3">
        <div className="flex-shrink-0 h-16 w-16">
          <img
            src={member.avatar || "/images/avatars/default.png"}
            alt={member.displayName}
            className="h-16 w-16 rounded-full object-cover"
          />
        </div>
        <div className="ml-4">
          <h3 className="text-lg font-medium text-white">
            {member.displayName}
          </h3>
          <p className="text-sm text-gray-400">@{member.username}</p>
        </div>
      </div>

      <div className="mb-3">
        <div className="flex items-center mb-1">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 text-gray-400 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
            />
          </svg>
          <span className="text-gray-400 text-sm truncate">{member.email}</span>
        </div>
      </div>

      <div className="mt-auto pt-3 border-t border-gray-600">
        <button
          onClick={(e) => {
            e.stopPropagation();
            onSelect(member.id);
          }}
          className="w-full px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
        >
          View Details
        </button>
      </div>
    </div>
  );
};
