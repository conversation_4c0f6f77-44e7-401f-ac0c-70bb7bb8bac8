"use client";

import React from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";

interface Action {
  name: string;
  path: string;
  icon: string;
  disabled?: boolean;
}

export const VolunteerQuickActions: React.FC = () => {
  const pathname = usePathname();

  // Function to check if a path is active
  const isActive = (path: string) => {
    return pathname === path;
  };

  // The main quick actions
  const mainActions: Action[] = [
    {
      name: "Category Management",
      path: "/volunteer/dashboard/categories",
      icon: "M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",
    },
    {
      name: "Shift Management",
      path: "/volunteer/dashboard/shifts",
      icon: "M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z",
    },
    {
      name: "Payment Processing",
      path: "/volunteer/dashboard/payments",
      icon: "M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2z",
    },
  ];

  const router = useRouter();

  // Action button component
  const ActionButton: React.FC<{ action: Action }> = ({ action }) => {
    // For disabled buttons, render a div instead of a link
    if (action.disabled) {
      return (
        <div
          className={`
            flex flex-col items-center justify-center p-3 rounded-md transition-colors
            bg-secondary border-2 border-gray-600 opacity-70 cursor-not-allowed
          `}
        >
          <svg
            className="w-6 h-6 text-text-secondary mb-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d={action.icon}
            />
          </svg>
          <span className="text-sm font-medium text-text-secondary">
            {action.name}
          </span>
        </div>
      );
    }

    // For active buttons
    return (
      <Link
        href={action.path}
        className={`
          flex flex-col items-center justify-center p-3 rounded-md transition-colors
          ${
            isActive(action.path)
              ? "bg-secondary border-2 border-primary"
              : "bg-secondary hover:bg-hover border-2 border-transparent"
          }
        `}
      >
        <svg
          className="w-6 h-6 text-primary mb-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d={action.icon}
          />
        </svg>
        <span className="text-sm font-medium">{action.name}</span>
      </Link>
    );
  };

  return (
    <div className="bg-secondary-light rounded-lg shadow-md p-4 border border-gray-600">
      <h2 className="text-lg font-semibold mb-4 text-white">Quick Actions</h2>

      {/* Desktop view - Show in a grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        {mainActions.map((action) => (
          <ActionButton key={action.name} action={action} />
        ))}
      </div>
    </div>
  );
};
