import { NextRequest, NextResponse } from "next/server";
import { sendEmail } from "@/lib/email";

// Configuration
const ADMIN_EMAIL = process.env.ADMIN_EMAIL || "<EMAIL>";

export async function POST(req: NextRequest) {
  try {
    // Parse the request body
    const body = await req.json();
    const { name, email, phone, message } = body;

    if (!message) {
      return NextResponse.json(
        { error: "Message is required" },
        { status: 400 },
      );
    }

    // Basic validation
    if (!email || !/^\S+@\S+\.\S+$/.test(email)) {
      return NextResponse.json(
        { error: "Valid email is required" },
        { status: 400 },
      );
    }

    // Prepare email content
    const emailText = `
      Contact Form Submission:
      
      Name: ${name || "Not provided"}
      Email: ${email}
      Phone: ${phone || "Not provided"}
      
      Message:
      ${message}
    `;

    const emailHtml = `
      <h2>Contact Form Submission</h2>
      <p><strong>Name:</strong> ${name || "Not provided"}</p>
      <p><strong>Email:</strong> ${email}</p>
      <p><strong>Phone:</strong> ${phone || "Not provided"}</p>
      <h3>Message:</h3>
      <p>${message.replace(/\n/g, "<br>")}</p>
    `;

    // Send email to admin
    const success = await sendEmail({
      to: ADMIN_EMAIL,
      subject: "New Contact Form Submission - Bank of Styx",
      text: emailText,
      html: emailHtml,
      replyTo: email,
    });

    if (success) {
      return NextResponse.json({
        success: true,
        message: "Your message has been sent successfully",
      });
    } else {
      return NextResponse.json(
        {
          error:
            "There was a problem sending your message. Please try again later.",
        },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error("Error in contact form submission:", error);
    return NextResponse.json(
      {
        error:
          "There was a problem processing your request. Please try again later.",
      },
      { status: 500 },
    );
  }
}
