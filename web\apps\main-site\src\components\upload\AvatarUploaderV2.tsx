import React, { useState, useRef } from "react";
import UniversalImageUploader from "../common/UniversalImageUploader";
import { UploadResponse } from "@/types/upload";
import { uploadConfig } from "@/lib/uploadConfig";

// Fallback components if UI library is not available
const Card = ({
  title,
  children,
  className = "",
  headerAction,
}: {
  title: string;
  children: React.ReactNode;
  className?: string;
  headerAction?: React.ReactNode;
  padding?: string;
}) => (
  <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
    <div className="flex justify-between items-center mb-4">
      <h2 className="text-xl font-semibold">{title}</h2>
      {headerAction}
    </div>
    {children}
  </div>
);

const Button = ({
  children,
  onClick,
  disabled = false,
  variant = "default",
  className = "",
}: {
  children: React.ReactNode;
  onClick: () => void;
  disabled?: boolean;
  variant?: "default" | "outline";
  className?: string;
}) => {
  const baseStyles = "px-4 py-2 rounded-md font-medium transition-colors";
  const variantStyles =
    variant === "outline"
      ? "border border-gray-300 hover:bg-gray-50 text-gray-700"
      : "bg-blue-600 text-white hover:bg-blue-700";

  return (
    <button
      type="button"
      onClick={onClick}
      disabled={disabled}
      className={`${baseStyles} ${variantStyles} ${
        disabled ? "opacity-50 cursor-not-allowed" : ""
      } ${className}`}
    >
      {children}
    </button>
  );
};

// Simple SVG icons as fallback
const ImageIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
    <circle cx="8.5" cy="8.5" r="1.5"></circle>
    <polyline points="21 15 16 10 5 21"></polyline>
  </svg>
);

const CameraIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
    <circle cx="12" cy="13" r="4"></circle>
  </svg>
);

const LoaderIcon = () => (
  <svg
    className="animate-spin -ml-1 mr-2 h-4 w-4"
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
  >
    <circle
      className="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="4"
    ></circle>
    <path
      className="opacity-75"
      fill="currentColor"
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    ></path>
  </svg>
);

interface AvatarUploaderV2Props {
  userId: string;
  onUploadComplete: (response: UploadResponse) => void;
  currentAvatarUrl?: string;
  userInitials?: string;
  className?: string;
}

const AvatarUploaderV2: React.FC<AvatarUploaderV2Props> = ({
  userId,
  onUploadComplete,
  currentAvatarUrl,
  userInitials = "US",
  className = "",
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);

  const handleUploadComplete = (response: UploadResponse) => {
    if (response.success) {
      // Handle both new format (response.file.url) and legacy format (response.url)
      const imageUrl = response.file?.url || response.url;
      if (imageUrl) {
        setPreviewUrl(imageUrl);
      }
    }
    onUploadComplete(response);
  };

  const handleUploadStart = () => {
    setIsUploading(true);
  };

  const handleUploadEnd = (response: UploadResponse) => {
    setIsUploading(false);
    handleUploadComplete(response);
  };

  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className={className}>
      <Card
        title="Profile Picture"
        headerAction={
          <div className="flex items-center space-x-2">
            <ImageIcon />
            <span>Upload New</span>
          </div>
        }
        className="max-w-md mx-auto"
      >
        <div className="flex flex-col items-center space-y-6">
          <div
            className="relative group cursor-pointer"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onClick={handleClick}
          >
            <div className="h-32 w-32 rounded-full bg-gray-100 flex items-center justify-center overflow-hidden">
              {previewUrl || currentAvatarUrl ? (
                <img
                  src={previewUrl || currentAvatarUrl}
                  alt="Profile"
                  className="h-full w-full object-cover"
                />
              ) : (
                <span className="text-2xl font-semibold text-gray-500">
                  {userInitials}
                </span>
              )}
            </div>

            {isHovered && (
              <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                <CameraIcon />
              </div>
            )}
          </div>

          <div className="w-full space-y-4">
            <UniversalImageUploader
              uploadType="avatar"
              entityId={userId}
              onUploadComplete={handleUploadEnd}
              onUploadStart={handleUploadStart}
              options={{
                maxSize: uploadConfig.avatar.maxSize,
                allowedTypes: uploadConfig.avatar.allowedTypes,
                width: 256,
                height: 256,
                quality: 0.9,
              }}
              inputRef={fileInputRef}
              className="w-full"
            />

            <p className="text-xs text-gray-500 text-center">
              Recommended size: 256x256px • Max size: 2MB • JPG, PNG
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default AvatarUploaderV2;
