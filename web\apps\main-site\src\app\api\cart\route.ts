import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";
import { TicketStatus } from "@prisma/client";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/cart - Get user's cart
export async function GET(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Find or create cart for user
    let cart = await prisma.cart.findFirst({
      where: { userId: user.id },
      include: {
        items: {
          include: {
            product: {
              include: {
                category: true,
                event: {
                  select: {
                    id: true,
                    name: true,
                    startDate: true,
                    endDate: true,
                  },
                },
              },
            },
            ticketHold: {
              include: {
                tickets: {
                  where: {
                    status: TicketStatus.HELD,
                  },
                },
              },
            },
            eventCapacityHold: true,
            redemptionCode: {
              select: {
                id: true,
                code: true,
              },
            },
          },
        },
      },
    });

    if (!cart) {
      cart = await prisma.cart.create({
        data: {
          userId: user.id,
        },
        include: {
          items: {
            include: {
              product: {
                include: {
                  category: true,
                  event: {
                    select: {
                      id: true,
                      name: true,
                      startDate: true,
                      endDate: true,
                    },
                  },
                },
              },
              ticketHold: {
                include: {
                  tickets: {
                    where: {
                      status: TicketStatus.HELD,
                    },
                  },
                },
              },
              eventCapacityHold: true,
              redemptionCode: {
                select: {
                  id: true,
                  code: true,
                },
              },
            },
          },
        },
      });
    }

    // Calculate totals (free items don't contribute to subtotal)
    const subtotal = cart.items.reduce(
      (sum, item) => {
        // Code-redeemed items are free
        const itemPrice = item.isCodeRedemption ? 0 : item.product.price;
        return sum + itemPrice * item.quantity;
      },
      0,
    );

    return NextResponse.json({
      cart: {
        ...cart,
        subtotal,
      },
    });
  } catch (error) {
    console.error("Error fetching cart:", error);
    return NextResponse.json(
      { error: "Failed to fetch cart" },
      { status: 500 },
    );
  }
}
