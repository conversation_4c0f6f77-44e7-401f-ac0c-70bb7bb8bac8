import React, { useState, useRef, useEffect, useMemo } from "react";

export interface CategoryItem {
  name: string;
  isFilter?: boolean;
  isDisabled?: boolean;
  isSeparator?: boolean;
}

export interface SearchBarProps {
  placeholder?: string;
  onSearch: (query: string) => void;
  onClear?: () => void;
  initialValue?: string;
  className?: string;
  buttonText?: string;
  showButton?: boolean;
  categories?: string[] | CategoryItem[];
  selectedCategory?: string;
  onCategoryChange?: (category: string, isFilter?: boolean) => void;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = "Search...",
  onSearch,
  onClear,
  initialValue = "",
  className = "",
  buttonText = "Search",
  showButton = true,
  categories,
  selectedCategory: externalSelectedCategory,
  onCategoryChange,
}) => {
  const [searchQuery, setSearchQuery] = useState(initialValue);
  const [internalSelectedCategory, setInternalSelectedCategory] =
    useState<string>(
      categories && categories.length > 0
        ? typeof categories[0] === "string"
          ? categories[0]
          : (categories[0] as CategoryItem).name
        : "",
    );
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Determine which selected category to use (external prop or internal state)
  const selectedCategory =
    externalSelectedCategory !== undefined
      ? externalSelectedCategory
      : internalSelectedCategory;

  // Update searchQuery when initialValue changes
  useEffect(() => {
    setSearchQuery(initialValue);
  }, [initialValue]);

  // Process categories to handle both string[] and CategoryItem[]
  const processedCategories = useMemo(() => {
    if (!categories || categories.length === 0) return [];

    return categories.map((category) => {
      if (typeof category === "string") {
        return { name: category, isDisabled: category === "Categories" };
      }
      return category;
    });
  }, [categories]);

  // Calculate the width based on the longest category name
  const dropdownWidth = useMemo(() => {
    if (!processedCategories || processedCategories.length === 0) return "auto";

    // Find the longest category name
    let maxLength = 0;
    processedCategories.forEach((category) => {
      if (category.name && category.name.length > maxLength) {
        maxLength = category.name.length;
      }
    });

    // Add some padding to ensure there's enough space
    return `${maxLength * 10 + 60}px`;
  }, [processedCategories]);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      onSearch(searchQuery);
    }
  };

  const handleClear = () => {
    setSearchQuery("");
    if (onClear) {
      onClear();
    }
  };

  const handleCategoryChange = (category: CategoryItem) => {
    if (category.isDisabled) return; // Prevent selecting disabled categories

    // Only update internal state if we're not using external state
    if (externalSelectedCategory === undefined) {
      setInternalSelectedCategory(category.name);
    }

    setIsDropdownOpen(false);

    if (onCategoryChange) {
      onCategoryChange(category.name, category.isFilter);
    }
  };

  return (
    <form onSubmit={handleSubmit} className={`${className}`}>
      <div className="flex flex-col sm:flex-row items-start">
        {/* Search input - order-first on mobile, order-2 on sm and up */}
        <div
          className="relative flex-grow order-first sm:order-2 w-full sm:w-auto"
          style={{
            minWidth: processedCategories.length > 0 ? dropdownWidth : "auto",
          }}
        >
          <div className="flex w-full">
            <div className="relative flex-grow">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={placeholder}
                className={`w-full px-4 py-2 text-white bg-secondary border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                  showButton ? "rounded-r-none" : ""
                }`}
              />
              {searchQuery.trim() && (
                <button
                  type="button"
                  onClick={handleClear}
                  className="absolute inset-y-0 right-0 flex items-center px-2"
                  aria-label="Clear search"
                >
                  <svg
                    className="w-4 h-4 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              )}
            </div>

            {showButton && (
              <button
                type="submit"
                className="px-4 py-2 text-white bg-primary rounded-r-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 flex-shrink-0"
              >
                {buttonText}
              </button>
            )}

            {!showButton && (
              <button
                type="submit"
                className="absolute inset-y-0 right-0 flex items-center px-3"
              >
                <svg
                  className="w-5 h-5 text-gray-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </button>
            )}
          </div>
        </div>

        {/* Categories dropdown - order-2 on mobile, order-first on sm and up */}
        {processedCategories.length > 0 && (
          <div
            className="relative mt-2 sm:mt-0 sm:mr-2 order-2 sm:order-first w-full sm:w-auto"
            ref={dropdownRef}
            style={{ minWidth: dropdownWidth }}
          >
            <button
              type="button"
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              className="flex items-center justify-between w-full px-4 py-2 text-white bg-secondary border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              style={{ minWidth: dropdownWidth }}
            >
              <span>{selectedCategory}</span>
              <svg
                className="w-4 h-4 ml-2 text-gray-500 flex-shrink-0"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>

            {isDropdownOpen && (
              <div
                className="absolute z-10 mt-1 bg-secondary border border-gray-600 rounded-md shadow-lg"
                style={{ minWidth: dropdownWidth, width: "100%" }}
              >
                <ul className="py-1 overflow-auto max-h-60">
                  {processedCategories.map((category, index) =>
                    category.isSeparator ? (
                      <li
                        key={`separator-${index}`}
                        className="border-t border-gray-600 my-1"
                      ></li>
                    ) : (
                      <li
                        key={`${category.name}-${index}`}
                        onClick={() =>
                          !category.isDisabled && handleCategoryChange(category)
                        }
                        className={`px-4 py-2 cursor-pointer whitespace-nowrap ${
                          category.name === selectedCategory
                            ? "bg-primary-dark text-white"
                            : category.isDisabled
                            ? "text-gray-500 bg-gray-700 cursor-not-allowed"
                            : category.isFilter
                            ? "text-white hover:bg-secondary-light border-l-4 border-primary pl-3"
                            : "text-white hover:bg-secondary-light"
                        }`}
                      >
                        {category.name}
                      </li>
                    ),
                  )}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>
    </form>
  );
};
