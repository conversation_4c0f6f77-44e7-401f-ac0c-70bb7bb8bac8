import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../../lib/prisma";
import jwt from "jsonwebtoken";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
const JWT_SECRET = process.env.JWT_SECRET || "your-development-secret-key";

// Helper function to verify token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

export const GET = async (req: NextRequest) => {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Check if user is a banker
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || !user.isBanker) {
      return NextResponse.json(
        { error: "Unauthorized - Banker role required" },
        { status: 403 },
      );
    }

    // Get date range from query parameters
    const url = new URL(req.url);
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");

    // Build where conditions
    const whereConditions: any = {};

    if (startDate && endDate) {
      whereConditions.date = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    } else if (startDate) {
      whereConditions.date = {
        gte: new Date(startDate),
      };
    } else if (endDate) {
      whereConditions.date = {
        lte: new Date(endDate),
      };
    }

    // Query the database for ledger entries
    const ledgerEntries = await prisma.ledger.findMany({
      where: whereConditions,
      include: {
        verifiedBy: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
      orderBy: {
        date: "desc",
      },
    });

    // Format dates as ISO strings for JSON serialization
    const formattedLedgerEntries = ledgerEntries.map((entry) => ({
      ...entry,
      date: entry.date.toISOString(),
      verifiedAt: entry.verifiedAt ? entry.verifiedAt.toISOString() : null,
    }));

    return NextResponse.json(formattedLedgerEntries);
  } catch (error) {
    console.error("Error fetching ledger entries:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};

export const POST = async (req: NextRequest) => {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Check if user is a banker
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || !user.isBanker) {
      return NextResponse.json(
        { error: "Unauthorized - Banker role required" },
        { status: 403 },
      );
    }

    // Get request body
    const {
      description,
      totalDeposits,
      totalWithdrawals,
      totalTransfers,
      netChange,
    } = await req.json();

    // Validate input
    if (!description) {
      return NextResponse.json(
        { error: "Description is required" },
        { status: 400 },
      );
    }

    // Create the ledger entry
    const ledgerEntry = await prisma.ledger.create({
      data: {
        description,
        totalDeposits: totalDeposits || 0,
        totalWithdrawals: totalWithdrawals || 0,
        totalTransfers: totalTransfers || 0,
        netChange: netChange || 0,
        status: "pending",
      },
    });

    // Format dates for response
    const formattedLedgerEntry = {
      ...ledgerEntry,
      date: ledgerEntry.date.toISOString(),
      verifiedAt: ledgerEntry.verifiedAt
        ? ledgerEntry.verifiedAt.toISOString()
        : null,
    };

    return NextResponse.json(formattedLedgerEntry);
  } catch (error) {
    console.error("Error creating ledger entry:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
