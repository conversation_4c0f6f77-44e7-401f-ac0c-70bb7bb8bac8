import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

interface Params {
  params: {
    id: string;
  };
}

// GET /api/products/[id] - Get a specific product (public)
export async function GET(req: NextRequest, { params }: Params) {
  try {
    const { id } = params;

    // Get the product
    const product = await prisma.product.findUnique({
      where: { id },
      include: {
        category: true,
        event: {
          select: {
            id: true,
            name: true,
            startDate: true,
            endDate: true,
            capacity: true,
          },
        },
      },
    });

    if (!product) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 });
    }

    return NextResponse.json({ product });
  } catch (error) {
    console.error("Error fetching product:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
