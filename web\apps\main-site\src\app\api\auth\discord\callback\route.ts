import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";
import prisma from "../../../../../lib/prisma";
import {
  exchangeCodeForToken,
  getDiscordUser,
} from "../../../../../lib/discord";
import { downloadAndSaveDiscordAvatar } from "../../../../../lib/discordAvatarUtils";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
const JWT_SECRET = process.env.JWT_SECRET || "your-development-secret-key";

export const OPTIONS = async (req: NextRequest) => {
  // Handle OPTIONS request for CORS preflight
  const response = new NextResponse(null, { status: 204 });

  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set("Access-Control-Allow-Methods", "GET, OPTIONS");
  response.headers.set(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization",
  );

  return response;
};

export const GET = async (req: NextRequest) => {
  try {
    // Get code and state from query parameters
    const url = new URL(req.url);
    const code = url.searchParams.get("code");
    const stateParam = url.searchParams.get("state");

    // Default values if state is not provided
    let isConnecting = false;
    let returnUrl = "";

    // Parse state if available
    if (stateParam) {
      try {
        const stateData = JSON.parse(
          Buffer.from(stateParam, "base64").toString(),
        );
        isConnecting = stateData.connect === true;
        returnUrl = stateData.returnUrl || "";
      } catch (e) {
        console.error("Error parsing state parameter:", e);
      }
    }

    if (!code) {
      // Use absolute URL for redirect
      const baseUrl = new URL(req.url).origin;
      return NextResponse.redirect(
        `${baseUrl}/auth/discord/error?error=missing_code`,
      );
    }

    // Initialize variables
    let user = null;
    let currentUserId = null;
    if (isConnecting) {
      const authHeader = req.headers.get("authorization");
      if (authHeader && authHeader.startsWith("Bearer ")) {
        const token = authHeader.split(" ")[1];
        try {
          const decoded = jwt.verify(token, JWT_SECRET) as { id: string };
          if (decoded && decoded.id) {
            currentUserId = decoded.id;
          }
        } catch (e) {
          console.error("Error verifying token:", e);
        }
      }

      // If no current user ID from header, try to get it from cookies
      if (!currentUserId) {
        const cookies = req.cookies;
        const authToken = cookies.get("auth_token")?.value;
        if (authToken) {
          try {
            const decoded = jwt.verify(authToken, JWT_SECRET) as { id: string };
            if (decoded && decoded.id) {
              currentUserId = decoded.id;
            }
          } catch (e) {
            console.error("Error verifying token from cookie:", e);
          }
        }
      }
    }

    // Exchange code for token
    const tokenData = await exchangeCodeForToken(code);

    // Get Discord user data
    const discordUser = await getDiscordUser(tokenData.access_token);

    if (!discordUser.email) {
      const baseUrl = new URL(req.url).origin;
      return NextResponse.redirect(
        `${baseUrl}/auth/discord/error?error=email_required`,
      );
    }

    // If connecting an existing account
    if (isConnecting && currentUserId) {
      // Get the current user
      const currentUser = await prisma.user.findUnique({
        where: { id: currentUserId },
      });

      if (!currentUser) {
        const baseUrl = new URL(req.url).origin;
        return NextResponse.redirect(
          `${baseUrl}/auth/discord/error?error=user_not_found`,
        );
      }

      // Check if this Discord account is already connected to another user
      const existingDiscordCredential = await prisma.userCredential.findFirst({
        where: {
          type: "discord",
          identifier: discordUser.id,
        },
        include: {
          user: true,
        },
      });

      if (
        existingDiscordCredential &&
        existingDiscordCredential.user.id !== currentUserId
      ) {
        const baseUrl = new URL(req.url).origin;
        return NextResponse.redirect(
          `${baseUrl}/auth/discord/error?error=discord_already_connected`,
        );
      }

      // Update or create Discord credential for the current user
      if (existingDiscordCredential) {
        await prisma.userCredential.update({
          where: { id: existingDiscordCredential.id },
          data: {
            accessToken: tokenData.access_token,
            refreshToken: tokenData.refresh_token,
            tokenExpiry: new Date(Date.now() + tokenData.expires_in * 1000),
            lastUsedAt: new Date(),
          },
        });
      } else {
        await prisma.userCredential.create({
          data: {
            userId: currentUserId,
            type: "discord",
            identifier: discordUser.id,
            accessToken: tokenData.access_token,
            refreshToken: tokenData.refresh_token,
            tokenExpiry: new Date(Date.now() + tokenData.expires_in * 1000),
            lastUsedAt: new Date(),
          },
        });
      }

      // Update Discord info in the user record (for backward compatibility)
      let updatedUser = await prisma.user.update({
        where: { id: currentUserId },
        data: {
          discordId: discordUser.id,
          discordConnected: true,
        },
      });

      // Update avatar if available
      if (discordUser.avatar) {
        try {
          // Download and save the avatar
          const avatarUrl = await downloadAndSaveDiscordAvatar(
            discordUser.id,
            discordUser.avatar,
            currentUserId,
          );

          // Update the user with the local avatar URL
          updatedUser = await prisma.user.update({
            where: { id: currentUserId },
            data: { avatar: avatarUrl },
          });
        } catch (error) {
          console.error(
            "Error updating Discord avatar when connecting account:",
            error,
          );
          // Continue with existing avatar if there's an error
        }
      }

      // If the user has an email from Discord that doesn't match their current email,
      // create an additional email credential
      if (discordUser.email && discordUser.email !== currentUser.email) {
        const existingEmailCredential = await prisma.userCredential.findFirst({
          where: {
            type: "email",
            identifier: discordUser.email,
          },
        });

        if (!existingEmailCredential) {
          await prisma.userCredential.create({
            data: {
              userId: currentUserId,
              type: "email",
              identifier: discordUser.email,
              lastUsedAt: new Date(),
            },
          });
        }
      }

      // Create JWT token
      const token = jwt.sign(
        { id: updatedUser.id, email: updatedUser.email },
        JWT_SECRET,
        { expiresIn: "7d" },
      );

      // Redirect back to the return URL or settings page with token
      const baseUrl = new URL(req.url).origin;
      const redirectUrl = returnUrl
        ? `${baseUrl}${returnUrl}?connected=true&token=${token}`
        : `${baseUrl}/settings?connected=true&token=${token}`;

      return NextResponse.redirect(redirectUrl);
    }
    // Normal login/registration flow
    else {
      // First, check if we have a credential with this Discord ID
      let credential = await prisma.userCredential.findFirst({
        where: {
          type: "discord",
          identifier: discordUser.id,
        },
        include: {
          user: true,
        },
      });

      // If credential exists, use the associated user
      if (credential && credential.user) {
        // Update the credential with new token data
        await prisma.userCredential.update({
          where: { id: credential.id },
          data: {
            accessToken: tokenData.access_token,
            refreshToken: tokenData.refresh_token,
            tokenExpiry: new Date(Date.now() + tokenData.expires_in * 1000),
            lastUsedAt: new Date(),
          },
        });

        // Use the existing user
        user = credential.user;

        // Update avatar if available
        if (discordUser.avatar) {
          try {
            // Download and save the avatar
            const avatarUrl = await downloadAndSaveDiscordAvatar(
              discordUser.id,
              discordUser.avatar,
              user.id,
            );

            // Update the user with the local avatar URL
            user = await prisma.user.update({
              where: { id: user.id },
              data: { avatar: avatarUrl },
            });
          } catch (error) {
            console.error(
              "Error updating Discord avatar for existing user:",
              error,
            );
            // Continue with existing avatar if there's an error
          }
        }
      }
      // If no credential with Discord ID, check if we have a user with this Discord ID (legacy)
      else {
        user = await prisma.user.findFirst({
          where: { discordId: discordUser.id },
        });

        // If user exists with Discord ID, create a credential for them
        if (user) {
          await prisma.userCredential.create({
            data: {
              userId: user.id,
              type: "discord",
              identifier: discordUser.id,
              accessToken: tokenData.access_token,
              refreshToken: tokenData.refresh_token,
              tokenExpiry: new Date(Date.now() + tokenData.expires_in * 1000),
              lastUsedAt: new Date(),
            },
          });

          // Update avatar if available
          if (discordUser.avatar) {
            try {
              // Download and save the avatar
              const avatarUrl = await downloadAndSaveDiscordAvatar(
                discordUser.id,
                discordUser.avatar,
                user.id,
              );

              // Update the user with the local avatar URL
              user = await prisma.user.update({
                where: { id: user.id },
                data: { avatar: avatarUrl },
              });
            } catch (error) {
              console.error(
                "Error updating Discord avatar for user found by Discord ID:",
                error,
              );
              // Continue with existing avatar if there's an error
            }
          }
        }
      }

      // If no user with Discord ID, check by email
      if (!user && discordUser.email) {
        // Check if we have a credential with this email
        credential = await prisma.userCredential.findFirst({
          where: {
            type: "email",
            identifier: discordUser.email,
          },
          include: {
            user: true,
          },
        });

        if (credential && credential.user) {
          user = credential.user;
        } else {
          // Legacy check by email in the User table
          user = await prisma.user.findUnique({
            where: { email: discordUser.email },
          });
        }

        // If user exists by email, add a Discord credential for them
        if (user) {
          // Update Discord info in the user record (for backward compatibility)
          user = await prisma.user.update({
            where: { id: user.id },
            data: {
              discordId: discordUser.id,
              discordConnected: true,
            },
          });

          // Create a Discord credential for this user
          await prisma.userCredential.create({
            data: {
              userId: user.id,
              type: "discord",
              identifier: discordUser.id,
              accessToken: tokenData.access_token,
              refreshToken: tokenData.refresh_token,
              tokenExpiry: new Date(Date.now() + tokenData.expires_in * 1000),
              lastUsedAt: new Date(),
            },
          });

          // Update avatar if available
          if (discordUser.avatar) {
            try {
              // Download and save the avatar
              const avatarUrl = await downloadAndSaveDiscordAvatar(
                discordUser.id,
                discordUser.avatar,
                user.id,
              );

              // Update the user with the local avatar URL
              user = await prisma.user.update({
                where: { id: user.id },
                data: { avatar: avatarUrl },
              });
            } catch (error) {
              console.error(
                "Error updating Discord avatar for user found by email:",
                error,
              );
              // Continue with existing avatar if there's an error
            }
          }
        }
      }

      // If no user exists, create a new one
      if (!user) {
        // Generate a unique username based on Discord username
        let username = discordUser.username.toLowerCase().replace(/\s+/g, "_");

        // Check if username exists
        const existingUser = await prisma.user.findUnique({
          where: { username },
        });

        if (existingUser) {
          // Add random numbers to make username unique
          username = `${username}_${Math.floor(Math.random() * 10000)}`;
        }

        // Create new user with default avatar first
        user = await prisma.user.create({
          data: {
            username,
            displayName: discordUser.username,
            email:
              discordUser.email || `${discordUser.id}@discord.placeholder.com`,
            passwordHash: "", // No password for OAuth users
            discordId: discordUser.id,
            discordConnected: true,
            // Set default avatar initially
            avatar: "/images/avatars/default.png",
            // Create Discord credential
            credentials: {
              create: {
                type: "discord",
                identifier: discordUser.id,
                accessToken: tokenData.access_token,
                refreshToken: tokenData.refresh_token,
                tokenExpiry: new Date(Date.now() + tokenData.expires_in * 1000),
                lastUsedAt: new Date(),
              },
            },
          },
        });

        // If the user has an email, also create an email credential
        if (discordUser.email) {
          await prisma.userCredential.create({
            data: {
              userId: user.id,
              type: "email",
              identifier: discordUser.email,
              lastUsedAt: new Date(),
            },
          });
        }

        // Download and save Discord avatar if available
        if (discordUser.avatar) {
          try {
            // Download and save the avatar
            const avatarUrl = await downloadAndSaveDiscordAvatar(
              discordUser.id,
              discordUser.avatar,
              user.id,
            );

            // Update the user with the local avatar URL
            user = await prisma.user.update({
              where: { id: user.id },
              data: { avatar: avatarUrl },
            });
          } catch (error) {
            console.error("Error saving Discord avatar for new user:", error);
            // Continue with default avatar if there's an error
          }
        }
      }
    }

    // Only create token and redirect for normal login flow (not for connecting)
    if (!isConnecting || !currentUserId) {
      // Create JWT token
      const token = jwt.sign({ id: user.id, email: user.email }, JWT_SECRET, {
        expiresIn: "7d",
      });

      // Redirect to frontend callback page with token
      const baseUrl = new URL(req.url).origin;
      return NextResponse.redirect(
        `${baseUrl}/auth/discord/callback?token=${token}`,
      );
    }
  } catch (error) {
    console.error("Discord callback error:", error);
    const baseUrl = new URL(req.url).origin;
    return NextResponse.redirect(
      `${baseUrl}/auth/discord/error?error=server_error`,
    );
  }
};
