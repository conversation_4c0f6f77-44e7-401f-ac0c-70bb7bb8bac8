"use client";

import React from "react";
import { ShiftCard } from "./ShiftCard";
import { VolunteerShift } from "@/hooks/useVolunteerShifts";

interface ShiftListProps {
  shifts: VolunteerShift[];
  isLoading: boolean;
  error: Error | null;
  onEdit: (shift: VolunteerShift) => void;
  onDelete: (shift: VolunteerShift) => void;
}

export const ShiftList: React.FC<ShiftListProps> = ({
  shifts,
  isLoading,
  error,
  onEdit,
  onDelete,
}) => {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-40">
        <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-accent bg-opacity-20 border border-accent rounded-md p-4 text-accent">
        <p>Error loading shifts: {error.message}</p>
      </div>
    );
  }

  if (shifts.length === 0) {
    return (
      <div className="bg-secondary-light rounded-lg shadow-md p-6 border border-gray-600 text-center">
        <p className="text-gray-400 mb-4">No shifts found for this category.</p>
        <p className="text-white">Create a new shift to get started.</p>
      </div>
    );
  }

  // Group shifts by date
  const shiftsByDate = shifts.reduce(
    (acc, shift) => {
      const date = new Date(shift.startTime).toLocaleDateString("en-US", {
        weekday: "long",
        month: "long",
        day: "numeric",
        year: "numeric",
      });

      if (!acc[date]) {
        acc[date] = [];
      }

      acc[date].push(shift);
      return acc;
    },
    {} as Record<string, VolunteerShift[]>,
  );

  // Sort dates chronologically
  const sortedDates = Object.keys(shiftsByDate).sort((a, b) => {
    const dateA = new Date(shiftsByDate[a][0].startTime);
    const dateB = new Date(shiftsByDate[b][0].startTime);
    return dateA.getTime() - dateB.getTime();
  });

  return (
    <div className="space-y-6">
      {sortedDates.map((date) => (
        <div key={date}>
          <h3 className="text-xl font-semibold text-white mb-3">{date}</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {shiftsByDate[date].map((shift) => (
              <ShiftCard
                key={shift.id}
                shift={shift}
                onEdit={onEdit}
                onDelete={onDelete}
              />
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};
