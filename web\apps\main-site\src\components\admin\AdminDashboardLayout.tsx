"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface AdminDashboardLayoutProps {
  children: React.ReactNode;
}

export const AdminDashboardLayout: React.FC<AdminDashboardLayoutProps> = ({
  children,
}) => {
  const pathname = usePathname();

  const navItems = [
    { name: "User Management", path: "/admin/dashboard/users" },
    { name: "Featured Content", path: "/admin/dashboard/featured" },
    { name: "Support Tickets", path: "/admin/dashboard/tickets" },
    { name: "Events", path: "/admin/events" },
    { name: "Event Categories", path: "/admin/event-categories" },
  ];

  // Function to check if a path is active
  const isActive = (path: string) => {
    if (path === "/admin/dashboard") {
      return pathname === "/admin/dashboard";
    }
    return pathname.startsWith(path);
  };

  return (
    <div className="min-h-screen bg-secondary-dark">
      <div className="container mx-auto px-2 py-2">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className="w-12 h-12 rounded-full mr-4 bg-primary flex items-center justify-center text-white font-bold text-xl">
              A
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">Admin Dashboard</h1>
              <p className="text-gray-400">
                Manage users, content, and site features
              </p>
            </div>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-0">
          {/* Sidebar Navigation - Hidden on mobile */}
          <aside className="hidden md:block md:w-40 flex-shrink-0">
            <div className="bg-secondary rounded-lg shadow-md p-4">
              <nav>
                <ul className="space-y-5">
                  {navItems.map((item) => (
                    <li key={item.path}>
                      <Link
                        href={item.path}
                        className={`
                          block px-4 py-2 rounded-md transition-colors
                          ${
                            isActive(item.path)
                              ? "border-2 border-primary font-medium"
                              : "border-2 border-gray-600 hover:bg-secondary-light"
                          }
                        `}
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </nav>
            </div>
          </aside>

          {/* Mobile Navigation - Only visible on mobile */}
          <div className="md:hidden bg-secondary rounded-lg shadow-md p-2 mb-6">
            <nav>
              <ul className="grid grid-cols-2 gap-2">
                {navItems.map((item) => (
                  <li key={item.path}>
                    <Link
                      href={item.path}
                      className={`
                        block px-3 py-2 rounded-md text-sm text-center transition-colors
                        ${
                          isActive(item.path)
                            ? "bg-primary text-white font-medium"
                            : "bg-secondary-light hover:bg-secondary-dark text-white"
                        }
                      `}
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </nav>
          </div>

          {/* Main Content */}
          <main className="flex-1 bg-secondary-light rounded-lg shadow-md p-2 border border-gray-600">
            {children}
          </main>
        </div>
      </div>
    </div>
  );
};
