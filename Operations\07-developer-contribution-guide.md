# 07 - Developer Contribution Guide

This document outlines the standards and best practices for contributing to this project. Adhering to these guidelines ensures code quality, consistency, and a smooth, collaborative development workflow.

---

## 1. Version Control (Git) Workflow

A clean and understandable Git history is crucial. We follow a feature-branching model with specific conventions for branches and commits.

### Branching Strategy

- **`main` is the primary branch.** It represents the latest stable, production-ready code. It is protected, and direct pushes are disabled.
- **Create branches from `main`** for all new work (features, fixes, etc.).
- **Branch Naming Convention:** Branches should be named using the format `[type]/[short-description]`.
  - **Examples:**
    - `feat/add-user-profile-page`
    - `fix/login-form-validation-bug`
    - `docs/update-contribution-guide`
    - `chore/update-npm-dependencies`
  - **Common types:** `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`.

### Commit Message Convention

We use the **Conventional Commits** specification. This format makes the commit history readable and allows for automatic changelog generation.

- **Format:** `<type>(<scope>): <subject>`
  - `<type>`: Must be one of the common types listed above.
  - `<scope>` (optional): The part of the codebase affected (e.g., `auth`, `events`, `api`).
  - `<subject>`: A short, imperative-mood description of the change.
- **Examples:**
  - `feat(auth): implement password reset endpoint`
  - `fix(events): correct date formatting on event cards`
  - `docs(readme): update setup instructions`

---

## 2. Code Quality & Style

A consistent code style is maintained using automated tools.

- **Formatting (Prettier):** This project uses Prettier for automatic code formatting. Before creating a Pull Request, run the format command to ensure your code conforms to the project's style.
  - `pnpm format`
- **Linting (ESLint):** This project uses ESLint to catch common errors and style issues. Your code must be free of any linting errors before it can be merged.
  - `pnpm lint`

---

## 3. The Pull Request (PR) Process

PRs are the only way to merge code into the `main` branch.

- **Create a PR** as soon as you have meaningful work to share. It's okay to create "Draft" PRs for work in progress to get early feedback.
- **Use the PR Template:** Fill out the description template completely. This provides crucial context for reviewers.
- **Review Process:**
  - At least one other developer must review and approve the PR.
  - The original author **should not** merge their own PR.
  - Address all reviewer comments and ensure all automated checks (linting, tests, build) are passing before requesting a final review.

### Pull Request Template

```markdown
## Description
A clear and concise description of what this PR does and why it's needed.

## Related Issue
Link to any relevant issue tickets (e.g., "Closes #123").

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] This change requires a documentation update

## How Has This Been Tested?
Please describe the tests that you ran to verify your changes.
- [ ] Unit Tests
- [ ] Integration Tests
- [ ] End-to-End Tests (if applicable)
- [ ] Manual Testing (describe steps)

## Checklist:
- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
```

---

## 4. Testing Expectations

All contributions should be accompanied by appropriate tests.

- **Unit Tests:** For individual functions, components, or classes in isolation.
- **Integration Tests:** For testing the interaction between multiple units.
- **End-to-End (E2E) Tests:** For critical user journeys. Refer to `06-end-to-end-testing-guide.md` for the strategy on creating these.

New features without tests will not be accepted. Bug fixes should include a test that proves the fix is effective.
