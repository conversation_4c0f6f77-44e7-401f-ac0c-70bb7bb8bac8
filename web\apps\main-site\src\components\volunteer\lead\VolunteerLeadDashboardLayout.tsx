"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface VolunteerLeadDashboardLayoutProps {
  children: React.ReactNode;
}

export const VolunteerLeadDashboardLayout: React.FC<
  VolunteerLeadDashboardLayoutProps
> = ({ children }) => {
  const pathname = usePathname();

  const navItems = [{ name: "Dashboard", path: "/volunteer/lead/dashboard" }];

  // Function to check if a path is active
  const isActive = (path: string) => {
    if (path === "/volunteer/lead/dashboard") {
      return pathname === "/volunteer/lead/dashboard";
    }
    return pathname.startsWith(path);
  };

  return (
    <div className="min-h-screen bg-secondary-dark">
      <div className="container px-2 py-2 md:py-4 lg:py-8 mx-auto">
        <div className="flex items-center justify-between mb-4 md:mb-4">
          <div className="flex items-center">
            <div className="w-10 h-10 md:w-12 md:h-12 rounded-full mr-3 md:mr-4 bg-primary flex items-center justify-center text-white font-bold text-lg md:text-xl">
              VL
            </div>
            <div>
              <h1 className="text-xl md:text-2xl lg:text-3xl font-bold text-white">
                Category Lead Dashboard
              </h1>
              <p className="text-sm md:text-base text-gray-400">
                Manage your assigned category, shifts, and volunteers
              </p>
            </div>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-4">
          {/* Sidebar Navigation - Hidden on mobile */}
          <aside className="hidden md:block md:w-40 lg:w-40 flex-shrink-0">
            <div className="bg-secondary rounded-lg shadow-md p-4 border border-gray-600">
              <nav>
                <ul className="space-y-4">
                  {navItems.map((item) => (
                    <li key={item.path}>
                      <Link
                        href={item.path}
                        className={`
                          block px-2 py-2 rounded-md font-semibold border border-gray-600 transition-colors text-base
                          ${
                            isActive(item.path)
                              ? "bg-primary text-white font-medium"
                              : "text-white hover:bg-secondary-light"
                          }
                        `}
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </nav>
            </div>
          </aside>

          {/* Mobile Navigation - Only visible on mobile */}
          <div className="md:hidden bg-secondary rounded-lg shadow-md p-2 mb-4">
            <nav>
              <ul className="grid grid-cols-2 gap-2">
                {navItems.map((item) => (
                  <li key={item.path}>
                    <Link
                      href={item.path}
                      className={`
                        block px-3 py-2 rounded-md text-sm text-center transition-colors
                        ${
                          isActive(item.path)
                            ? "bg-primary text-white font-medium"
                            : "bg-secondary-light hover:bg-secondary-dark text-white"
                        }
                      `}
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </nav>
          </div>

          {/* Main Content */}
          <main className="flex-1 bg-secondary-light rounded-lg shadow-md p-4 border border-gray-600">
            {children}
          </main>
        </div>
      </div>
    </div>
  );
};
