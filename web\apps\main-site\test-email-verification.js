// Test script to verify email verification functionality
const prisma = require('./src/lib/prisma');

async function main() {
  try {
    // Get a test user
    const testUser = await prisma.user.findFirst({
      where: {
        connectedAccounts: {
          discord: true
        },
        isEmailVerified: false
      }
    });

    if (!testUser) {
      console.log('No test user found. Please create a user with Discord connected and email not verified.');
      return;
    }

    console.log(`Test user: ${testUser.username} (${testUser.email})`);
    console.log(`Current email verification status: ${testUser.isEmailVerified ? 'Verified' : 'Not Verified'}`);

    // Simulate verification code creation
    const verificationCode = await prisma.verificationCode.upsert({
      where: {
        userId_type: {
          userId: testUser.id,
          type: 'EMAIL_VERIFICATION'
        }
      },
      update: {
        code: 'TEST123',
        expiresAt: new Date(Date.now() + 3600000), // 1 hour from now
        attempts: 0,
        verified: false
      },
      create: {
        userId: testUser.id,
        type: 'EMAIL_VERIFICATION',
        code: 'TEST123',
        expiresAt: new Date(Date.now() + 3600000), // 1 hour from now
        attempts: 0,
        verified: false
      }
    });

    console.log(`Created verification code: ${verificationCode.code}`);

    // Simulate verification
    const updatedVerificationCode = await prisma.verificationCode.update({
      where: {
        id: verificationCode.id
      },
      data: {
        verified: true
      }
    });

    console.log(`Verification code marked as verified: ${updatedVerificationCode.verified}`);

    // Update user's email verification status
    const updatedUser = await prisma.user.update({
      where: {
        id: testUser.id
      },
      data: {
        isEmailVerified: true
      }
    });

    console.log(`Updated user email verification status: ${updatedUser.isEmailVerified ? 'Verified' : 'Not Verified'}`);

    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Error during test:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
