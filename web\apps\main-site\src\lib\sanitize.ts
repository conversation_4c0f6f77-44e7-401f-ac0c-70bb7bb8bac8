/**
 * HTML sanitization utility for the Bank of Styx website
 * Used primarily for sanitizing content from the React Quill editor
 */

import DOMPurify from "dompurify";

// Enhanced allowed tags to support more rich text features
const ALLOWED_TAGS = [
  "p",
  "br",
  "b",
  "i",
  "em",
  "strong",
  "a",
  "h1",
  "h2",
  "h3",
  "h4",
  "h5",
  "h6",
  "blockquote",
  "code",
  "pre",
  "hr",
  "ul",
  "ol",
  "li",
  "span",
  "div",
  "table",
  "thead",
  "tbody",
  "tr",
  "th",
  "td",
  "img",
  "figure",
  "figcaption",
  "sub",
  "sup",
];

// Enhanced allowed attributes to support more styling options
const ALLOWED_ATTR = [
  "href",
  "target",
  "rel",
  "src",
  "alt",
  "class",
  "style",
  "width",
  "height",
  "align",
  "data-*",
];

/**
 * Sanitizes HTML content to prevent XSS attacks
 */
export function sanitizeHtml(html: string): string {
  if (!html) return "";

  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS,
    ALLOWED_ATTR,
    FORBID_TAGS: ["script", "iframe", "object", "embed"],
    FORBID_ATTR: ["onerror", "onload", "onclick", "onmouseover"],
    ADD_ATTR: ["target"], // Allow target attribute for links
    ALLOW_DATA_ATTR: true, // Allow data attributes for advanced formatting
  });
}

/**
 * Converts HTML to plain text for previews or excerpts
 * @param html The HTML string to convert
 * @param maxLength Optional maximum length of the resulting string
 * @returns Plain text with HTML tags removed
 */
export const htmlToPlainText = (html: string, maxLength?: number): string => {
  if (!html) return "";

  // Create a DOM element to parse the HTML
  const doc = new DOMParser().parseFromString(html, "text/html");
  let text = doc.body.textContent || "";

  // Trim and limit length if specified
  text = text.trim();
  if (maxLength && text.length > maxLength) {
    text = text.substring(0, maxLength) + "...";
  }

  return text;
};
