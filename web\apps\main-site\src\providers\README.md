# React Providers

React providers that wrap the application with third-party services and global state management.

## Provider Types

This directory contains providers for:

- **TanStack Query** - Server state management and caching
- **Theme Provider** - Dark/light theme management
- **Toast/Notification Provider** - Global notification system
- **Authentication Provider** - User session management
- **Real-time Provider** - SSE connections and live updates

Providers are typically composed in the root layout to make services available throughout the component tree while maintaining proper initialization order.
