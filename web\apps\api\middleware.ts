/**
 * @file c:\Users\<USER>\projects\Bank-of-styx-website\web\apps\api\middleware.ts
 * @summary Next.js middleware for handling API requests. It adds CORS headers to responses for API routes and provides a helper function to handle CORS preflight OPTIONS requests.
 */
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

/**
 * Middleware function to intercept requests. Adds CORS headers to API responses.
 * @param {NextRequest} request - The incoming request object.
 * @returns {NextResponse} The response object, potentially modified with CORS headers.
 */
export function middleware(request: NextRequest) {
  // Check if the request is for an API endpoint
  if (request.nextUrl.pathname.startsWith("/api/")) {
    // Get response from the origin
    const response = NextResponse.next();

    // Add CORS headers
    response.headers.set("Access-Control-Allow-Origin", "*");
    response.headers.set(
      "Access-Control-Allow-Methods",
      "GET, POST, PUT, DELETE, OPTIONS",
    );
    response.headers.set(
      "Access-Control-Allow-Headers",
      "Content-Type, Authorization",
    );

    return response;
  }

  // For all other requests, just continue the request
  return NextResponse.next();
}

/**
 * Handles CORS preflight OPTIONS requests.
 * @param {NextRequest} request - The incoming request object.
 * @returns {Promise<NextResponse | null>} A NextResponse with CORS headers if it's an OPTIONS request, otherwise null.
 */
export async function handleOptionRequest(request: NextRequest) {
  // If this is an OPTIONS request, return a response with CORS headers
  if (request.method === "OPTIONS") {
    const response = new NextResponse(null, { status: 204 });

    response.headers.set("Access-Control-Allow-Origin", "*");
    response.headers.set(
      "Access-Control-Allow-Methods",
      "GET, POST, PUT, DELETE, OPTIONS",
    );
    response.headers.set(
      "Access-Control-Allow-Headers",
      "Content-Type, Authorization",
    );

    return response;
  }

  return null;
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: "/api/:path*",
};
