import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/product-categories - List all product categories (public)
export async function GET(req: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(req.url);
    const isActive = searchParams.get("isActive") === "true";

    // Build filter
    const filter: any = {};
    if (searchParams.has("isActive")) filter.isActive = isActive;

    // Get categories with filtering
    const categories = await prisma.productCategory.findMany({
      where: filter,
      orderBy: {
        name: "asc",
      },
    });

    return NextResponse.json({ categories });
  } catch (error) {
    console.error("Error fetching product categories:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
