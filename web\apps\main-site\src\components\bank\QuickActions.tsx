import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface Action {
  name: string;
  path: string;
  icon: string;
  disabled?: boolean;
}

export const QuickActions: React.FC = () => {
  const pathname = usePathname();

  // Function to check if a path is active
  const isActive = (path: string) => {
    if (path === "/bank/dashboard") {
      return pathname === "/bank/dashboard";
    }
    return pathname.startsWith(path);
  };

  // The 4 main quick actions that will always be shown
  const mainActions: Action[] = [
    {
      name: "Transfer",
      path: "/bank/dashboard/transfer",
      icon: "M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4",
    },
    {
      name: "Transactions",
      path: "/bank/dashboard/transactions",
      icon: "M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2",
    },
    {
      name: "Deposit",
      path: "/bank/dashboard/deposit",
      icon: "M12 4v16m8-8H4",
    },
    {
      name: "Withdraw",
      path: "/bank/dashboard/withdraw",
      icon: "M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z",
    },
  ];

  // Additional actions for medium screens (768px-896px)
  const additionalActions: Action[] = [
    {
      name: "Pay-Code",
      path: "/bank/dashboard/pay-code",
      icon: "M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2z",
    },
    {
      name: "Donate",
      path: "/bank/dashboard/donate",
      icon: "M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z",
    },
  ];

  // Combine actions for medium screens
  const mediumScreenActions = [...mainActions, ...additionalActions];

  // Mobile view actions (same as medium but with different order)
  const mobileActions = [
    ...mainActions.slice(0, 3), // First 3 main actions
    {
      name: "Pay-Code",
      path: "/bank/dashboard/pay-code",
      icon: "M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2z",
    },
    mainActions[3], // Withdraw (4th main action)
    {
      name: "Donate",
      path: "/bank/dashboard/donate",
      icon: "M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z",
    },
  ];

  // Action button component to avoid repetition
  const ActionButton: React.FC<{ action: Action }> = ({ action }) => {
    // For disabled buttons, render a div instead of a link
    if (action.disabled) {
      return (
        <div
          className={`
            flex flex-col items-center justify-center p-2 sm:p-3 rounded-md transition-colors
            bg-secondary border-2 border-gray-600 opacity-70 cursor-not-allowed
          `}
        >
          <svg
            className="w-5 h-5 sm:w-6 sm:h-6 text-text-secondary mb-1 sm:mb-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d={action.icon}
            />
          </svg>
          <span className="text-xs sm:text-sm font-medium text-text-secondary">
            {action.name}
          </span>
        </div>
      );
    }

    // For active buttons
    return (
      <Link
        href={action.path}
        className={`
          flex flex-col items-center justify-center p-2 sm:p-3 rounded-md transition-colors
          ${
            isActive(action.path)
              ? "bg-secondary border-2 border-primary"
              : "bg-secondary hover:bg-hover border-2 border-transparent"
          }
        `}
      >
        <svg
          className="w-5 h-5 sm:w-6 sm:h-6 text-primary mb-1 sm:mb-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d={action.icon}
          />
        </svg>
        <span className="text-xs sm:text-sm font-medium">{action.name}</span>
      </Link>
    );
  };

  return (
    <div className="bg-secondary-light rounded-lg shadow-md p-4">
      <h2 className="text-lg font-semibold mb-4">Quick Actions</h2>

      {/* Large Desktop view (896px+) - Show only the 4 main actions */}
      <div className="hidden min-[896px]:grid min-[896px]:grid-cols-2 gap-3">
        {mainActions.map((action) => (
          <ActionButton key={action.name} action={action} />
        ))}
      </div>

      {/* Medium view (768px-896px) - Show all 8 actions in a 4-column grid */}
      <div className="hidden md:grid md:grid-cols-4 min-[896px]:hidden gap-3">
        {mediumScreenActions.map((action) => (
          <ActionButton key={action.name} action={action} />
        ))}
      </div>

      {/* Mobile view (below 768px) - Show all actions in a 2-column grid */}
      <div className="grid grid-cols-2 gap-2 md:hidden">
        {mobileActions.map((action) => (
          <ActionButton key={action.name} action={action} />
        ))}
      </div>
    </div>
  );
};
