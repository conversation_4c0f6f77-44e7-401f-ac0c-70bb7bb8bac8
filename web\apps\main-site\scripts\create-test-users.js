// Load environment variables manually (avoiding dotenv dependency)
const fs = require('fs');
const path = require('path');

function loadEnvFile(filePath) {
  try {
    const fullPath = path.join(__dirname, '..', filePath);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      content.split('\n').forEach(line => {
        line = line.trim();
        // Skip comments and empty lines
        if (!line || line.startsWith('#')) return;
        
        const equalIndex = line.indexOf('=');
        if (equalIndex === -1) return;
        
        const key = line.substring(0, equalIndex).trim();
        let value = line.substring(equalIndex + 1).trim();
        
        // Remove quotes if present
        if ((value.startsWith('"') && value.endsWith('"')) || 
            (value.startsWith("'") && value.endsWith("'"))) {
          value = value.slice(1, -1);
        }
        
        if (!process.env[key]) {
          process.env[key] = value;
          const safeValue = key.includes('PASSWORD') || key.includes('SECRET') || key.includes('DATABASE_URL') 
            ? '***' 
            : value;
          console.log(`Set ${key}=${safeValue}`);
        }
      });
      console.log(`Loaded environment from ${filePath}`);
    }
  } catch (error) {
    // Ignore errors - file might not exist
  }
}

// Try loading from different env files
loadEnvFile('.env.local');
loadEnvFile('.env.production');
loadEnvFile('.env');

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

// Check if DATABASE_URL is loaded
console.log('Available environment variables:', Object.keys(process.env).filter(k => k.includes('DATABASE') || k.includes('JWT')));
console.log('DATABASE_URL found:', !!process.env.DATABASE_URL);

if (!process.env.DATABASE_URL) {
  console.error('Error: DATABASE_URL environment variable not found.');
  console.error('Please ensure you have a .env.local file with DATABASE_URL set.');
  console.error('Example: DATABASE_URL="mysql://root:password@localhost:3306/bank_of_styx"');
  process.exit(1);
}

const prisma = new PrismaClient();

async function createTestUsers() {
  console.log('Creating test users for load testing...');
  console.log('Using database:', process.env.DATABASE_URL.replace(/:[^:]*@/, ':***@')); // Hide password

  const testUsers = [
    { email: '<EMAIL>', username: 'testuser1', isAdmin: false },
    { email: '<EMAIL>', username: 'testuser2', isAdmin: false },
    { email: '<EMAIL>', username: 'testuser3', isAdmin: false },
    { email: '<EMAIL>', username: 'testuser4', isAdmin: false },
    { email: '<EMAIL>', username: 'testuser5', isAdmin: false },
    { email: '<EMAIL>', username: 'testuser6', isAdmin: false },
    { email: '<EMAIL>', username: 'testuser7', isAdmin: false },
    { email: '<EMAIL>', username: 'testuser8', isAdmin: false },
    { email: '<EMAIL>', username: 'testuser9', isAdmin: false },
    { email: '<EMAIL>', username: 'testuser10', isAdmin: false },
    // Add some privileged users for admin tests
    { email: '<EMAIL>', username: 'testmod', isAdmin: false, isBanker: true },
    { email: '<EMAIL>', username: 'testadmin', isAdmin: true },
  ];

  const password = 'TestPassword123!';
  const hashedPassword = await bcrypt.hash(password, 10);

  for (const userData of testUsers) {
    try {
      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email: userData.email }
      });

      if (existingUser) {
        console.log(`User ${userData.email} already exists, skipping...`);
        continue;
      }

      // Create user
      const user = await prisma.user.create({
        data: {
          email: userData.email,
          username: userData.username,
          displayName: userData.username, // Add required displayName field
          passwordHash: hashedPassword,
          isAdmin: userData.isAdmin || false,
          isBanker: userData.isBanker || false,
          isEmailVerified: true, // Use correct field name
          balance: Math.floor(Math.random() * 1000) + 100, // Random balance for testing
        }
      });

      // Create some initial transactions for banking tests
      if (!userData.isAdmin) {
        await prisma.transaction.createMany({
          data: [
            {
              userId: user.id,
              amount: 100.00,
              type: 'DEPOSIT',
              description: 'Initial test deposit',
              status: 'COMPLETED',
              createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
            },
            {
              userId: user.id,
              amount: -25.50,
              type: 'PURCHASE',
              description: 'Test purchase',
              status: 'COMPLETED',
              createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
            },
          ]
        });
      }

      const roleStr = userData.isAdmin ? 'ADMIN' : userData.isBanker ? 'BANKER' : 'USER';
      console.log(`Created test user: ${userData.email} (${roleStr})`);
    } catch (error) {
      console.error(`Failed to create user ${userData.email}:`, error.message);
    }
  }

  console.log('\nTest users created successfully!');
  console.log('Login credentials for all test users:');
  console.log('Password: TestPassword123!');
  console.log('\nAvailable test accounts:');
  testUsers.forEach(user => {
    console.log(`- ${user.email} (${user.role})`);
  });
}

async function main() {
  try {
    await createTestUsers();
  } catch (error) {
    console.error('Error creating test users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}

module.exports = { createTestUsers };