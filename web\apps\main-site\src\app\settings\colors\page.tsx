"use client";

import React, { useState, useEffect } from "react";
import { Card } from "@bank-of-styx/ui";
import Link from "next/link";
import { useRouter } from "next/navigation";
import ColorThemeScript from "./ColorThemeScript";
import { ColorPicker } from "../../../components/settings/ColorPicker";
import { ThemePresets } from "../../../components/settings/ThemePresets";

// Define color categories and their variables
const colorCategories = [
  {
    name: "Primary Colors",
    colors: [
      {
        name: "Primary",
        variable: "--color-primary",
        description: "Discord blue, used for primary actions",
      },
      {
        name: "Primary Dark",
        variable: "--color-primary-dark",
        description: "Used for hover states",
      },
      {
        name: "Primary Light",
        variable: "--color-primary-light",
        description: "Used for backgrounds and accents",
      },
    ],
  },
  {
    name: "Secondary Colors",
    colors: [
      {
        name: "Secondary",
        variable: "--color-secondary",
        description: "Discord dark, used for nav and input backgrounds",
      },
      {
        name: "Secondary Dark",
        variable: "--color-secondary-dark",
        description: "Near black, used for page background",
      },
      {
        name: "Secondary Light",
        variable: "--color-secondary-light",
        description: "Lighter dark, used for card backgrounds",
      },
    ],
  },
  {
    name: "Accent Colors",
    colors: [
      {
        name: "Accent",
        variable: "--color-accent",
        description: "Discord red, used for destructive actions",
      },
      {
        name: "Accent Dark",
        variable: "--color-accent-dark",
        description: "Used for hover states",
      },
      {
        name: "Accent Light",
        variable: "--color-accent-light",
        description: "Used for backgrounds and accents",
      },
    ],
  },
  {
    name: "Status Colors",
    colors: [
      {
        name: "Success",
        variable: "--color-success",
        description: "Green for success states",
      },
      {
        name: "Warning",
        variable: "--color-warning",
        description: "Orange for warnings",
      },
      {
        name: "Error",
        variable: "--color-error",
        description: "Red for errors",
      },
      {
        name: "Info",
        variable: "--color-info",
        description: "Blue for info (same as primary)",
      },
    ],
  },
  {
    name: "Text Colors",
    colors: [
      {
        name: "Text Primary",
        variable: "--color-text-primary",
        description: "Primary text color on dark backgrounds",
      },
      {
        name: "Text Secondary",
        variable: "--color-text-secondary",
        description: "Secondary text color",
      },
      {
        name: "Text Muted",
        variable: "--color-text-muted",
        description: "Muted text color",
      },
      {
        name: "Text Disabled",
        variable: "--color-text-disabled",
        description: "Disabled text",
      },
    ],
  },
  {
    name: "Border Colors",
    colors: [
      {
        name: "Border Dark",
        variable: "--color-border-dark",
        description: "Default border color",
      },
      {
        name: "Border Primary",
        variable: "--color-border-primary",
        description: "Primary border color",
      },
      {
        name: "Border Accent",
        variable: "--color-border-accent",
        description: "Accent border color",
      },
      {
        name: "Border Subtle",
        variable: "--color-border-subtle",
        description: "Subtle border/divider",
      },
    ],
  },
  {
    name: "Background Colors",
    colors: [
      {
        name: "Background Page",
        variable: "--color-bg-page",
        description: "Main page background",
      },
      {
        name: "Background Card",
        variable: "--color-bg-card",
        description: "Card background",
      },
      {
        name: "Background Input",
        variable: "--color-bg-input",
        description: "Input background",
      },
      {
        name: "Background Hover",
        variable: "--color-bg-hover",
        description: "Hover background",
      },
    ],
  },
];

export default function ColorSettingsPage() {
  // State to store current color values
  const [colorValues, setColorValues] = useState<Record<string, string>>({});
  // State to track if changes have been made
  const [hasChanges, setHasChanges] = useState(false);
  // State to store original values for reset functionality
  const [originalValues, setOriginalValues] = useState<Record<string, string>>(
    {},
  );

  // State to track if component is mounted (client-side)
  const [mounted, setMounted] = useState(false);
  // State to store the previous page URL
  const [previousPage, setPreviousPage] = useState("/");
  const router = useRouter();

  // Load current CSS variable values on component mount
  useEffect(() => {
    setMounted(true);

    const root = document.documentElement;
    const computedStyle = getComputedStyle(root);

    const values: Record<string, string> = {};

    // Get all color variables
    colorCategories.forEach((category) => {
      category.colors.forEach((color) => {
        const value = computedStyle.getPropertyValue(color.variable).trim();
        values[color.variable] = value;
      });
    });

    setColorValues(values);
    setOriginalValues(values);

    // Store the previous page URL from localStorage or referrer
    const storedPreviousPage = localStorage.getItem("previousPage");
    if (storedPreviousPage && storedPreviousPage !== "/settings/colors") {
      setPreviousPage(storedPreviousPage);
    } else if (
      document.referrer &&
      document.referrer.includes(window.location.host)
    ) {
      // Extract path from referrer URL if it's from the same host
      const referrerUrl = new URL(document.referrer);
      const path = referrerUrl.pathname;
      if (path && path !== "/settings/colors") {
        setPreviousPage(path);
      }
    }

    // Store current page before navigating away
    const handleBeforeUnload = () => {
      if (window.location.pathname !== "/settings/colors") {
        localStorage.setItem("previousPage", window.location.pathname);
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, []);

  // Handle color change
  const handleColorChange = (variable: string, value: string) => {
    setColorValues((prev) => {
      const newValues = { ...prev, [variable]: value };

      // Apply the change to the document
      document.documentElement.style.setProperty(variable, value);

      // Check if any values are different from original
      const changed = Object.keys(newValues).some(
        (key) => newValues[key] !== originalValues[key],
      );
      setHasChanges(changed);

      return newValues;
    });
  };

  // Apply all changes permanently by updating the CSS variables in localStorage
  const applyChanges = () => {
    localStorage.setItem("bankOfStyxColorTheme", JSON.stringify(colorValues));
    setHasChanges(false);
    alert(
      "Color theme saved! These settings will persist across page refreshes.",
    );
  };

  // Reset to original values
  const resetChanges = () => {
    // Reset all variables to their original values
    Object.entries(originalValues).forEach(([variable, value]) => {
      document.documentElement.style.setProperty(variable, value);
    });

    setColorValues(originalValues);
    setHasChanges(false);
    localStorage.removeItem("bankOfStyxColorTheme");
  };

  // Reset to defaults (remove from localStorage and reload)
  const resetToDefaults = () => {
    localStorage.removeItem("bankOfStyxColorTheme");
    window.location.reload();
  };

  // Apply a preset theme
  const applyPreset = (presetColors: Record<string, string>) => {
    // Apply all colors from the preset
    Object.entries(presetColors).forEach(([variable, value]) => {
      document.documentElement.style.setProperty(variable, value);
    });

    // Update state
    setColorValues(presetColors);
    setHasChanges(true);
  };

  // Don't render anything on the server
  if (!mounted) {
    return (
      <div className="container mx-auto px-4 py-8">
        Loading color settings...
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <ColorThemeScript />
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold mb-2">Color Settings</h1>
          <p className="text-text-secondary">
            Customize the color scheme of the Bank of Styx website
          </p>
        </div>
        <div className="flex space-x-4">
          <Link
            href={previousPage}
            className="px-4 py-2 rounded border border-gray-600 hover:bg-secondary-light transition"
          >
            Back
          </Link>
          {hasChanges && (
            <>
              <button
                onClick={resetChanges}
                className="px-4 py-2 rounded border border-gray-600 hover:bg-secondary-light transition"
              >
                Reset Changes
              </button>
              <button
                onClick={applyChanges}
                className="px-4 py-2 rounded bg-primary hover:bg-primary-dark transition"
              >
                Save Changes
              </button>
            </>
          )}
          <button
            onClick={resetToDefaults}
            className="px-4 py-2 rounded bg-accent hover:bg-accent-dark transition"
          >
            Reset to Defaults
          </button>
        </div>
      </div>

      {/* Theme Presets */}
      <Card title="Theme Presets" className="mb-8" headerAction={null}>
        <div className="p-4">
          <p className="mb-4 text-text-secondary">
            Choose from predefined themes including light and dark mode options,
            or customize colors below
          </p>
          <div className="mb-4">
            <p className="text-sm text-text-secondary mb-2">
              <span className="font-medium">Note:</span> The theme toggle in the
              avatar menu will switch between Discord Light theme and your
              selected dark theme. You can also select specific theme presets
              here for more customization options.
            </p>
            <p className="text-sm text-text-secondary">
              Choose from 8 themes total, including 2 light themes and 6 dark
              themes. The Pirate Gold and Crimson Gold themes are designed
              specifically for the Bank of Styx.
            </p>
          </div>
          <ThemePresets onApplyPreset={applyPreset} />
        </div>
      </Card>

      {/* Preview section */}
      <Card title="Preview" className="mb-8" headerAction={null}>
        <div className="p-4 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-secondary p-4 rounded-lg">
              <h3 className="font-semibold mb-2">Secondary Background</h3>
              <p className="text-text-secondary">
                Text on secondary background
              </p>
              <button className="mt-2 bg-primary hover:bg-primary-dark px-4 py-2 rounded transition">
                Primary Button
              </button>
            </div>
            <div className="bg-secondary-light p-4 rounded-lg">
              <h3 className="font-semibold mb-2">Secondary Light Background</h3>
              <p className="text-text-secondary">
                Text on secondary light background
              </p>
              <button className="mt-2 bg-accent hover:bg-accent-dark px-4 py-2 rounded transition">
                Accent Button
              </button>
            </div>
            <div className="bg-secondary-dark p-4 rounded-lg">
              <h3 className="font-semibold mb-2">Secondary Dark Background</h3>
              <p className="text-text-secondary">
                Text on secondary dark background
              </p>
              <div className="mt-2 flex space-x-2">
                <div className="w-8 h-8 rounded-full bg-success"></div>
                <div className="w-8 h-8 rounded-full bg-warning"></div>
                <div className="w-8 h-8 rounded-full bg-error"></div>
                <div className="w-8 h-8 rounded-full bg-info"></div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Color categories */}
      {colorCategories.map((category) => (
        <Card
          key={category.name}
          title={category.name}
          className="mb-6"
          headerAction={null}
        >
          <div className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {category.colors.map((color) => (
                <div key={color.variable}>
                  <ColorPicker
                    value={colorValues[color.variable] || ""}
                    onChange={(value) =>
                      handleColorChange(color.variable, value)
                    }
                    label={color.name}
                    description={color.description}
                    variable={color.variable}
                  />
                </div>
              ))}
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}
