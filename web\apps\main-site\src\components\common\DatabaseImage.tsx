"use client";

import { useState } from "react";
import Image, { ImageProps } from "next/image";

interface DatabaseImageProps extends Omit<ImageProps, "src" | "alt"> {
  imageId: string;
  alt?: string;
  width?: number;
  height?: number;
}

export function DatabaseImage({
  imageId,
  alt = "Image",
  width = 800,
  height = 600,
  ...props
}: DatabaseImageProps) {
  // Use ID-based URL
  const src = `/api/images/${imageId}`;

  return <Image src={src} alt={alt} width={width} height={height} {...props} />;
}
