import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export async function GET(request: Request) {
  try {
    // Check if user is authenticated and has admin role
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const isAdmin = await userHasRole(request, "admin");
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Admin privileges required" },
        { status: 403 },
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const type = url.searchParams.get("type") as "news" | "hero" | null;

    // Get featured content based on type
    if (type === "news") {
      // Get featured news articles
      const featuredNews = await prisma.newsArticle.findMany({
        where: {
          featured: true,
        },
        select: {
          id: true,
          title: true,
          image: true,
          featured: true,
          updatedAt: true,
        },
        orderBy: {
          updatedAt: "desc",
        },
      });

      // Transform to match the FeaturedContent interface
      const transformedNews = featuredNews.map((article) => ({
        id: article.id,
        title: article.title,
        type: "news" as const,
        image: article.image,
        featured: article.featured,
        updatedAt: article.updatedAt.toISOString(),
      }));

      return NextResponse.json(transformedNews);
    } else if (type === "hero") {
      // This would typically come from a hero banner table
      // For now, return a placeholder
      return NextResponse.json([
        {
          id: "hero-1",
          title: "Bank of Styx Welcome Banner",
          type: "hero",
          image: "/images/hero/welcome-banner.jpg",
          featured: true,
          updatedAt: new Date().toISOString(),
        },
      ]);
    } else {
      // Get all featured content
      const featuredNews = await prisma.newsArticle.findMany({
        where: {
          featured: true,
        },
        select: {
          id: true,
          title: true,
          image: true,
          featured: true,
          updatedAt: true,
        },
        orderBy: {
          updatedAt: "desc",
        },
      });

      // Transform news to match the FeaturedContent interface
      const transformedNews = featuredNews.map((article) => ({
        id: article.id,
        title: article.title,
        type: "news" as const,
        image: article.image,
        featured: article.featured,
        updatedAt: article.updatedAt.toISOString(),
      }));

      // Add hero banner (placeholder)
      const allFeaturedContent = [
        ...transformedNews,
        {
          id: "hero-1",
          title: "Bank of Styx Welcome Banner",
          type: "hero" as const,
          image: "/images/hero/welcome-banner.jpg",
          featured: true,
          updatedAt: new Date().toISOString(),
        },
      ];

      return NextResponse.json(allFeaturedContent);
    }
  } catch (error) {
    console.error("Error fetching featured content:", error);
    return NextResponse.json(
      { error: "Failed to fetch featured content" },
      { status: 500 },
    );
  }
}
