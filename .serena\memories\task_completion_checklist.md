# Bank of Styx - Task Completion Checklist

## After Making Code Changes

### 1. Database Changes (If Applicable)
```bash
cd web/apps/main-site

# If schema.prisma was modified:
npx prisma migrate dev           # Create migration
npx prisma migrate deploy        # Apply to database
npx prisma generate             # Update Prisma client
npx prisma migrate status       # Verify success
```

### 2. Code Quality Checks
```bash
# From project root:
pnpm lint                       # Check linting
pnpm format                     # Format code
cd web/apps/main-site && pnpm check  # Type checking
```

### 3. Build Verification
```bash
# Verify build succeeds:
pnpm build
```

### 4. Testing (If Available)
```bash
cd web/apps/main-site

# Run tests if implemented:
pnpm test:e2e                   # E2E tests
pnpm test:load                  # Load tests (if needed)
```

### 5. Critical Verification Points
- [ ] Database migrations applied successfully
- [ ] No TypeScript errors
- [ ] No ESLint errors
- [ ] Code properly formatted
- [ ] Build completes without errors
- [ ] No security vulnerabilities introduced

## Before Committing (If Requested)
- [ ] All tests pass
- [ ] Code follows project conventions
- [ ] No sensitive data in commits
- [ ] Proper commit message format

## Never Do Automatically
- **Don't commit changes** unless explicitly requested
- **Don't push to remote** unless explicitly requested
- **Don't run database seeds** in production
- **Don't modify .env files** with real credentials

## Windows-Specific Notes
- Use PowerShell or Command Prompt for commands
- File paths use backslashes in Windows
- Ensure cross-platform compatibility for any scripts