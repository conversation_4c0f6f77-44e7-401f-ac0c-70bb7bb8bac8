import React, { useState } from "react";
import UniversalImageUploader from "../common/UniversalImageUploader";
import { UploadResponse } from "@/types/upload";
import { uploadConfig } from "@/lib/uploadConfig";

interface EventImageUploaderProps {
  eventId: string;
  onUploadComplete: (response: UploadResponse) => void;
  onUploadStart?: () => void;
  aspectRatio?: number;
  label?: string;
  description?: string;
  className?: string;
  imageType?: "featured" | "gallery" | "banner";
}

const EventImageUploader: React.FC<EventImageUploaderProps> = ({
  eventId,
  onUploadComplete,
  onUploadStart,
  aspectRatio = 16 / 9,
  label,
  description,
  className = "",
  imageType = "featured",
}) => {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const getDefaultLabel = () => {
    switch (imageType) {
      case "banner":
        return "Event Banner Image";
      case "gallery":
        return "Event Gallery Image";
      default:
        return "Event Featured Image";
    }
  };

  const getDefaultDescription = () => {
    switch (imageType) {
      case "banner":
        return "Upload a banner image for the event header";
      case "gallery":
        return "Upload an image for the event gallery";
      default:
        return "Upload a featured image that represents this event";
    }
  };

  const handleUploadStart = () => {
    setIsUploading(true);
    onUploadStart?.();
  };

  const handleUploadComplete = (response: UploadResponse) => {
    setIsUploading(false);

    if (response.success) {
      // Handle both new format (response.file.url) and legacy format (response.url)
      const imageUrl = response.file?.url || response.url;
      if (imageUrl) {
        setPreviewUrl(imageUrl);
      }
    }

    onUploadComplete(response);
  };

  const handleRemoveImage = () => {
    setPreviewUrl(null);
    // Call onUploadComplete with a "removed" status
    onUploadComplete({
      success: true,
      message: "Image removed",
    });
  };

  // Calculate dimensions based on image type
  const getImageDimensions = () => {
    switch (imageType) {
      case "banner":
        return { width: 1920, height: Math.round(1920 / aspectRatio) };
      case "gallery":
        return { width: 800, height: Math.round(800 / aspectRatio) };
      default:
        return { width: 1200, height: Math.round(1200 / aspectRatio) };
    }
  };

  const dimensions = getImageDimensions();

  return (
    <div className={`space-y-4 ${className}`}>
      <div>
        <h3 className="text-lg font-medium text-gray-900">
          {label || getDefaultLabel()}
        </h3>
        <p className="text-sm text-gray-500 mt-1">
          {description || getDefaultDescription()}
        </p>
      </div>

      {/* Image Preview */}
      {previewUrl && (
        <div className="relative">
          <div className="border border-gray-300 rounded-lg overflow-hidden">
            <img
              src={previewUrl}
              alt="Event preview"
              className={`w-full object-cover ${
                imageType === "banner" ? "h-32" : "h-48"
              }`}
              style={{ aspectRatio }}
            />
          </div>
          <button
            type="button"
            onClick={handleRemoveImage}
            disabled={isUploading}
            className="absolute top-2 right-2 bg-red-600 text-white rounded-full p-1 hover:bg-red-700 disabled:opacity-50"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      )}

      <UniversalImageUploader
        uploadType="event"
        entityId={eventId}
        onUploadComplete={handleUploadComplete}
        onUploadStart={handleUploadStart}
        options={{
          maxSize: uploadConfig.event.maxSize,
          allowedTypes: uploadConfig.event.allowedTypes,
          width: dimensions.width,
          height: dimensions.height,
          quality: uploadConfig.event.quality,
          processImage: uploadConfig.event.processImage,
        }}
      />

      <div className="text-sm text-gray-500 space-y-1">
        <p>
          • Recommended size: {dimensions.width}x{dimensions.height}px (
          {aspectRatio === 16 / 9
            ? "16:9"
            : aspectRatio === 4 / 3
            ? "4:3"
            : "custom"}{" "}
          aspect ratio)
        </p>
        <p>
          • Max file size:{" "}
          {(uploadConfig.event.maxSize / (1024 * 1024)).toFixed(1)}MB
        </p>
        <p>
          • Accepted formats:{" "}
          {uploadConfig.event.allowedTypes
            .map((type) => type.split("/")[1].toUpperCase())
            .join(", ")}
        </p>
        {imageType === "featured" && (
          <p>• This image will be displayed prominently on the event page</p>
        )}
        {imageType === "banner" && (
          <p>• Banner images appear at the top of event pages</p>
        )}
        {imageType === "gallery" && (
          <p>• Gallery images showcase the event atmosphere</p>
        )}
        <p>• Images will be automatically optimized for web delivery</p>
      </div>

      {isUploading && (
        <div className="mt-2 text-sm text-blue-600 flex items-center">
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          Processing event image...
        </div>
      )}
    </div>
  );
};

export default EventImageUploader;
