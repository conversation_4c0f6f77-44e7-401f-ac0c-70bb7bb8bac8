import React, { useState } from "react";
import { Ledger } from "../../services/bankService";
import { toast } from "react-hot-toast";

interface LedgerDisplayProps {
  ledgerEntries: Ledger[];
  onVerify: (id: string) => Promise<void>;
  onCreateEntry: (data: {
    description: string;
    totalDeposits: number;
    totalWithdrawals: number;
    totalTransfers: number;
    netChange: number;
  }) => Promise<void>;
}

export const LedgerDisplay: React.FC<LedgerDisplayProps> = ({
  ledgerEntries,
  onVerify,
  onCreateEntry,
}) => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [description, setDescription] = useState("");
  const [totalDeposits, setTotalDeposits] = useState("");
  const [totalWithdrawals, setTotalWithdrawals] = useState("");
  const [totalTransfers, setTotalTransfers] = useState("");
  const [netChange, setNetChange] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedEntry, setSelectedEntry] = useState<Ledger | null>(null);
  const [showVerifyConfirmation, setShowVerifyConfirmation] = useState(false);

  const handleCreateEntry = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate inputs
    if (!description) {
      toast.error("Description is required");
      return;
    }

    if (!totalDeposits || isNaN(Number(totalDeposits))) {
      toast.error("Valid total deposits amount is required");
      return;
    }

    if (!totalWithdrawals || isNaN(Number(totalWithdrawals))) {
      toast.error("Valid total withdrawals amount is required");
      return;
    }

    if (!totalTransfers || isNaN(Number(totalTransfers))) {
      toast.error("Valid total transfers amount is required");
      return;
    }

    if (!netChange || isNaN(Number(netChange))) {
      toast.error("Valid net change amount is required");
      return;
    }

    try {
      setIsSubmitting(true);

      await onCreateEntry({
        description,
        totalDeposits: Number(totalDeposits),
        totalWithdrawals: Number(totalWithdrawals),
        totalTransfers: Number(totalTransfers),
        netChange: Number(netChange),
      });

      // Reset form
      setDescription("");
      setTotalDeposits("");
      setTotalWithdrawals("");
      setTotalTransfers("");
      setNetChange("");
      setShowCreateForm(false);

      toast.success("Ledger entry created successfully");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "An error occurred";
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleVerifyEntry = (entry: Ledger) => {
    setSelectedEntry(entry);
    setShowVerifyConfirmation(true);
  };

  const handleConfirmVerify = async () => {
    if (!selectedEntry) return;

    try {
      await onVerify(selectedEntry.id);
      setShowVerifyConfirmation(false);
      setSelectedEntry(null);
      toast.success("Ledger entry verified successfully");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "An error occurred";
      toast.error(errorMessage);
    }
  };

  const handleCancelVerify = () => {
    setShowVerifyConfirmation(false);
    setSelectedEntry(null);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const calculateNetChange = () => {
    const deposits = Number(totalDeposits) || 0;
    const withdrawals = Number(totalWithdrawals) || 0;
    const net = deposits - withdrawals;
    setNetChange(net.toString());
  };

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-xl font-semibold text-white">Ledger Entries</h3>
        <button
          onClick={() => setShowCreateForm(!showCreateForm)}
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-light"
        >
          {showCreateForm ? "Cancel" : "Create New Entry"}
        </button>
      </div>

      {showCreateForm && (
        <div className="bg-secondary-dark p-4 rounded-lg border border-gray-600 mb-6">
          <h4 className="font-semibold text-white mb-3">Create Ledger Entry</h4>
          <form onSubmit={handleCreateEntry}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-gray-400 mb-1">Description</label>
                <input
                  type="text"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Daily Balance Verification"
                  className="w-full bg-secondary border border-gray-600 rounded-md p-2 text-white"
                  required
                />
              </div>
              <div>
                <label className="block text-gray-400 mb-1">
                  Total Deposits
                </label>
                <input
                  type="number"
                  value={totalDeposits}
                  onChange={(e) => {
                    setTotalDeposits(e.target.value);
                    // Recalculate net change when deposits change
                    setTimeout(calculateNetChange, 0);
                  }}
                  placeholder="0"
                  className="w-full bg-secondary border border-gray-600 rounded-md p-2 text-white"
                  required
                />
              </div>
              <div>
                <label className="block text-gray-400 mb-1">
                  Total Withdrawals
                </label>
                <input
                  type="number"
                  value={totalWithdrawals}
                  onChange={(e) => {
                    setTotalWithdrawals(e.target.value);
                    // Recalculate net change when withdrawals change
                    setTimeout(calculateNetChange, 0);
                  }}
                  placeholder="0"
                  className="w-full bg-secondary border border-gray-600 rounded-md p-2 text-white"
                  required
                />
              </div>
              <div>
                <label className="block text-gray-400 mb-1">
                  Total Transfers
                </label>
                <input
                  type="number"
                  value={totalTransfers}
                  onChange={(e) => setTotalTransfers(e.target.value)}
                  placeholder="0"
                  className="w-full bg-secondary border border-gray-600 rounded-md p-2 text-white"
                  required
                />
              </div>
              <div>
                <label className="block text-gray-400 mb-1">Net Change</label>
                <input
                  type="number"
                  value={netChange}
                  onChange={(e) => setNetChange(e.target.value)}
                  placeholder="0"
                  className="w-full bg-secondary border border-gray-600 rounded-md p-2 text-white"
                  required
                />
              </div>
            </div>
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 bg-success text-white rounded-md hover:bg-green-600 disabled:opacity-50"
              >
                {isSubmitting ? "Creating..." : "Create Entry"}
              </button>
            </div>
          </form>
        </div>
      )}

      {ledgerEntries.length === 0 ? (
        <div className="bg-secondary-dark p-4 rounded-lg border border-gray-600 text-center">
          <p className="text-gray-400">No ledger entries found.</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-secondary-dark">
                <th className="p-3 text-left text-white">ID</th>
                <th className="p-3 text-left text-white">Date</th>
                <th className="p-3 text-left text-white">Description</th>
                <th className="p-3 text-left text-white">Deposits</th>
                <th className="p-3 text-left text-white">Withdrawals</th>
                <th className="p-3 text-left text-white">Net Change</th>
                <th className="p-3 text-left text-white">Status</th>
                <th className="p-3 text-left text-white">Actions</th>
              </tr>
            </thead>
            <tbody>
              {ledgerEntries.map((entry) => (
                <tr
                  key={entry.id}
                  className="border-t border-gray-600 hover:bg-secondary-dark"
                >
                  <td className="p-3 text-white">{entry.id}</td>
                  <td className="p-3 text-white">{formatDate(entry.date)}</td>
                  <td className="p-3 text-white">{entry.description}</td>
                  <td className="p-3 text-success">
                    NS {entry.totalDeposits.toFixed(0)}
                  </td>
                  <td className="p-3 text-error">
                    NS {entry.totalWithdrawals.toFixed(0)}
                  </td>
                  <td
                    className={`p-3 ${
                      entry.netChange >= 0 ? "text-success" : "text-error"
                    }`}
                  >
                    NS {entry.netChange.toFixed(0)}
                  </td>
                  <td
                    className={`p-3 ${
                      entry.status === "verified"
                        ? "text-success"
                        : "text-warning"
                    }`}
                  >
                    {entry.status.charAt(0).toUpperCase() +
                      entry.status.slice(1)}
                  </td>
                  <td className="p-3">
                    {entry.status === "pending" && (
                      <button
                        onClick={() => handleVerifyEntry(entry)}
                        className="px-3 py-1 bg-success text-white rounded-md hover:bg-green-600"
                      >
                        Verify
                      </button>
                    )}
                    {entry.status === "verified" && (
                      <span className="text-gray-400">
                        Verified by {entry.verifiedBy?.displayName || "Unknown"}
                      </span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Verification Confirmation Modal */}
      {showVerifyConfirmation && selectedEntry && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-secondary-dark p-6 rounded-lg border border-gray-600 max-w-md w-full">
            <h3 className="text-xl font-semibold text-white mb-4">
              Confirm Verification
            </h3>
            <p className="text-gray-400 mb-4">
              Are you sure you want to verify this ledger entry? This action
              confirms that all transactions have been reconciled and the
              balances are correct.
            </p>
            <div className="flex space-x-4">
              <button
                onClick={handleConfirmVerify}
                className="px-4 py-2 bg-success text-white rounded-md hover:bg-green-600 flex-1"
              >
                Verify
              </button>
              <button
                onClick={handleCancelVerify}
                className="px-4 py-2 bg-secondary text-white rounded-md hover:bg-secondary-light flex-1"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LedgerDisplay;
