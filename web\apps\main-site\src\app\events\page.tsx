"use client";

import { useState, useEffect, useMemo } from "react";
import { format } from "date-fns";
import { SearchBar } from "@bank-of-styx/ui";
import { EventCard } from "@/components/events/EventCard";
import { useColorTheme } from "@/contexts/ColorThemeContext";
import fetchClient from "@/lib/fetchClient";

// Define types
interface Event {
  id: string;
  name: string;
  shortDescription: string | null;
  startDate: string;
  endDate: string;
  location: string | null;
  isVirtual: boolean;
  image: string | null;
  category: {
    id: string;
    name: string;
    color: string | null;
  };
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

interface EventsResponse {
  events: Event[];
  pagination: Pagination;
}

interface Category {
  id: string;
  name: string;
  description: string | null;
  color: string | null;
}

export default function EventsPage() {
  const { isDarkMode } = useColorTheme();
  const [events, setEvents] = useState<Event[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 9,
    total: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filter states
  const [search, setSearch] = useState("");
  const [categoryId, setCategoryId] = useState("");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");

  // Create a category list including the "Upcoming Events" option
  const [selectedCategory, setSelectedCategory] = useState("All Categories");
  const allCategories = useMemo(() => {
    if (!categories.length) return ["All Categories"];
    return [
      "All Categories",
      "Upcoming Events",
      ...categories.map((c) => c.name),
    ];
  }, [categories]);

  // Fetch events
  const fetchEvents = async (page = 1) => {
    setLoading(true);
    try {
      // Build query parameters
      const params: Record<string, string> = {
        page: page.toString(),
        limit: pagination.limit.toString(),
      };

      if (search) params.search = search;
      if (categoryId) params.categoryId = categoryId;
      if (selectedCategory === "Upcoming Events") params.upcoming = "true";

      const data = await fetchClient.get<EventsResponse>("/api/events", {
        params,
      });
      setEvents(data.events);
      setPagination(data.pagination);
      setError(null);
    } catch (err) {
      setError("Error loading events. Please try again.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const data = await fetchClient.get<Category[]>("/api/event-categories");
      setCategories(data);
    } catch (err) {
      console.error("Error loading categories:", err);
    }
  };

  // Handle filter changes
  const handleFilterChange = () => {
    fetchEvents(1); // Reset to first page when filters change
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    fetchEvents(newPage);
  };

  // Format date range
  const formatDateRange = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Same day event
    if (start.toDateString() === end.toDateString()) {
      return `${format(start, "MMM d, yyyy")} · ${format(
        start,
        "h:mm a",
      )} - ${format(end, "h:mm a")}`;
    }

    // Multi-day event
    return `${format(start, "MMM d")} - ${format(end, "MMM d, yyyy")}`;
  };

  // Initialize
  useEffect(() => {
    fetchEvents();
    fetchCategories();
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2 text-primary">Events</h1>
        <p className="text-secondary">
          Discover upcoming events and activities
        </p>
      </div>

      <div className="mb-4 md:mb-8 max-w-full">
        <SearchBar
          placeholder="Search events..."
          onSearch={(query: string) => {
            setSearch(query);
            fetchEvents(1);
          }}
          onClear={() => {
            setSearch("");
            fetchEvents(1);
          }}
          showButton={true}
          initialValue={search}
          categories={allCategories}
          selectedCategory={selectedCategory}
          onCategoryChange={(category: string) => {
            setSelectedCategory(category);
            if (category === "All Categories") {
              setCategoryId("");
            } else if (category === "Upcoming Events") {
              setCategoryId("");
            } else {
              const cat = categories.find((c) => c.name === category);
              if (cat) setCategoryId(cat.id);
            }
            fetchEvents(1);
          }}
          className="text-sm md:text-base"
        />

        {/* Sort options - Only show when there are results */}
        {events.length > 0 && (
          <div className="flex justify-end items-center mt-4">
            <div className="flex items-center gap-3">
              <span className="text-gray-400 mr-2 text-sm md:text-base">
                Sort by:
              </span>
              <select
                value={sortOrder}
                onChange={(e) => {
                  setSortOrder(e.target.value as "asc" | "desc");
                  fetchEvents(1);
                }}
                className="bg-secondary border border-gray-600 rounded-md px-2 py-1 text-sm md:text-base text-white focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="asc">Earliest</option>
                <option value="desc">Latest</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Error message */}
      {error && (
        <div className="mb-6 px-4 py-3 rounded bg-error-light text-error-dark border border-error">
          {error}
        </div>
      )}

      {/* Loading state */}
      {loading ? (
        <div className="text-center py-12">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary border-r-transparent"></div>
          <p className="mt-2 text-secondary">Loading events...</p>
        </div>
      ) : events.length === 0 ? (
        <div className="text-center py-12 rounded-lg bg-surface shadow-sm">
          <h3 className="text-lg font-medium mb-2 text-primary">
            No events found
          </h3>
          <p className="text-secondary">
            {search || categoryId
              ? "Try adjusting your filters to see more events"
              : "Check back later for upcoming events"}
          </p>
        </div>
      ) : (
        <div className="flex flex-col gap-6 max-w-4xl mx-auto">
          {events.map((event) => (
            <EventCard key={event.id} event={event} />
          ))}
        </div>
      )}

      {/* Pagination */}
      {!loading && pagination.totalPages > 1 && (
        <div className="flex justify-between items-center mt-8">
          <div className="text-sm text-secondary">
            Showing{" "}
            <span className="font-medium">
              {(pagination.page - 1) * pagination.limit + 1}
            </span>{" "}
            to{" "}
            <span className="font-medium">
              {Math.min(pagination.page * pagination.limit, pagination.total)}
            </span>{" "}
            of <span className="font-medium">{pagination.total}</span> events
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={!pagination.hasPrevPage}
              className={`px-4 py-2 rounded transition-colors ${
                pagination.hasPrevPage
                  ? "bg-primary text-white cursor-pointer"
                  : "bg-gray-600 text-gray-400 cursor-not-allowed"
              }`}
            >
              Previous
            </button>
            <button
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={!pagination.hasNextPage}
              className={`px-4 py-2 rounded transition-colors ${
                pagination.hasNextPage
                  ? "bg-primary text-white cursor-pointer"
                  : "bg-gray-600 text-gray-400 cursor-not-allowed"
              }`}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
