import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);

  // Parse query parameters
  const page = parseInt(searchParams.get("page") || "1");
  const limit = parseInt(searchParams.get("limit") || "10");
  const category = searchParams.get("category") || undefined;
  const search = searchParams.get("search") || undefined;
  const featured = searchParams.get("featured") === "true";
  const sortBy = searchParams.get("sortBy") || "publishedAt";
  const order = searchParams.get("order") || "desc";

  const skip = (page - 1) * limit;

  try {

    // Build the where clause
    const where: any = {
      status: "published", // Only return published articles
    };

    if (category) {
      where.category = {
        slug: category, // Match by category slug for public API
      };
    }

    if (featured) {
      where.featured = true;
    }

    if (search) {
      where.OR = [
        { title: { contains: search } },
        { excerpt: { contains: search } },
        { content: { contains: search } },
      ];
    }

    // Get total count of matching articles
    const totalCount = await prisma.newsArticle.count({ where });

    // Get the articles with pagination, sorting and include author/category
    const articles = await prisma.newsArticle.findMany({
      where,
      skip,
      take: limit,
      orderBy: {
        [sortBy]: order as "asc" | "desc",
      },
      select: {
        id: true,
        title: true,
        excerpt: true,
        image: true,
        slug: true,
        publishedAt: true,
        views: true,
        featured: true,
        author: {
          select: {
            displayName: true,
            avatar: true,
          },
        },
        category: {
          select: {
            name: true,
            slug: true,
          },
        },
      },
    });

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return NextResponse.json({
      data: articles,
      meta: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNext,
        hasPrev,
      },
    });
  } catch (error) {
    console.error("Error fetching public articles:", error);
    return NextResponse.json(
      { error: "Failed to fetch articles" },
      { status: 500 },
    );
  }
}
