"use client";

import React, { useState } from "react";
import Link from "next/link";
import { DashboardLayout } from "../../../../components/bank";
import { CreatePayCode } from "./components/CreatePayCode";
import { RedeemPayCode } from "./components/RedeemPayCode";
import { ManagePayCode } from "./components/ManagePayCode";
import { Tab } from "../../../../components/ui";

export default function PayCodePage() {
  // Main state for navigation
  const [activeTab, setActiveTab] = useState<"create" | "redeem" | "manage">(
    "create",
  );

  // Tab navigation component
  const TabNavigation = () => {
    return (
      <div className="flex space-x-1 mb-4 border-b border-gray-600">
        <Tab
          active={activeTab === "create"}
          onClick={() => setActiveTab("create")}
        >
          Create Pay Code
        </Tab>
        <Tab
          active={activeTab === "redeem"}
          onClick={() => setActiveTab("redeem")}
        >
          Redeem Pay Code
        </Tab>
        <Tab
          active={activeTab === "manage"}
          onClick={() => setActiveTab("manage")}
        >
          Manage Pay Codes
        </Tab>
      </div>
    );
  };

  // Render appropriate content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case "create":
        return <CreatePayCode />;
      case "redeem":
        return <RedeemPayCode />;
      case "manage":
        return <ManagePayCode />;
      default:
        return <CreatePayCode />;
    }
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto py-2">
        <div className="mb-3">
          <Link
            href="/bank/dashboard"
            className="text-primary hover:text-primary-light"
          >
            &larr; Back to Dashboard
          </Link>
        </div>

        <div className="bg-secondary-light rounded-lg shadow-md p-4">
          <h2 className="text-2xl font-bold text-white mb-2">
            Pay Code Management
          </h2>

          <TabNavigation />

          {/* Main content area */}
          <div className="mt-2+">{renderContent()}</div>
        </div>
      </div>
    </DashboardLayout>
  );
}
