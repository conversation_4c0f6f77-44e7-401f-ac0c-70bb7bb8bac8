import React, { useRef } from "react";
import { PayCode } from "../../../../../../services/bankService";
import { copyToClipboard } from "../../utils";

interface GeneratedCodeSuccessModalProps {
  generatedCode: PayCode;
  onClose: () => void;
}

export const GeneratedCodeSuccessModal: React.FC<
  GeneratedCodeSuccessModalProps
> = ({ generatedCode, onClose }) => {
  const codeRef = useRef<HTMLDivElement>(null);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-secondary-light rounded-lg p-6 max-w-md w-full">
        <h3 className="text-xl font-bold text-white mb-4">
          Pay Code Created Successfully!
        </h3>
        <p className="mb-4 text-white">
          Your pay code has been created. Share this code with someone who wants
          to pay you. When they redeem this code, they will pay you the
          specified amount.
        </p>

        <div className="bg-secondary p-4 rounded-md mb-4">
          <div
            ref={codeRef}
            className="text-center text-xl font-bold text-primary break-all"
          >
            {generatedCode.code}
          </div>
        </div>

        <div className="mb-4">
          <button
            onClick={() => copyToClipboard(generatedCode.code)}
            className="w-full px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
              />
            </svg>
            Copy Code
          </button>
        </div>

        <div className="mb-4 text-sm text-gray-400">
          <p>
            Amount:{" "}
            <span className="font-medium text-white">
              NS {generatedCode.amount}
            </span>
          </p>
          <p>
            Expires:{" "}
            <span className="font-medium text-white">
              {new Date(generatedCode.expiresAt).toLocaleDateString()}
            </span>
          </p>
          <p>
            Max Uses:{" "}
            <span className="font-medium text-white">
              {generatedCode.maxUses || 1}
            </span>
          </p>
        </div>

        <div className="mb-4 p-3 bg-secondary rounded-md text-sm text-gray-300">
          <p className="flex items-start">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2 flex-shrink-0 text-primary"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span>
              If you lose this code, you can find it again in the{" "}
              <strong>Manage Pay-Codes</strong> tab.
            </span>
          </p>
        </div>

        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};
