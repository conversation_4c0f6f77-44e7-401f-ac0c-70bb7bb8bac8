# Testing Suite for Event Website

## Overview
This testing suite provides both browser automation (Playwright) and load testing (Artillery.js) to simulate multiple concurrent users and test your application under various load conditions.

## Quick Start

### Setup (First Time Only)
```bash
# Create test users in database (run once)
pnpm test:setup
```

### Run Playwright E2E Tests
```bash
# Run all browser automation tests
pnpm test:e2e

# Run specific test file
pnpm test:e2e tests/e2e/concurrent-users.spec.ts

# Run tests in headed mode (visible browser)
pnpm test:e2e --headed

# Run tests with debugging
pnpm test:e2e --debug
```

### Run Artillery Load Tests
```bash
# Basic load test (gradual ramp up)
pnpm test:load

# Concurrent users test (30-40 users, peaks at 100)
pnpm test:concurrent

# Stress test (find breaking point)
artillery run tests/load/stress-test.yml

# Generate detailed report
pnpm test:load-report
```

## Test Scenarios

### Playwright Browser Tests

**concurrent-users.spec.ts**
- Simulates 5 concurrent users browsing different sections
- Tests banking operations under concurrent load
- Shopping cart concurrency with ticket holds
- Real-time SSE connection testing

**user-workflows.spec.ts**  
- Complete user registration and login flows
- Banking operations (balance checks, pay codes, transactions)
- Shopping cart and checkout processes
- Event registration workflows
- Volunteer system interactions
- Real-time notification testing

### Artillery Load Tests

**basic-load.yml**
- 3-phase load test: 5→10→20 concurrent users
- Tests homepage browsing, banking, shopping, and auth flows
- 60-120 second test duration
- Realistic user think times

**concurrent-users.yml**
- Targets your specific user loads: 30-40 concurrent, peaks at 100
- 4-phase test with gradual ramp up and cool down
- Mixed scenarios: browsing, shopping, real-time features, admin operations
- Realistic user journeys with think times

**stress-test.yml**
- Aggressive stress test: 50→100→200→300 concurrent users
- Finds the breaking point of your application
- Rapid-fire requests and parallel API calls
- Database-intensive operations

## Key Features Tested

### Performance Critical Areas
- **SSE Connections**: Real-time notifications under load
- **Database Operations**: Banking transactions, user lookups
- **Cart System**: Ticket holds and concurrent shopping
- **Authentication**: Login/logout under concurrent access
- **Image Uploads**: Avatar and content image processing

### Load Testing Patterns
- **Gradual Ramp Up**: Simulates realistic user growth
- **Burst Testing**: Tests peak capacity (your 100-user spikes)
- **Sustained Load**: Tests stability under continuous load
- **Mixed Workloads**: Different user behavior patterns

## Monitoring During Tests

### Key Metrics to Watch
- **Response Times**: API endpoint latency
- **Error Rates**: 4xx/5xx HTTP errors
- **Database Performance**: Query execution times
- **Memory Usage**: Server and database memory
- **SSE Connections**: Active real-time connections

### Artillery Reports
Artillery generates detailed reports showing:
- Request rates and response times
- Error rates by endpoint
- Latency percentiles (p50, p95, p99)
- Concurrent user metrics

## Customization

### Adjusting Test Parameters
Edit the YAML config files to modify:
- `arrivalRate`: Users per second
- `duration`: Test phase duration
- `target`: Your server URL
- `think`: User pause times between actions

### Adding Test Scenarios
Create new scenarios in the YAML files:
```yaml
scenarios:
  - name: "Your Custom Test"
    weight: 50
    flow:
      - get:
          url: "/your-endpoint"
      - think: 2
```

### Custom Playwright Tests
Add new test files in `tests/e2e/`:
- Follow the existing pattern
- Use `data-testid` attributes for reliable selectors  
- Test critical user paths specific to your application

## Troubleshooting

### Common Issues
- **Port conflicts**: Ensure dev server runs on 192.168.1.87:3000
- **Test data**: Some tests may need existing users/products
- **Rate limiting**: Adjust test rates if hitting API limits
- **Browser crashes**: Reduce concurrent browser instances

### Performance Bottlenecks
Use test results to identify:
- Slow database queries
- Memory leaks in SSE connections
- Inefficient API endpoints
- Frontend rendering issues under load

This testing suite will help you verify your application can handle the load you described and identify any performance bottlenecks before they affect real users.