"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import fetchClient from "@/lib/fetchClient";

// Types
export interface VolunteerShift {
  id: string;
  title: string;
  description: string | null;
  startTime: string;
  endTime: string;
  location: string | null;
  maxVolunteers: number;
  eventId: string;
  categoryId: string;
  isAutomated: boolean;
  createdAt: string;
  updatedAt: string;
  stats?: {
    totalAssignments: number;
    completedAssignments: number;
  };
}

export interface CreateShiftData {
  title: string;
  description?: string;
  startTime: string; // ISO date string
  endTime: string; // ISO date string
  location?: string;
  maxVolunteers?: number;
}

export interface UpdateShiftData {
  title: string;
  description?: string;
  startTime: string; // ISO date string
  endTime: string; // ISO date string
  location?: string;
  maxVolunteers?: number;
}

// Query keys
export const volunteerShiftQueryKeys = {
  shifts: "volunteerShifts",
  shiftsByCategory: (categoryId: string) => ["volunteerShifts", categoryId],
  shiftsByEvent: (eventId: string) => ["volunteerShiftsByEvent", eventId],
  shiftsByEventAndCategory: (eventId: string, categoryId: string) => [
    "volunteerShifts",
    eventId,
    categoryId,
  ],
  shift: (id: string) => ["volunteerShift", id],
};

/**
 * Hook to fetch shifts for a specific category
 */
export function useVolunteerShiftsByCategory(categoryId: string | null) {
  return useQuery<VolunteerShift[]>({
    queryKey: categoryId
      ? volunteerShiftQueryKeys.shiftsByCategory(categoryId)
      : [],
    queryFn: async () => {
      if (!categoryId) return [];
      return fetchClient.get(`/api/volunteer/categories/${categoryId}/shifts`);
    },
    enabled: !!categoryId, // Only run query if categoryId is provided
  });
}

/**
 * Hook to fetch shifts for a specific event and category
 */
export function useVolunteerShiftsByEventAndCategory(
  eventId: string | null,
  categoryId: string | null,
) {
  return useQuery<VolunteerShift[]>({
    queryKey:
      eventId && categoryId
        ? volunteerShiftQueryKeys.shiftsByEventAndCategory(eventId, categoryId)
        : [],
    queryFn: async () => {
      if (!eventId || !categoryId) return [];
      return fetchClient.get(
        `/api/volunteer/events/${eventId}/categories/${categoryId}/shifts`,
      );
    },
    enabled: !!eventId && !!categoryId, // Only run query if both IDs are provided
  });
}

/**
 * Hook to fetch a specific shift
 */
export function useVolunteerShift(id: string | null) {
  return useQuery<VolunteerShift | null>({
    queryKey: id ? volunteerShiftQueryKeys.shift(id) : [],
    queryFn: async () => {
      if (!id) return null;
      return fetchClient.get(`/api/volunteer/shifts/${id}`);
    },
    enabled: !!id, // Only run query if id is provided
  });
}

/**
 * Hook to create a new shift or multiple shifts
 */
export function useCreateVolunteerShift(
  eventId: string | null,
  categoryId: string | null,
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateShiftData | CreateShiftData[]) => {
      if (!eventId) throw new Error("Event ID is required");
      if (!categoryId) throw new Error("Category ID is required");

      return fetchClient.post(
        `/api/volunteer/events/${eventId}/categories/${categoryId}/shifts`,
        data,
      );
    },
    onSuccess: () => {
      // Invalidate shifts queries to refetch the updated lists
      if (categoryId) {
        queryClient.invalidateQueries({
          queryKey: volunteerShiftQueryKeys.shiftsByCategory(categoryId),
        });
      }

      if (eventId && categoryId) {
        queryClient.invalidateQueries({
          queryKey: volunteerShiftQueryKeys.shiftsByEventAndCategory(
            eventId,
            categoryId,
          ),
        });
      }
    },
  });
}

/**
 * Hook to update an existing shift
 */
export function useUpdateVolunteerShift(
  id: string | null,
  eventId: string | null,
  categoryId: string | null,
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateShiftData) => {
      if (!id) throw new Error("Shift ID is required");

      return fetchClient.put(`/api/volunteer/shifts/${id}`, data);
    },
    onSuccess: () => {
      // Invalidate specific shift query
      if (id) {
        queryClient.invalidateQueries({
          queryKey: volunteerShiftQueryKeys.shift(id),
        });
      }

      // Invalidate shifts by category query
      if (categoryId) {
        queryClient.invalidateQueries({
          queryKey: volunteerShiftQueryKeys.shiftsByCategory(categoryId),
        });
      }

      // Invalidate shifts by event and category query
      if (eventId && categoryId) {
        queryClient.invalidateQueries({
          queryKey: volunteerShiftQueryKeys.shiftsByEventAndCategory(
            eventId,
            categoryId,
          ),
        });
      }
    },
  });
}

/**
 * Hook to delete a shift
 */
export function useDeleteVolunteerShift(
  eventId: string | null,
  categoryId: string | null,
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      if (!id) throw new Error("Shift ID is required");

      return fetchClient.delete(`/api/volunteer/shifts/${id}`);
    },
    onSuccess: () => {
      // Invalidate shifts by category query
      if (categoryId) {
        queryClient.invalidateQueries({
          queryKey: volunteerShiftQueryKeys.shiftsByCategory(categoryId),
        });
      }

      // Invalidate shifts by event and category query
      if (eventId && categoryId) {
        queryClient.invalidateQueries({
          queryKey: volunteerShiftQueryKeys.shiftsByEventAndCategory(
            eventId,
            categoryId,
          ),
        });
      }
    },
  });
}

export default {
  useVolunteerShiftsByCategory,
  useVolunteerShiftsByEventAndCategory,
  useVolunteerShift,
  useCreateVolunteerShift,
  useUpdateVolunteerShift,
  useDeleteVolunteerShift,
};
