# Color Theme and Mobile-First UI Elements

## Overview
The application uses a comprehensive custom color theme system with CSS variables and mobile-first responsive design patterns optimized for the "Bank of Styx" pirate theme.

## Color Theme System

### CSS Variable Architecture
All colors are defined as CSS variables for dynamic theming:

```css
:root {
  /* Primary Colors */
  --color-primary: #5865F2; /* Discord blue */
  --color-primary-dark: #4752C4;
  --color-primary-light: #7984F5;

  /* Secondary Colors */
  --color-secondary: #2C2F33; /* Discord dark (nav, input backgrounds) */
  --color-secondary-dark: #23272A; /* Near black (page background) */
  --color-secondary-light: #36393F; /* Lighter dark (card backgrounds) */

  /* Accent Colors */
  --color-accent: #ED4245; /* Discord red */
  --color-accent-dark: #D03A3D;
  --color-accent-light: #F25D60;

  /* Status Colors */
  --color-success: #43B581;
  --color-warning: #FAA61A;
  --color-error: #F04747;
  --color-info: #5865F2;

  /* Text Colors */
  --color-text-primary: #FFFFFF;
  --color-text-secondary: #99AAB5;
  --color-text-muted: #72767d;
  --color-text-disabled: rgba(114, 118, 125, 0.75);

  /* Background Colors */
  --color-bg-page: #23272A;
  --color-bg-card: #36393F;
  --color-bg-input: #2C2F33;
  --color-bg-hover: #474B52;
}
```

### Theme Management System

#### Theme Context (`ColorThemeContext.tsx`)
```typescript
interface ColorThemeContextType {
  isDarkMode: boolean;
  toggleTheme: () => void;
  applyTheme: (theme: Record<string, string>) => void;
}

// Apply theme by setting CSS variables
const applyTheme = (themeValues: Record<string, string>) => {
  Object.entries(themeValues).forEach(([variable, value]) => {
    document.documentElement.style.setProperty(variable, value);
  });
  localStorage.setItem("bankOfStyxColorTheme", JSON.stringify(themeValues));
};
```

#### Theme Persistence
- **Storage**: localStorage with key `"bankOfStyxColorTheme"`
- **Cross-tab Sync**: Storage event listeners for theme changes
- **SSR Support**: ColorThemeScript component prevents hydration mismatch

### Predefined Theme Presets
The system includes 8 carefully crafted theme presets:

#### 1. Discord Dark (Default Dark)
- **Primary**: #5865F2 (Discord blue)
- **Background**: #23272A (Near black)
- **Cards**: #36393F (Dark gray)

#### 2. Pirate Gold
- **Primary**: #FFD700 (Gold)
- **Background**: #0F0F0F (Black)
- **Accent**: #C41E3A (Crimson)
- **Theme**: Dark with gold accents for pirate aesthetic

#### 3. Discord Light
- **Primary**: #4752C4 (Darker blue for contrast)
- **Background**: #F5F7FA (Light gray)
- **Cards**: #FFFFFF (White)

#### 4. Crimson Gold, Midnight Blue, Emerald Dark, Seafoam, Royal Purple
- Each with unique color combinations optimized for accessibility

### Color Picker Component
Advanced color picker with palette and hex input:

```typescript
// Predefined color palette with Discord-inspired colors
const colorPalette = [
  "#5865F2", "#4752C4", "#7984F5", // Primary blues
  "#2C2F33", "#23272A", "#36393F", // Secondary grays
  "#ED4245", "#D03A3D", "#F25D60", // Accent reds
  "#43B581", "#FAA61A", "#F04747", // Status colors
  // ... additional colors
];
```

Features:
- **Dual Input**: Color picker and hex text input
- **Palette Selection**: Quick color selection from predefined palette
- **Real-time Preview**: Live color preview during selection
- **Validation**: Hex color format validation

## Tailwind CSS Integration

### Custom Color Mapping
```javascript
// tailwind.config.js
colors: {
  primary: {
    DEFAULT: "var(--color-primary)",
    dark: "var(--color-primary-dark)",
    light: "var(--color-primary-light)"
  },
  secondary: {
    DEFAULT: "var(--color-secondary)",
    dark: "var(--color-secondary-dark)",
    light: "var(--color-secondary-light)"
  },
  // Status colors directly mapped
  success: "var(--color-success)",
  warning: "var(--color-warning)",
  error: "var(--color-error)",
  info: "var(--color-info)",
}
```

### Custom Background and Text Colors
```javascript
backgroundColor: {
  'card': "var(--color-bg-card)",
  'input': "var(--color-bg-input)",
},
textColor: {
  'disabled': "var(--color-text-disabled)",
}
```

## Mobile-First Responsive Design

### Breakpoint Strategy
The application uses a mobile-first approach with specific breakpoints:

- **Mobile**: `< 768px` (default, no prefix)
- **Medium**: `md: 768px+`
- **Large**: `lg: 1024px+`
- **Custom**: `min-[896px]:` for specific layouts

### Responsive Layout Patterns

#### Grid Systems
```typescript
// Adaptive grid layouts
className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"

// Custom breakpoint usage
className="hidden min-[896px]:grid min-[896px]:grid-cols-2 gap-6"
```

#### Component Visibility
```typescript
// Desktop-only hero
<div className="hidden md:block">
  <Hero />
</div>

// Mobile-only hero
<div className="md:hidden container mx-auto px-2 mb-4">
  <MobileHero />
</div>
```

### Spacing System

#### Consistent Spacing Scale
- **Gap**: `gap-2` (8px), `gap-3` (12px), `gap-4` (16px), `gap-6` (24px)
- **Padding**: `p-2` (8px), `p-3` (12px), `p-4` (16px), `p-6` (24px)
- **Margin**: `m-2`, `m-4`, `m-6`, `m-8` for consistent vertical rhythm

#### Responsive Spacing
```typescript
// Responsive padding
className="p-2 sm:p-3 md:p-4"

// Responsive gaps
className="gap-2 md:gap-3 lg:gap-4"
```

### Navigation Patterns

#### Mobile Navigation
```typescript
// Fixed mobile nav with overlay
<nav className={`
  bg-secondary flex-shrink-0 shadow-md
  md:w-40 md:sticky md:top-12 md:self-start md:block md:z-10
  ${isMobileNavOpen 
    ? "block fixed z-50 h-full right-0 top-0 w-32" 
    : "hidden"
  }
`}>
```

#### Responsive Spacing in Navigation
```typescript
// Adaptive spacing
<div className="p-3 md:p-3 space-y-2 md:space-y-3">
  <ul className="space-y-2 md:space-y-4">
```

### Card Component System

#### Flexible Card Props
```typescript
interface CardProps {
  padding?: "none" | "sm" | "md" | "lg";
  shadow?: "none" | "sm" | "md" | "lg"; 
  border?: boolean;
}

// Padding mapping
const paddingClasses = {
  none: "",
  sm: "p-3",
  md: "p-4", 
  lg: "p-6",
};
```

#### Card Header with Actions
```typescript
{title && (
  <div className="px-4 py-3 border-b border-gray-600 bg-secondary flex justify-between items-center overflow-hidden">
    <h3 className="text-lg font-medium">{title}</h3>
    {headerAction && <div>{headerAction}</div>}
  </div>
)}
```

### Quick Actions Component Pattern

#### Responsive Grid Layout
```typescript
// Large screens: 2x2 grid (4 actions)
<div className="hidden min-[896px]:grid min-[896px]:grid-cols-2 gap-3">

// Medium screens: 4x2 grid (8 actions)  
<div className="hidden md:grid md:grid-cols-4 min-[896px]:hidden gap-3">

// Mobile: 2x3 grid (6 actions)
<div className="grid grid-cols-2 gap-2 md:hidden">
```

#### Responsive Button Sizing
```typescript
// Icon sizing
className="w-5 h-5 sm:w-6 sm:h-6"

// Text sizing  
className="text-xs sm:text-sm"

// Padding
className="p-2 sm:p-3"
```

### Custom Scrollbar Styling
```css
.scrollbar-dark::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.scrollbar-dark::-webkit-scrollbar-track {
  background: var(--color-secondary);
  border-radius: 4px;
}

.scrollbar-dark::-webkit-scrollbar-thumb {
  background-color: var(--color-border-dark);
  border-radius: 4px;
  border: 2px solid var(--color-secondary);
}
```

## Rich Text Editor Theming

### Theme-Independent Editor Styling
```css
/* Editor-specific colors (theme-independent) */
--editor-bg-toolbar: #2C2F33;
--editor-bg-content: #23272A;
--editor-border: #4B5563;
--editor-border-active: #5865F2;
```

### Responsive Editor Adjustments
```css
@media (max-width: 640px) {
  .styx-editor .ql-toolbar.ql-snow {
    padding: 4px;
    gap: 2px;
  }
  
  .styx-editor .ql-toolbar.ql-snow button {
    height: 28px;
    width: 28px;
    margin: 0 1px;
  }
}
```

## Key UI Patterns

### 1. Container Patterns
```typescript
// Standard container with responsive padding
<div className="container mx-auto px-2 py-4 md:py-8 lg:py-12">

// Content wrapper with max width
<div className="max-w-4xl mx-auto">
```

### 2. Button Patterns
```typescript
// Primary button
className="bg-primary hover:bg-primary-dark text-white font-bold py-2 px-6 rounded-full"

// Secondary button with border
className="bg-transparent hover:bg-white/10 text-white font-bold py-2 px-6 rounded-full border border-white"
```

### 3. Loading States
```typescript
// Spinner component with size variants
const LoadingSpinner = ({ size = "md" }: { size?: "sm" | "md" | "lg" }) => {
  const sizeClasses = {
    sm: "h-6 w-6",
    md: "h-8 w-8", 
    lg: "h-12 w-12",
  };
  
  return (
    <div className={`animate-spin rounded-full ${sizeClasses[size]} border-t-2 border-b-2 border-primary`}>
  );
};
```

### 4. Status Indicators
```typescript
// Success/error states with theme colors
<div className="bg-red-900/30 border border-red-700 text-white p-4 rounded-md">
<div className="bg-secondary-light p-4 rounded-md">
```

## Important Files
- `src/contexts/ColorThemeContext.tsx` - Theme management context
- `src/components/settings/ColorPicker.tsx` - Color selection component
- `src/components/settings/ThemePresets.tsx` - Predefined theme presets
- `src/app/globals.css` - CSS variables and base styles
- `web/packages/config/tailwind.config.js` - Tailwind configuration
- `src/app/settings/colors/ColorThemeScript.tsx` - SSR theme loading