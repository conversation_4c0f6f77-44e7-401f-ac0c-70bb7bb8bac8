import { NextRequest, NextResponse } from "next/server";
import { stripe } from "@/lib/stripe-server";
import { prisma } from "@/lib/prisma";
import { headers } from "next/headers";
import { convertEventCapacityHoldsToSold } from "@/lib/event-capacity-system";

export async function POST(req: NextRequest) {
  const body = await req.text();
  const signature = headers().get("stripe-signature");

  if (!signature) {
    return NextResponse.json(
      { error: "Missing stripe-signature header" },
      { status: 400 },
    );
  }

  let event;
  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!,
    );
  } catch (err: any) {
    console.error(`Webhook signature verification failed: ${err.message}`);
    return NextResponse.json({ error: err.message }, { status: 400 });
  }

  // Handle the event
  switch (event.type) {
    case "payment_intent.succeeded":
      await handlePaymentSucceeded(event.data.object);
      break;
    case "payment_intent.payment_failed":
      await handlePaymentFailed(event.data.object);
      break;
    case "charge.succeeded":
      await handleChargeSucceeded(event.data.object);
      break;
    case "charge.updated":
      await handleChargeUpdated(event.data.object);
      break;
    default:
  }

  return NextResponse.json({ received: true });
}

async function handlePaymentSucceeded(paymentIntent: any) {
  const orderId = paymentIntent.metadata.orderId;

  try {
    // Update order status
    await prisma.order.update({
      where: { id: orderId },
      data: {
        status: "paid",
        updatedAt: new Date(),
      },
    });

    // Convert held tickets to sold
    await convertHeldTicketsToSold(orderId, paymentIntent.metadata.cartId);

    // Update event capacity if applicable
    await updateEventCapacity(orderId);

    // Clear the user's cart
    const cartId = paymentIntent.metadata.cartId;
    if (cartId) {
      await prisma.cartItem.deleteMany({
        where: { cartId },
      });
    }

  } catch (error) {
    console.error(
      `Error handling payment success for order ${orderId}:`,
      error,
    );
  }
}

async function handlePaymentFailed(paymentIntent: any) {
  const orderId = paymentIntent.metadata.orderId;

  try {
    // Update order status
    await prisma.order.update({
      where: { id: orderId },
      data: {
        status: "failed",
        updatedAt: new Date(),
      },
    });

  } catch (error) {
    console.error(
      `Error handling payment failure for order ${orderId}:`,
      error,
    );
  }
}

async function updateEventCapacity(orderId: string) {
  const order = await prisma.order.findUnique({
    where: { id: orderId },
    include: {
      items: {
        include: {
          product: true,
        },
      },
    },
  });

  if (!order) return;

  // Update product inventory for all items
  for (const item of order.items) {
    if (item.product.inventory !== null) {
      await prisma.product.update({
        where: { id: item.productId },
        data: {
          inventory: {
            decrement: item.quantity,
          },
        },
      });
    }
  }

}

async function handleChargeSucceeded(charge: any) {

  // The charge succeeded event provides additional details about the payment
  // This can be used for detailed payment tracking or reconciliation
  if (charge.metadata?.orderId) {
    try {
      // You could add additional charge tracking here if needed
      // For example, updating order with charge details
      await prisma.order.update({
        where: { id: charge.metadata.orderId },
        data: {
          // Add any charge-specific fields if your schema supports them
          updatedAt: new Date(),
        },
      });

    } catch (error) {
      console.error(`Error tracking charge ${charge.id}:`, error);
    }
  }
}

async function handleChargeUpdated(charge: any) {

  // Handle charge updates - this could include disputes, refunds, etc.
  if (charge.metadata?.orderId) {
    try {
      // Log charge status changes for audit purposes

      // If the charge was disputed or failed after initially succeeding
      if (charge.status === "failed" || charge.dispute) {
        // You might want to update the order status or handle disputes
      }
    } catch (error) {
      console.error(`Error handling charge update ${charge.id}:`, error);
    }
  }
}

async function convertHeldTicketsToSold(orderId: string, cartId?: string) {
  if (!cartId) {
    return { converted: 0, eventConverted: 0 };
  }

  try {
    return await prisma.$transaction(async (tx) => {
      let totalConverted = 0;
      let eventConverted = 0;

      // First, handle event capacity holds
      const cartItemsWithEventHolds = await tx.cartItem.findMany({
        where: {
          cartId,
          eventCapacityHold: {
            isNot: null,
          },
        },
        include: {
          eventCapacityHold: {
            include: {
              event: true
            }
          },
          product: true,
        },
      });

      // Group by event for batch processing
      const eventGroups = new Map<string, typeof cartItemsWithEventHolds>();
      for (const item of cartItemsWithEventHolds) {
        if (item.eventCapacityHold?.eventId) {
          const eventId = item.eventCapacityHold.eventId;
          if (!eventGroups.has(eventId)) {
            eventGroups.set(eventId, []);
          }
          eventGroups.get(eventId)!.push(item);
        }
      }

      // Convert event capacity holds to sold tickets
      for (const [eventId, items] of eventGroups) {
        const cart = await tx.cart.findUnique({
          where: { id: cartId },
          select: { userId: true }
        });

        if (cart) {
          const result = await convertEventCapacityHoldsToSold(eventId, cart.userId, cartId);
          eventConverted += result.converted;
        }
      }

      // Then, handle individual ticket holds (existing logic)
      const cartItemsWithTicketHolds = await tx.cartItem.findMany({
        where: {
          cartId,
          ticketHold: {
            isNot: null,
          },
        },
        include: {
          ticketHold: true,
        },
      });

      const holdIds = cartItemsWithTicketHolds
        .map((item) => item.ticketHold?.id)
        .filter((id) => id !== undefined) as string[];

      if (holdIds.length > 0) {
        // Convert individual tickets from HELD to SOLD
        const result = await tx.ticket.updateMany({
          where: {
            holdId: {
              in: holdIds,
            },
            status: "HELD",
          },
          data: {
            status: "SOLD",
            holdId: null,
            orderId,
          },
        });

        totalConverted += result.count;
      }

      totalConverted += eventConverted;

      
      return { converted: totalConverted, eventConverted };
    });
  } catch (error) {
    console.error(
      `Error converting held tickets to sold for order ${orderId}:`,
      error,
    );
    return { converted: 0, eventConverted: 0 };
  }
}
