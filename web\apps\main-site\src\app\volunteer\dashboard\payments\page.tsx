"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@bank-of-styx/ui";
import {
  VolunteerDashboardLayout,
  PaymentFilters,
  PaymentList,
  PaymentHistory,
  PaymentConfirmationModal,
  Tabs,
} from "@/components/volunteer";
import {
  useVolunteerPayments,
  useVolunteerPaymentHistory,
  useProcessVolunteerPayment,
  useBulkProcessVolunteerPayments,
  PaymentFilters as FilterType,
  VolunteerPayment,
} from "@/hooks/useVolunteerPayments";
import { toast } from "react-hot-toast";

export default function PaymentsPage() {
  const { user, isLoading } = useAuth();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("pending");
  const [filters, setFilters] = useState<FilterType>({ status: "pending" });
  const [historyFilters, setHistoryFilters] = useState<FilterType>({
    status: "paid",
  });
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [selectedPayments, setSelectedPayments] = useState<VolunteerPayment[]>(
    [],
  );
  const [isBulkProcessing, setIsBulkProcessing] = useState(false);

  // Fetch payments based on filters
  const {
    data: pendingPayments = [],
    isLoading: isLoadingPayments,
    refetch: refetchPayments,
  } = useVolunteerPayments(filters);

  // Fetch payment history
  const {
    data: paymentHistory = [],
    isLoading: isLoadingHistory,
    refetch: refetchHistory,
  } = useVolunteerPaymentHistory(historyFilters);

  // Process payment hooks
  const processPayment = useProcessVolunteerPayment();
  const bulkProcessPayments = useBulkProcessVolunteerPayments();

  // Check if user is authorized to access this page
  useEffect(() => {
    if (!isLoading) {
      if (!user || !user.roles?.volunteerCoordinator) {
        router.push("/");
      } else {
        setIsAuthorized(true);
      }
    }
  }, [user, isLoading, router]);

  // Handle filter changes for pending payments
  const handleFilterChange = (newFilters: FilterType) => {
    setFilters({ ...newFilters, status: "pending" });
  };

  // Handle filter changes for payment history
  const handleHistoryFilterChange = (newFilters: FilterType) => {
    setHistoryFilters({ ...newFilters, status: "paid" });
  };

  // Handle tab change
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  // Handle single payment processing
  const handleProcessPayment = (paymentId: string) => {
    const payment = pendingPayments.find((p) => p.id === paymentId);
    if (payment) {
      setSelectedPayments([payment]);
      setIsBulkProcessing(false);
      setShowConfirmation(true);
    }
  };

  // Handle bulk payment processing
  const handleBulkProcess = (paymentIds: string[]) => {
    const paymentsToProcess = pendingPayments.filter((p) =>
      paymentIds.includes(p.id),
    );
    if (paymentsToProcess.length > 0) {
      setSelectedPayments(paymentsToProcess);
      setIsBulkProcessing(true);
      setShowConfirmation(true);
    }
  };

  // Handle confirmation modal close
  const handleCloseConfirmation = () => {
    setShowConfirmation(false);
    setSelectedPayments([]);
  };

  // Handle payment confirmation
  const handleConfirmPayment = async (note: string) => {
    try {
      if (isBulkProcessing) {
        // Process multiple payments
        const result = await bulkProcessPayments.mutateAsync({
          paymentIds: selectedPayments.map((p) => p.id),
          note,
        });

        toast.success(
          `Successfully processed ${selectedPayments.length} payments`,
        );
      } else if (selectedPayments.length === 1) {
        // Process single payment
        await processPayment.mutateAsync({
          paymentId: selectedPayments[0].id,
          note,
        });

        toast.success(
          `Successfully processed payment for ${selectedPayments[0].user.displayName}`,
        );
      }

      // Close modal and refetch data
      setShowConfirmation(false);
      setSelectedPayments([]);
      refetchPayments();
      refetchHistory();
    } catch (error) {
      console.error("Error processing payment:", error);
      toast.error("Failed to process payment. Please try again.");
    }
  };

  // Show loading state while checking authorization
  if (isLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="text-white text-lg mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <VolunteerDashboardLayout>
      <div>
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-white mb-2">
            Payment Processing
          </h1>
          <p className="text-gray-400">
            Process payments for volunteer shifts and view payment history.
          </p>
        </div>

        {/* Tabs for Pending Payments and Payment History */}
        <Tabs
          tabs={[
            { id: "pending", label: "Pending Payments" },
            { id: "history", label: "Payment History" },
          ]}
          activeTab={activeTab}
          onChange={handleTabChange}
          className="mb-6"
        />

        {activeTab === "pending" ? (
          <>
            {/* Filters for Pending Payments */}
            <PaymentFilters
              onFilterChange={handleFilterChange}
              initialFilters={filters}
            />

            {/* Pending Payments List */}
            <PaymentList
              payments={pendingPayments}
              isLoading={isLoadingPayments}
              onProcessPayment={handleProcessPayment}
              onBulkProcess={handleBulkProcess}
            />
          </>
        ) : (
          <>
            {/* Filters for Payment History */}
            <PaymentFilters
              onFilterChange={handleHistoryFilterChange}
              initialFilters={historyFilters}
            />

            {/* Payment History List */}
            <PaymentHistory
              payments={paymentHistory}
              isLoading={isLoadingHistory}
            />
          </>
        )}

        {/* Payment Confirmation Modal */}
        <PaymentConfirmationModal
          isOpen={showConfirmation}
          onClose={handleCloseConfirmation}
          onConfirm={handleConfirmPayment}
          payments={selectedPayments}
          isBulk={isBulkProcessing}
        />
      </div>
    </VolunteerDashboardLayout>
  );
}
