"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  getArticles,
  getArticleById,
  createArticle,
  updateArticle,
  deleteArticle,
  toggleArticleStatus,
  toggleArticleFeatured,
  getCategories,
  createCategory,
  updateCategory,
  deleteCategory,
  ArticleFilters,
  CreateArticleData,
  UpdateArticleData,
} from "../services/newsService";

// Query keys
export const queryKeys = {
  articles: "articles",
  article: (id: string) => ["article", id],
  categories: "categories",
};

// Articles hooks

/**
 * Hook to fetch articles with filtering and pagination
 */
export function useArticles(filters: ArticleFilters = {}) {
  return useQuery({
    queryKey: [queryKeys.articles, filters],
    queryFn: () => getArticles(filters),
  });
}

/**
 * Hook to fetch a single article by ID
 */
export function useArticle(id: string) {
  return useQuery({
    queryKey: queryKeys.article(id),
    queryFn: () => getArticleById(id),
    enabled: !!id, // Only run query if ID is provided
  });
}

/**
 * Hook to create a new article
 */
export function useCreateArticle() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateArticleData) => createArticle(data),
    onSuccess: () => {
      // Invalidate articles query to refetch the updated list
      queryClient.invalidateQueries({ queryKey: [queryKeys.articles] });
    },
  });
}

/**
 * Hook to update an existing article
 */
export function useUpdateArticle(id: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateArticleData) => updateArticle(id, data),
    onSuccess: (updatedArticle) => {
      // Update the article in the cache
      queryClient.setQueryData(queryKeys.article(id), updatedArticle);

      // Invalidate articles query to refetch the updated list
      queryClient.invalidateQueries({ queryKey: [queryKeys.articles] });
    },
  });
}

/**
 * Hook to delete an article
 */
export function useDeleteArticle() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteArticle(id),
    onSuccess: (_data, id) => {
      // Invalidate and remove article from cache
      queryClient.removeQueries({ queryKey: queryKeys.article(id) });

      // Invalidate articles query to refetch the updated list
      queryClient.invalidateQueries({ queryKey: [queryKeys.articles] });
    },
  });
}

/**
 * Hook to toggle article status (published/paused)
 */
export function useToggleArticleStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => toggleArticleStatus(id),
    onSuccess: (updatedArticle) => {
      // Update the article in the cache
      queryClient.setQueryData(
        queryKeys.article(updatedArticle.id),
        updatedArticle,
      );

      // Invalidate articles query to refetch the updated list
      queryClient.invalidateQueries({ queryKey: [queryKeys.articles] });
    },
  });
}

/**
 * Hook to toggle article featured status
 */
export function useToggleArticleFeatured() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => toggleArticleFeatured(id),
    onSuccess: (updatedArticle) => {
      // Update the article in the cache
      queryClient.setQueryData(
        queryKeys.article(updatedArticle.id),
        updatedArticle,
      );

      // Invalidate articles query to refetch the updated list
      queryClient.invalidateQueries({ queryKey: [queryKeys.articles] });
    },
  });
}

// Categories hooks

/**
 * Hook to fetch all categories
 */
export function useCategories() {
  return useQuery({
    queryKey: [queryKeys.categories],
    queryFn: async () => {
      try {
        const data = await getCategories();
        // Only log in development mode to reduce console noise
        if (process.env.NODE_ENV === "development") {
          console.log("Categories fetched successfully:", data);
        }
        return data;
      } catch (error) {
        console.error("Error in useCategories hook:", error);
        throw error;
      }
    },
  });
}

/**
 * Hook to create a new category
 */
export function useCreateCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { name: string; description?: string }) =>
      createCategory(data),
    onSuccess: () => {
      // Invalidate categories query to refetch the updated list
      queryClient.invalidateQueries({ queryKey: [queryKeys.categories] });
    },
  });
}

/**
 * Hook to update an existing category
 */
export function useUpdateCategory(id: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { name: string; description?: string }) =>
      updateCategory(id, data),
    onSuccess: () => {
      // Invalidate categories query to refetch the updated list
      queryClient.invalidateQueries({ queryKey: [queryKeys.categories] });
    },
  });
}

/**
 * Hook to delete a category
 */
export function useDeleteCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteCategory(id),
    onSuccess: () => {
      // Invalidate categories query to refetch the updated list
      queryClient.invalidateQueries({ queryKey: [queryKeys.categories] });
    },
  });
}
