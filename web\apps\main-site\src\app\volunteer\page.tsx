"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import {
  VolunteerEventsList,
  VolunteerCategoriesList,
  VolunteerShiftsList,
  UserVolunteerQuickActions,
} from "@/components/volunteer/public";

export default function VolunteerPage() {
  const [selectedEventId, setSelectedEventId] = useState<string | null>(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(
    null,
  );
  const { user, isAuthenticated, isLoading, openAuthModal } = useAuth();

  // Reset category when event changes
  useEffect(() => {
    setSelectedCategoryId(null);
  }, [selectedEventId]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="text-white text-lg mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  // If not authenticated, show login prompt
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-secondary-dark">
        <div className="container px-4 py-8 mx-auto">
          <div className="bg-secondary-light rounded-lg shadow-md p-6 border border-gray-600 text-center">
            <h1 className="text-3xl font-bold text-white mb-4">
              Volunteer Opportunities
            </h1>
            <p className="text-gray-400 mb-6">
              Please log in to view volunteer opportunities and sign up for
              shifts.
            </p>
            <button
              onClick={openAuthModal}
              className="px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-md"
            >
              Log In
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-secondary-dark min-h-screen">
      <div className="container px-4 py-8 mx-auto">
        {/* Page Header */}
        <h1 className="text-3xl font-bold text-white mb-6">
          Volunteer Opportunities
        </h1>

        {/* Quick Actions for authenticated users with upcoming shifts */}
        {isAuthenticated && user && (
          <UserVolunteerQuickActions userId={user.id} />
        )}

        {/* Main Content */}
        <div className="bg-secondary-light rounded-lg shadow-md p-6 border border-gray-600">
          {!selectedEventId ? (
            /* Events List */
            <VolunteerEventsList onEventSelect={setSelectedEventId} />
          ) : !selectedCategoryId ? (
            /* Categories List for selected event */
            <VolunteerCategoriesList
              eventId={selectedEventId}
              onCategorySelect={setSelectedCategoryId}
              onBack={() => setSelectedEventId(null)}
            />
          ) : (
            /* Shifts List for selected category */
            <VolunteerShiftsList
              eventId={selectedEventId}
              categoryId={selectedCategoryId}
              onBack={() => setSelectedCategoryId(null)}
            />
          )}
        </div>
      </div>
    </div>
  );
}
