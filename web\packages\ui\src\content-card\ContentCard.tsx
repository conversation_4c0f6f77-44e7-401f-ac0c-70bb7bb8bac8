"use client";

import React from "react";

export interface ContentCardProps {
  title: string;
  content: string;
  image?: string;
  date?: string;
  author?: string;
  category?: string;
  onClick?: () => void;
  className?: string;
  footer?: React.ReactNode;
  htmlContent?: boolean; // Add this prop to indicate if content is HTML
}

export const ContentCard: React.FC<ContentCardProps> = ({
  title,
  content,
  image,
  date,
  author,
  category,
  onClick,
  className = "",
  footer,
  htmlContent = false, // Default to false for backward compatibility
}) => {
  return (
    <div
      className={`bg-secondary-light rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 ${className} ${
        onClick ? "cursor-pointer" : ""
      }`}
      onClick={onClick}
    >
      {image && (
        <div className="relative h-fit w-full overflow-hidden">
          <img src={image} alt={title} className="w-full h-full" />
          {category && (
            <span className="absolute top-4 right-4 bg-primary text-xs font-bold px-3 py-1 rounded-full">
              {category}
            </span>
          )}
        </div>
      )}
      <div className="p-3 sm:p-4 md:p-5">
        <h3 className="text-xl font-bold mb-1 md:mb-2">{title}</h3>
        {(date || author) && (
          <div className="flex items-center text-sm text-text-secondary mb-3">
            {date && <span className="mr-3">{date}</span>}
            {author && <span>By {author}</span>}
          </div>
        )}

        {/* Conditionally render as HTML or plain text based on htmlContent prop */}
        {htmlContent ? (
          <div
            className="text-text-secondary mb-2 md:mb-4 ql-content line-clamp-3"
            dangerouslySetInnerHTML={{ __html: content }}
          />
        ) : (
          <p className="text-text-secondary mb-2 md:mb-4 line-clamp-3">
            {content}
          </p>
        )}

        {footer && (
          <div className="mt-4 pt-4 border-t border-gray-600">{footer}</div>
        )}
      </div>
    </div>
  );
};
