"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  getPublicArticles,
  getPublicArticleBySlug,
  getPublicCategories,
  PublicNewsFilters,
} from "../services/publicNewsService";

// Query keys for public news
export const publicNewsQueryKeys = {
  articles: "publicArticles",
  article: (slug: string) => ["publicArticle", slug],
  categories: "publicCategories",
};

/**
 * Hook to fetch public articles with filtering and pagination
 */
export function usePublicArticles(filters: PublicNewsFilters = {}) {
  // Extract individual parameters for consistent query key with useStateBasedNews
  const { page = 1, featured, category, limit = 10, search, sortBy = "publishedAt", order = "desc" } = filters;
  
  return useQuery({
    // Use same query key pattern as useStateBasedNews for cache sharing
    queryKey: ["news", page, featured, category, limit, search, sortBy, order],
    queryFn: () => getPublicArticles(filters),
  });
}

/**
 * Hook to fetch a single public article by slug
 */
export function usePublicArticle(slug: string) {
  return useQuery({
    queryKey: publicNewsQueryKeys.article(slug),
    queryFn: () => getPublicArticleBySlug(slug),
    enabled: !!slug, // Only run query if slug is provided
  });
}

/**
 * Hook to fetch public categories
 */
export function usePublicCategories() {
  return useQuery({
    // Use same query key as useStateBasedCategories for cache sharing
    queryKey: ["categories"],
    queryFn: () => getPublicCategories(),
  });
}
