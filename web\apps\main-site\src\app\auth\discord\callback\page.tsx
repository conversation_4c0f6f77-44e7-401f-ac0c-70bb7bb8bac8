"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "../../../../contexts/AuthContext";

export default function DiscordCallbackPage() {
  const [status, setStatus] = useState<"loading" | "success" | "error">(
    "loading",
  );
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { refreshUserData } = useAuth();

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const token = searchParams.get("token");

        if (!token) {
          setStatus("error");
          setError("No authentication token received");
          return;
        }

        // Store token in localStorage
        localStorage.setItem("auth_token", token);

        // Refresh user data
        await refreshUserData();

        setStatus("success");

        // Redirect after a short delay
        setTimeout(() => {
          router.push("/bank/dashboard");
        }, 1500);
      } catch (err) {
        console.error("Error handling Discord callback:", err);
        setStatus("error");
        setError("Failed to complete authentication");
      }
    };

    handleCallback();
  }, [searchParams, refreshUserData, router]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      {status === "loading" && (
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold mb-2">
            Completing Authentication
          </h2>
          <p className="text-text-secondary">
            Please wait while we finish setting up your account...
          </p>
        </div>
      )}

      {status === "success" && (
        <div className="text-center">
          <div className="bg-green-100 text-green-800 p-4 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-8 w-8"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <h2 className="text-xl font-semibold mb-2">
            Authentication Successful!
          </h2>
          <p className="text-text-secondary">
            You are now signed in with Discord. Redirecting you...
          </p>
        </div>
      )}

      {status === "error" && (
        <div className="text-center">
          <div className="bg-red-100 text-red-800 p-4 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-8 w-8"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </div>
          <h2 className="text-xl font-semibold mb-2">Authentication Failed</h2>
          <p className="text-text-secondary">
            {error || "An unknown error occurred"}
          </p>
          <button
            onClick={() => router.push("/")}
            className="mt-4 px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition-colors"
          >
            Return to Home
          </button>
        </div>
      )}
    </div>
  );
}
