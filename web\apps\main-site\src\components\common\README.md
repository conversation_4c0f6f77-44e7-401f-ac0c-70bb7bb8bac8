# Common Components

Shared utility components used across multiple features and pages throughout the application.

## Components

- **DatabaseImage.tsx** - Image component for displaying database-stored images with fallbacks and optimization
- **ImageUploader.tsx** - Reusable image upload component with validation and preview functionality
- **PageViewTracker.js** - Analytics component for tracking page views and user engagement

These common components provide essential functionality that is needed across different areas of the Bank of Styx platform, promoting code reuse and consistency.
