"use client";

/**
 * Custom hook for Server-Sent Events (SSE)
 *
 * This hook provides a way to establish and manage SSE connections.
 * It handles connection setup, message processing, and reconnection logic.
 *
 * Enhanced with:
 * - Global connection store to persist across re-renders
 * - True unmount detection to prevent premature disconnection
 * - Improved error handling for better connection stability
 * - Connection reuse to prevent duplicate connections
 */

import { useState, useEffect, useRef, useCallback } from "react";
import { useAuth } from "../contexts/AuthContext";
import { useUserState } from "../contexts/UserStateContext";
import { useQueryClient } from "@tanstack/react-query";

interface SSEOptions {
  onMessage?: (data: any) => void;
  onConnect?: () => void;
  onError?: (error: any) => void;
  enabled?: boolean;
}

// Create a global store for EventSource instances to persist across re-renders
const globalEventSources = new Map<
  string,
  {
    eventSource: EventSource;
    userId: string;
    lastActivity: Date;
  }
>();


export function useSSE(options: SSEOptions = {}) {
  const { isAuthenticated, user } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [lastEventTime, setLastEventTime] = useState<Date | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);
  const queryClient = useQueryClient();
  const { updateNotificationTimestamp } = useUserState();

  // Use a ref to track if this is a true unmount vs a re-render
  const isTrueUnmountRef = useRef(false);

  // Generate a stable connection ID for this component instance
  const connectionIdRef = useRef<string>(
    `sse-${Math.random().toString(36).substring(2, 9)}`,
  );

  // Store the user ID for connection management
  const userIdRef = useRef<string | null>(null);
  if (user?.id) {
    userIdRef.current = user.id;
  }

  const { onMessage, onConnect, onError, enabled = false } = options;

  // Connect to SSE
  const connect = useCallback(() => {
    if (!isAuthenticated || !userIdRef.current) return;

    try {
      // Check if we already have a connection in the global store for this user
      let existingConnection:
        | { eventSource: EventSource; userId: string; lastActivity: Date }
        | undefined;

      // Find any existing connection for this user
      globalEventSources.forEach((value, key) => {
        if (
          value.userId === userIdRef.current &&
          value.eventSource.readyState !== 2
        ) {
          // 2 = CLOSED
          existingConnection = value;
          connectionIdRef.current = key; // Update our reference to use the existing connection ID
        }
      });

      if (existingConnection) {
        console.log(
          `[SSE] Reusing existing connection (${connectionIdRef.current})`,
        );
        eventSourceRef.current = existingConnection.eventSource;

        // Update connection status based on the existing connection's state
        if (existingConnection.eventSource.readyState === 1) {
          // 1 = OPEN
          setIsConnected(true);
          setError(null);
        }

        // Update the last activity timestamp
        existingConnection.lastActivity = new Date();
        return;
      }

      // Close existing connection if any
      if (eventSourceRef.current) {
        console.log(
          "[SSE] Closing existing connection before creating a new one",
        );
        eventSourceRef.current.close();
      }

      const token = localStorage.getItem("auth_token");
      if (!token) {
        throw new Error("No authentication token found");
      }

      // Create EventSource with authorization
      const eventSource = createEventSourceWithAuth(
        "/api/notifications/sse",
        token,
      );

      // Set up event handlers
      eventSource.onopen = () => {
        setIsConnected(true);
        setError(null);

        // Update the last activity timestamp in the global store
        const connection = globalEventSources.get(connectionIdRef.current);
        if (connection) {
          connection.lastActivity = new Date();
        }

        if (onConnect) onConnect();
      };

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          const now = new Date();
          setLastEventTime(now);

          // Update the last activity timestamp in the global store
          const connection = globalEventSources.get(connectionIdRef.current);
          if (connection) {
            connection.lastActivity = now;
          }

          // Handle different message types
          if (data.type === "connected") {
          } else if (data.type === "heartbeat") {
            // Heartbeat received, update last event time but don't log to avoid console spam
            // console.log("[SSE] Heartbeat received at:", data.timestamp);
          } else if (data.type === "notification") {
    

            // Update notification timestamp to trigger refresh
            updateNotificationTimestamp();

            // Update the notifications query cache
            queryClient.setQueryData(
              ["notifications"],
              (oldData: any[] = []) => {
                // If the notification is already in the cache, don't add it again
                if (
                  oldData &&
                  oldData.some((item) => item.id === data.notification.id)
                ) {
                  return oldData;
                }
                // Add the new notification to the beginning of the array
                return [data.notification, ...(oldData || [])];
              },
            );

            // Invalidate the notifications query to ensure consistency
            queryClient.invalidateQueries({ queryKey: ["notifications"] });
          } else {
            // Log other message types
            console.log("[SSE] Received message of type:", data.type);
          }

          if (onMessage) onMessage(data);
        } catch (err) {
          console.error("[SSE] Error parsing message:", err);
        }
      };

      eventSource.onerror = (err) => {
        console.error("[SSE] Connection error:", err);
        setError(err as any);

        // Check the readyState to determine if the connection is actually closed
        if (eventSource.readyState === 2) {
          // 2 = CLOSED
          console.log(
            "[SSE] Connection closed due to fatal error. Manual reconnection required.",
          );
          setIsConnected(false);

          // Only remove from global store if it's a fatal error
          globalEventSources.delete(connectionIdRef.current);
          eventSourceRef.current = null;

          if (onError) onError(err);
        } else {
          // For non-fatal errors, the browser will automatically try to reconnect
          console.log(
            "[SSE] Non-fatal error, browser will attempt to reconnect automatically",
          );
        }
      };

      // Store the EventSource reference both locally and globally
      eventSourceRef.current = eventSource;
      globalEventSources.set(connectionIdRef.current, {
        eventSource,
        userId: userIdRef.current,
        lastActivity: new Date(),
      });

      // Log the current state of global connections

    } catch (err) {
      console.error("[SSE] Setup error:", err);
      setError(err as any);
      if (onError) onError(err);
    }
  }, [
    isAuthenticated,
    onConnect,
    onMessage,
    onError,
    queryClient,
    updateNotificationTimestamp,
    user?.id,
  ]);

  // Disconnect from SSE
  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      console.log("[SSE] Closing connection");
      eventSourceRef.current.close();
      globalEventSources.delete(connectionIdRef.current);
      eventSourceRef.current = null;
      setIsConnected(false);

      // Log the current state of global connections
  
    }
  }, []);

  // Track if we've attempted to connect
  const hasAttemptedConnectRef = useRef(false);

  // Set up true unmount detection
  useEffect(() => {
    isTrueUnmountRef.current = false;

    return () => {
      isTrueUnmountRef.current = true;

      // Use setTimeout to distinguish between re-renders and true unmounts
      // In a re-render, the component will mount again before the timeout fires
      setTimeout(() => {
        if (isTrueUnmountRef.current) {
          console.log(
            "[SSE] True component unmount detected, cleaning up connection",
          );
          const connectionData = globalEventSources.get(
            connectionIdRef.current,
          );
          if (connectionData) {
            connectionData.eventSource.close();
            globalEventSources.delete(connectionIdRef.current);

  
          }
        } else {
          console.log("[SSE] Component remounted, keeping connection alive");
        }
      }, 100);
    };
  }, []);

  // Connect on mount if enabled
  useEffect(() => {
    // Only connect automatically if enabled is true and we haven't already attempted
    if (isAuthenticated && enabled && !hasAttemptedConnectRef.current) {
      hasAttemptedConnectRef.current = true;
      connect();
    }

    // Note: We're NOT disconnecting on unmount here anymore
    // That's handled by the true unmount detection effect
  }, [isAuthenticated, enabled, connect]);

  // Cleanup stale connections periodically
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      const now = new Date();
      let cleanedUp = false;

      globalEventSources.forEach((value, key) => {
        const inactiveMs = now.getTime() - value.lastActivity.getTime();
        // If inactive for more than 5 minutes and not the current connection
        if (inactiveMs > 5 * 60 * 1000 && key !== connectionIdRef.current) {
          console.log(
            `[SSE] Cleaning up stale connection: ${key} (inactive for ${Math.round(
              inactiveMs / 1000,
            )}s)`,
          );
          value.eventSource.close();
          globalEventSources.delete(key);
          cleanedUp = true;
        }
      });

      if (cleanedUp) {
      }
    }, 60000); // Check every minute

    return () => {
      clearInterval(cleanupInterval);
    };
  }, []);

  return {
    isConnected,
    error,
    lastEventTime,
    connect,
    disconnect,
  };
}

// Function to create an EventSource with authentication
function createEventSourceWithAuth(url: string, token: string): EventSource {
  // Create a new URL object to add the token as a query parameter
  // This is a workaround since EventSource doesn't support custom headers
  const urlWithAuth = new URL(url, window.location.origin);
  urlWithAuth.searchParams.append("auth_token", token);


  // Create and return a new EventSource with the modified URL
  const eventSource = new EventSource(urlWithAuth.toString());

  // Add additional event listeners for debugging
  eventSource.addEventListener("error", (event) => {
    console.error("[SSE] EventSource error event:", event);
  });

  return eventSource;
}
