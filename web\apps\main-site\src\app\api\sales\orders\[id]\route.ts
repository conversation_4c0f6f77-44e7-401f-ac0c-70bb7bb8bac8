import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
interface Params {
  params: { id: string };
}

// GET /api/sales/orders/[id] - Get a specific order (Sales Manager)
export async function GET(req: NextRequest, { params }: Params) {
  try {
    const { id } = params;

    // Check if user is authenticated and has sales manager role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasSalesRole = await userHasRole(req, "salesManager");
    if (!hasSalesRole) {
      return NextResponse.json(
        { error: "Unauthorized - Sales Manager role required" },
        { status: 403 },
      );
    }

    // Get the order
    const order = await prisma.order.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            displayName: true,
          },
        },
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                category: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
                event: {
                  select: {
                    id: true,
                    name: true,
                    startDate: true,
                    endDate: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!order) {
      return NextResponse.json({ error: "Order not found" }, { status: 404 });
    }

    return NextResponse.json({ order });
  } catch (error) {
    console.error("Error fetching order:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}

// PUT /api/sales/orders/[id] - Update order status (Sales Manager)
export async function PUT(req: NextRequest, { params }: Params) {
  try {
    const { id } = params;
    const { status } = await req.json();

    // Check if user is authenticated and has sales manager role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasSalesRole = await userHasRole(req, "salesManager");
    if (!hasSalesRole) {
      return NextResponse.json(
        { error: "Unauthorized - Sales Manager role required" },
        { status: 403 },
      );
    }

    // Validate status
    const validStatuses = [
      "pending",
      "paid",
      "fulfilled",
      "cancelled",
      "refunded",
    ];
    if (!validStatuses.includes(status)) {
      return NextResponse.json({ error: "Invalid status" }, { status: 400 });
    }

    // Update the order
    const order = await prisma.order.update({
      where: { id },
      data: {
        status,
        updatedAt: new Date(),
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            displayName: true,
          },
        },
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                category: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
                event: {
                  select: {
                    id: true,
                    name: true,
                    startDate: true,
                    endDate: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return NextResponse.json({ order });
  } catch (error) {
    console.error("Error updating order:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
