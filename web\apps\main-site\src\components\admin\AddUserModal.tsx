"use client";

import React, { useState } from "react";
import { Input, Button } from "@bank-of-styx/ui";

interface AddUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (userData: {
    username: string;
    displayName: string;
    email: string;
    password: string;
    roles: {
      admin: boolean;
      editor: boolean;
      banker: boolean;
      chatModerator: boolean;
      volunteerCoordinator: boolean;
      leadManager: boolean;
      salesManager: boolean;
      landSteward: boolean;
    };
    status: string;
  }) => void;
  isLoading: boolean;
}

export const AddUserModal: React.FC<AddUserModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  isLoading,
}) => {
  const [username, setUsername] = useState("");
  const [displayName, setDisplayName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [roles, setRoles] = useState({
    admin: false,
    editor: false,
    banker: false,
    chatModerator: false,
    volunteerCoordinator: false,
    leadManager: false,
    salesManager: false,
    landSteward: false,
  });
  const [status, setStatus] = useState("active");
  const [errors, setErrors] = useState<Record<string, string>>({});

  if (!isOpen) return null;

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!username.trim()) {
      newErrors.username = "Username is required";
    } else if (username.length < 3) {
      newErrors.username = "Username must be at least 3 characters";
    }

    if (!displayName.trim()) {
      newErrors.displayName = "Display name is required";
    }

    if (!email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = "Email is invalid";
    }

    if (!password.trim()) {
      newErrors.password = "Password is required";
    } else if (password.length < 6) {
      newErrors.password = "Password must be at least 6 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit({
        username,
        displayName,
        email,
        password,
        roles,
        status,
      });
    }
  };

  const handleRoleChange = (role: keyof typeof roles) => {
    setRoles((prev) => ({
      ...prev,
      [role]: !prev[role],
    }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-secondary-light rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="bg-secondary p-4 border-b border-gray-600 flex justify-between items-center">
          <h2 className="text-xl font-bold text-white">Add New User</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="flex-grow overflow-y-auto p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Username */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Username*
                </label>
                <Input
                  type="text"
                  value={username}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setUsername(e.target.value)
                  }
                  placeholder="Enter username"
                  className={`w-full ${
                    errors.username ? "border-red-500" : ""
                  }`}
                />
                {errors.username && (
                  <p className="mt-1 text-sm text-red-500">{errors.username}</p>
                )}
              </div>

              {/* Display Name */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Display Name*
                </label>
                <Input
                  type="text"
                  value={displayName}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setDisplayName(e.target.value)
                  }
                  placeholder="Enter display name"
                  className={`w-full ${
                    errors.displayName ? "border-red-500" : ""
                  }`}
                />
                {errors.displayName && (
                  <p className="mt-1 text-sm text-red-500">
                    {errors.displayName}
                  </p>
                )}
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Email*
                </label>
                <Input
                  type="email"
                  value={email}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setEmail(e.target.value)
                  }
                  placeholder="Enter email"
                  className={`w-full ${errors.email ? "border-red-500" : ""}`}
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-500">{errors.email}</p>
                )}
              </div>

              {/* Password */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Password*
                </label>
                <Input
                  type="password"
                  value={password}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setPassword(e.target.value)
                  }
                  placeholder="Enter password"
                  className={`w-full ${
                    errors.password ? "border-red-500" : ""
                  }`}
                />
                {errors.password && (
                  <p className="mt-1 text-sm text-red-500">{errors.password}</p>
                )}
              </div>
            </div>

            {/* User Status */}
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">
                Status
              </label>
              <select
                value={status}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                  setStatus(e.target.value)
                }
                className="w-full bg-secondary border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="inactive">Inactive</option>
                <option value="suspended">Suspended</option>
                <option value="frozen">Frozen</option>
              </select>
            </div>

            {/* User Roles */}
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                Roles
              </label>
              <div className="grid grid-cols-2 gap-3">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="role-admin"
                    checked={roles.admin}
                    onChange={() => handleRoleChange("admin")}
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                  />
                  <label
                    htmlFor="role-admin"
                    className="ml-2 block text-sm text-white"
                  >
                    Admin
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="role-editor"
                    checked={roles.editor}
                    onChange={() => handleRoleChange("editor")}
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                  />
                  <label
                    htmlFor="role-editor"
                    className="ml-2 block text-sm text-white"
                  >
                    Editor
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="role-banker"
                    checked={roles.banker}
                    onChange={() => handleRoleChange("banker")}
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                  />
                  <label
                    htmlFor="role-banker"
                    className="ml-2 block text-sm text-white"
                  >
                    Banker
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="role-chatModerator"
                    checked={roles.chatModerator}
                    onChange={() => handleRoleChange("chatModerator")}
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                  />
                  <label
                    htmlFor="role-chatModerator"
                    className="ml-2 block text-sm text-white"
                  >
                    Chat Moderator
                  </label>
                  <div className="">
                    <input
                      type="checkbox"
                      id="role-volunteerCoordinator"
                      checked={roles.volunteerCoordinator}
                      onChange={() => handleRoleChange("volunteerCoordinator")}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                    />
                    <label
                      htmlFor="role-volunteerCoordinator"
                      className="ml-2 block text-sm text-white"
                    >
                      Volunteer Coordinator
                    </label>
                  </div>
                  <div className="">
                    <input
                      type="checkbox"
                      id="role-leadManager"
                      checked={roles.leadManager}
                      onChange={() => handleRoleChange("leadManager")}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                    />
                    <label
                      htmlFor="role-leadManager"
                      className="ml-2 block text-sm text-white"
                    >
                      Lead Manager
                    </label>
                  </div>
                  <div className="">
                    <input
                      type="checkbox"
                      id="role-salesManager"
                      checked={roles.salesManager}
                      onChange={() => handleRoleChange("salesManager")}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                    />
                    <label
                      htmlFor="role-salesManager"
                      className="ml-2 block text-sm text-white"
                    >
                      Sales Manager
                    </label>
                  </div>
                  <div className="">
                    <input
                      type="checkbox"
                      id="role-landSteward"
                      checked={roles.landSteward}
                      onChange={() => handleRoleChange("landSteward")}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                    />
                    <label
                      htmlFor="role-landSteward"
                      className="ml-2 block text-sm text-white"
                    >
                      Land Steward
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>

        {/* Footer */}
        <div className="bg-secondary p-4 border-t border-gray-600 flex justify-end space-x-3">
          <Button variant="secondary" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleSubmit} loading={isLoading}>
            Create User
          </Button>
        </div>
      </div>
    </div>
  );
};
