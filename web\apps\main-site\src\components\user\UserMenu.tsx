"use client";

import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import { useAuth } from "../../contexts/AuthContext";
import { useColorTheme } from "../../contexts/ColorThemeContext";

export const UserMenu = () => {
  const { user, logout } = useAuth();
  const { isDarkMode, toggleTheme } = useColorTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Set mounted to true after component mounts to prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  // Don't render anything during server-side rendering or if user is not available
  if (!mounted || !user) return null;

  return (
    <div className="relative" ref={menuRef}>
      {/* Avatar button */}
      <button
        className="flex items-center focus:outline-none text-text-primary"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <img
          src={user.avatar}
          alt={user.displayName}
          className="w-8 h-8 rounded-full border-2 border-transparent hover:border-primary transition-colors"
        />
        <span className="hidden md:inline ml-2 font-medium">
          {user.displayName}
        </span>
        <svg
          className={`ml-1 h-5 w-5 transition-transform ${
            isOpen ? "rotate-180" : ""
          }`}
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
          aria-hidden="true"
        >
          <path
            fillRule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </button>

      {/* Dropdown menu */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-secondary-light z-50 py-1 text-text-primary">
          <div
            className={`px-4 py-3 border-b ${
              isDarkMode ? "border-gray-600" : "border-gray-300"
            }`}
          >
            <p className="text-sm font-medium">{user.displayName}</p>
            <p className="text-sm text-text-secondary truncate">
              @{user.username}
            </p>
          </div>

          <Link
            href="/bank/dashboard"
            className="block px-4 py-2 text-sm hover:bg-secondary transition-colors"
            onClick={() => setIsOpen(false)}
          >
            Bank Dashboard
          </Link>

          {user.ship?.isCaptain && (
            <Link
              href="/captain/dashboard"
              className="block px-4 py-2 text-sm hover:bg-secondary transition-colors"
              onClick={() => setIsOpen(false)}
            >
              Captain Dashboard
            </Link>
          )}

          {user.merchant?.status === "approved" && (
            <Link
              href="/merchants/dashboard"
              className="block px-4 py-2 text-sm hover:bg-secondary transition-colors"
              onClick={() => setIsOpen(false)}
            >
              Merchant Dashboard
            </Link>
          )}

          {user.auctions?.hasCreated && (
            <Link
              href="/auctions/dashboard"
              className="block px-4 py-2 text-sm hover:bg-secondary transition-colors"
              onClick={() => setIsOpen(false)}
            >
              Auction Dashboard
            </Link>
          )}

          {user.roles?.admin && (
            <Link
              href="/admin/dashboard"
              className="block px-4 py-2 text-sm hover:bg-secondary transition-colors"
              onClick={() => setIsOpen(false)}
            >
              Admin Dashboard
            </Link>
          )}

          {(user.roles?.admin || user.roles?.landSteward) && (
            <Link
              href="/land-steward"
              className="block px-4 py-2 text-sm hover:bg-secondary transition-colors"
              onClick={() => setIsOpen(false)}
            >
              Land Steward
            </Link>
          )}

          {user.roles?.editor && (
            <Link
              href="/news/dashboard"
              className="block px-4 py-2 text-sm hover:bg-secondary transition-colors"
              onClick={() => setIsOpen(false)}
            >
              News Management
            </Link>
          )}

          {user.roles?.banker && (
            <Link
              href="/cashier/dashboard"
              className="block px-4 py-2 text-sm hover:bg-secondary transition-colors"
              onClick={() => setIsOpen(false)}
            >
              Cashier Dashboard
            </Link>
          )}

          {user.roles?.volunteerCoordinator && (
            <Link
              href="/volunteer/dashboard"
              className="block px-4 py-2 text-sm hover:bg-secondary transition-colors"
              onClick={() => setIsOpen(false)}
            >
              Volunteer Coordinator
            </Link>
          )}

          {user.roles?.leadManager && (
            <Link
              href="/volunteer/lead/dashboard"
              className="block px-4 py-2 text-sm hover:bg-secondary transition-colors"
              onClick={() => setIsOpen(false)}
            >
              Category Lead Dashboard
            </Link>
          )}

          {user.roles?.salesManager && (
            <Link
              href="/sales/dashboard"
              className="block px-4 py-2 text-sm hover:bg-secondary transition-colors"
              onClick={() => setIsOpen(false)}
            >
              Sales Dashboard
            </Link>
          )}

          <Link
            href="/shop"
            className="block px-4 py-2 text-sm hover:bg-secondary transition-colors"
            onClick={() => setIsOpen(false)}
          >
            Shop
          </Link>

          <Link
            href="/shop/cart"
            className="block px-4 py-2 text-sm hover:bg-secondary transition-colors"
            onClick={() => setIsOpen(false)}
          >
            Shopping Cart
          </Link>

          <Link
            href="/shop/orders"
            className="block px-4 py-2 text-sm hover:bg-secondary transition-colors"
            onClick={() => setIsOpen(false)}
          >
            Order History
          </Link>

          <Link
            href="/volunteer"
            className="block px-4 py-2 text-sm hover:bg-secondary transition-colors"
            onClick={() => setIsOpen(false)}
          >
            Volunteer Sign-up
          </Link>

          <Link
            href="/settings"
            className="block px-4 py-2 text-sm hover:bg-secondary transition-colors"
            onClick={() => setIsOpen(false)}
          >
            Account Settings
          </Link>

          <button
            className="w-full text-left px-4 py-2 text-sm hover:bg-secondary transition-colors"
            onClick={toggleTheme}
          >
            {isDarkMode ? "Switch to Light Mode" : "Switch to Dark Mode"}
          </button>

          <Link
            href="/settings/colors"
            className="block px-4 py-2 text-sm hover:bg-secondary transition-colors"
            onClick={() => setIsOpen(false)}
          >
            Color Settings
          </Link>

          <div
            className={`border-t mt-1 pt-1 ${
              isDarkMode ? "border-gray-600" : "border-gray-300"
            }`}
          >
            <button
              className="w-full text-left px-4 py-2 text-sm text-accent hover:bg-secondary transition-colors"
              onClick={() => {
                setIsOpen(false);
                logout();
              }}
            >
              Logout
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
