import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../../../lib/prisma";
import { verifyToken } from "../../../../../../lib/auth";
import { createNotification } from "../../../../../../lib/notifications";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export const POST = async (
  req: NextRequest,
  { params }: { params: { id: string } },
) => {
  try {
    // Get the transaction ID from the request body
    const { id } = await req.json();

    if (!id) {
      return NextResponse.json(
        { error: "Transaction ID is required" },
        { status: 400 },
      );
    }

    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const userId = decoded.id as string;

    // Find the withdrawal transaction
    const transaction = await prisma.transaction.findUnique({
      where: { id },
      include: {
        sender: true,
      },
    });

    if (!transaction) {
      return NextResponse.json(
        { error: "Transaction not found" },
        { status: 404 },
      );
    }

    // Check if the user is the sender of the withdrawal
    if (transaction.senderId !== userId) {
      return NextResponse.json(
        { error: "You can only cancel your own withdrawals" },
        { status: 403 },
      );
    }

    // Check if the transaction is a withdrawal and is pending
    if (transaction.type !== "withdrawal") {
      return NextResponse.json(
        { error: "Transaction is not a withdrawal" },
        { status: 400 },
      );
    }

    if (transaction.status !== "pending") {
      return NextResponse.json(
        { error: "Only pending withdrawals can be cancelled" },
        { status: 400 },
      );
    }

    // Process the cancellation in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update the transaction status to cancelled
      const updatedTransaction = await tx.transaction.update({
        where: { id },
        data: {
          status: "cancelled",
          note: transaction.note
            ? `${transaction.note} (Cancelled by user)`
            : "Cancelled by user",
          processedById: userId, // Self-processed
          processedAt: new Date(),
        },
        include: {
          sender: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatar: true,
            },
          },
          recipient: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatar: true,
            },
          },
          processedBy: {
            select: {
              id: true,
              username: true,
              displayName: true,
            },
          },
        },
      });

      // Return funds to the user's balance
      await tx.user.update({
        where: { id: userId },
        data: { balance: { increment: transaction.amount } },
      });

      return { transaction: updatedTransaction };
    });

    // Create notification for the user
    await createNotification(userId, {
      category: "transaction",
      type: "withdrawal_cancelled",
      title: "Withdrawal Cancelled",
      message: `Your withdrawal request of NS ${transaction.amount.toFixed(
        0,
      )} has been cancelled and the funds have been returned to your balance.`,
      link: "/bank/dashboard/withdraw",
      priority: "medium",
      transactionId: transaction.id,
    });

    // Format dates for response
    const formattedResponse = {
      transaction: {
        ...result.transaction,
        createdAt: result.transaction.createdAt.toISOString(),
        updatedAt: result.transaction.updatedAt.toISOString(),
        processedAt: result.transaction.processedAt
          ? result.transaction.processedAt.toISOString()
          : null,
      },
      message: "Withdrawal cancelled successfully",
    };

    return NextResponse.json(formattedResponse);
  } catch (error) {
    console.error("Error cancelling withdrawal:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
