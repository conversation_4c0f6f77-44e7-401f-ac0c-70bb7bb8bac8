import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../lib/prisma";
import jwt from "jsonwebtoken";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
const JWT_SECRET = process.env.JWT_SECRET || "your-development-secret-key";

// Helper function to verify token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

export const GET = async (req: NextRequest) => {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { id: decoded.id as string },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Count pending transactions
    const pendingTransactionsCount = await prisma.transaction.count({
      where: {
        OR: [{ senderId: user.id }, { recipientId: user.id }],
        status: "pending",
      },
    });

    // Get last login time (using updatedAt as a proxy)
    const lastLogin = user.updatedAt.toISOString();

    // Return account summary
    return NextResponse.json({
      accountType: user.merchantStatus === "approved" ? "Merchant" : "Standard",
      accountStatus: "Active",
      lastLogin: lastLogin,
      pendingTransactions: pendingTransactionsCount,
    });
  } catch (error) {
    console.error("Error fetching account summary:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
};
