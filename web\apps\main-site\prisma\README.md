# Database Schema & Migrations

Prisma ORM configuration, database schema, and migration files for the Bank of Styx platform.

## Files

- **schema.prisma** - Main Prisma schema defining all database models and relationships
- **seed.js** - Database seeding script for initial data and testing
- **dbsetup.sql** - Direct SQL setup commands for database initialization
- **migrations/** - Database migration files organized by feature and chronological order

## Migration Structure

The migrations directory contains feature-specific migrations including:

- Initial database setup
- Banking system models
- News and content management
- Notification systems
- Support ticket system
- Event management
- Volunteer management
- Shopping and order system
- Ticket hold system

Use `pnpm prisma migrate dev` to apply migrations during development and `pnpm prisma migrate deploy` for production deployments.
