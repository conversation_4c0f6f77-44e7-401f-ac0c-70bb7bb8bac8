import React, { useState } from "react";
import { <PERSON><PERSON>, Modal } from "@bank-of-styx/ui";
import { VolunteerPayment } from "@/hooks/useVolunteerPayments";
import { formatCurrency } from "@/lib/utils";

interface PaymentConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (note: string) => void;
  payments: VolunteerPayment[];
  isBulk: boolean;
}

export default function PaymentConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  payments,
  isBulk,
}: PaymentConfirmationModalProps) {
  const [note, setNote] = useState("");

  // Calculate total amount
  const totalAmount = payments.reduce(
    (total, payment) => total + payment.paymentAmount,
    0,
  );

  const handleConfirm = () => {
    onConfirm(note);
    setNote(""); // Reset note after confirmation
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Confirm Payment">
      <div className="p-4">
        <div className="mb-4">
          <p className="text-white mb-2">
            You are about to process{" "}
            {isBulk ? "multiple payments" : "a payment"} for:
          </p>

          {payments.length > 0 && (
            <div className="bg-secondary-dark p-3 rounded mb-4 max-h-60 overflow-y-auto">
              {payments.map((payment) => (
                <div
                  key={payment.id}
                  className="flex justify-between items-center mb-2 pb-2 border-b border-gray-600 last:border-0 last:mb-0 last:pb-0"
                >
                  <div>
                    <div className="text-white">{payment.user.displayName}</div>
                    <div className="text-gray-400 text-sm">
                      {payment.assignment.shift.event.name} -{" "}
                      {payment.assignment.shift.category.name}
                    </div>
                  </div>
                  <div className="text-white font-semibold">
                    {formatCurrency(payment.paymentAmount)}
                  </div>
                </div>
              ))}
            </div>
          )}

          <div className="flex justify-between items-center bg-secondary p-3 rounded">
            <span className="text-white font-semibold">Total Amount:</span>
            <span className="text-white font-bold text-lg">
              {formatCurrency(totalAmount)}
            </span>
          </div>
        </div>

        <div className="mb-4">
          <label className="block text-gray-300 mb-1">
            Payment Note (Optional)
          </label>
          <textarea
            value={note}
            onChange={(e) => setNote(e.target.value)}
            placeholder="Add a note about this payment..."
            className="w-full bg-secondary-dark text-white border border-gray-600 rounded p-2 h-24"
          />
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleConfirm}>
            Confirm Payment
          </Button>
        </div>
      </div>
    </Modal>
  );
}
