"use client";

import { useEffect, useState, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@bank-of-styx/ui";
import {
  VolunteerDashboardLayout,
  EventSelector,
  CategorySelector,
  ShiftList,
  ShiftForm,
  ConfirmationModal,
} from "@/components/volunteer";
import {
  VolunteerShift,
  useVolunteerShiftsByEventAndCategory,
  useDeleteVolunteerShift,
} from "@/hooks/useVolunteerShifts";

export default function ShiftsPage() {
  const { user, isLoading: authLoading } = useAuth();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();

  // State
  const [selectedEventId, setSelectedEventId] = useState<string | null>(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(
    null,
  );
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingShift, setEditingShift] = useState<VolunteerShift | null>(null);
  const [deletingShift, setDeletingShift] = useState<VolunteerShift | null>(
    null,
  );

  // Get event ID and category ID from URL if available
  useEffect(() => {
    const eventId = searchParams.get("eventId");
    const categoryId = searchParams.get("categoryId");

    if (eventId) {
      setSelectedEventId(eventId);
    }

    if (categoryId) {
      setSelectedCategoryId(categoryId);
    }
  }, [searchParams]);

  // Check if user is authorized to access this page
  useEffect(() => {
    if (!authLoading) {
      if (!user || !user.roles?.volunteerCoordinator) {
        router.push("/");
      } else {
        setIsAuthorized(true);
      }
    }
  }, [user, authLoading, router]);

  // Fetch shifts for the selected event and category
  const {
    data: shifts = [],
    isLoading: shiftsLoading,
    error: shiftsError,
    refetch: refetchShifts,
  } = useVolunteerShiftsByEventAndCategory(selectedEventId, selectedCategoryId);

  // Delete shift mutation
  const deleteMutation = useDeleteVolunteerShift(
    selectedEventId,
    selectedCategoryId,
  );

  // Handle event selection
  const handleEventSelect = useCallback(
    (eventId: string) => {
      setSelectedEventId(eventId);
      setSelectedCategoryId(null); // Reset category when event changes

      // Update URL
      router.push(`/volunteer/dashboard/shifts?eventId=${eventId}`);

      // Reset state when changing events
      setShowCreateForm(false);
      setEditingShift(null);
      setDeletingShift(null);
    },
    [router],
  );

  // Handle category selection
  const handleCategorySelect = useCallback(
    (categoryId: string) => {
      setSelectedCategoryId(categoryId);

      // Update URL
      if (selectedEventId) {
        router.push(
          `/volunteer/dashboard/shifts?eventId=${selectedEventId}&categoryId=${categoryId}`,
        );
      }

      // Reset state when changing categories
      setShowCreateForm(false);
      setEditingShift(null);
      setDeletingShift(null);
    },
    [selectedEventId, router],
  );

  // Handle create shift button click
  const handleCreateClick = () => {
    setShowCreateForm(true);
    setEditingShift(null);
  };

  // Handle edit shift
  const handleEditShift = (shift: VolunteerShift) => {
    setEditingShift(shift);
    setShowCreateForm(false);
  };

  // Handle delete shift
  const handleDeleteShift = (shift: VolunteerShift) => {
    setDeletingShift(shift);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!deletingShift) return;

    try {
      await deleteMutation.mutateAsync(deletingShift.id);
      setDeletingShift(null);
      refetchShifts();
    } catch (error) {
      console.error("Error deleting shift:", error);
    }
  };

  // Handle form success
  const handleFormSuccess = () => {
    setShowCreateForm(false);
    setEditingShift(null);
    refetchShifts();
  };

  // Handle form cancel
  const handleFormCancel = () => {
    setShowCreateForm(false);
    setEditingShift(null);
  };

  // Show loading state while checking authorization
  if (authLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="text-white text-lg mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <VolunteerDashboardLayout>
      <div>
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-white mb-2">
            Shift Management
          </h1>
          <p className="text-gray-400">
            Create and manage volunteer shifts for events and categories.
          </p>
        </div>

        {/* Event and Category Selectors */}
        <div className="mb-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          <EventSelector
            onEventSelect={handleEventSelect}
            initialEventId={selectedEventId || ""}
          />

          {selectedEventId && (
            <CategorySelector
              eventId={selectedEventId}
              onCategorySelect={handleCategorySelect}
              initialCategoryId={selectedCategoryId || ""}
            />
          )}
        </div>

        {/* Create Shift Button */}
        {selectedEventId &&
          selectedCategoryId &&
          !showCreateForm &&
          !editingShift && (
            <div className="flex justify-end mb-6">
              <Button onClick={handleCreateClick} variant="primary">
                Create Shift
              </Button>
            </div>
          )}

        {/* Create/Edit Form */}
        {selectedEventId &&
          selectedCategoryId &&
          (showCreateForm || editingShift) && (
            <div className="mb-6">
              <ShiftForm
                eventId={selectedEventId}
                categoryId={selectedCategoryId}
                shift={editingShift}
                onSuccess={handleFormSuccess}
                onCancel={handleFormCancel}
              />
            </div>
          )}

        {/* Shifts List */}
        {selectedEventId &&
          selectedCategoryId &&
          !showCreateForm &&
          !editingShift && (
            <ShiftList
              shifts={shifts}
              isLoading={shiftsLoading}
              error={shiftsError as Error}
              onEdit={handleEditShift}
              onDelete={handleDeleteShift}
            />
          )}

        {/* No Event Selected Message */}
        {!selectedEventId && (
          <div className="bg-secondary-light rounded-lg shadow-md p-6 border border-gray-600 text-center">
            <p className="text-white mb-4">
              Please select an event to manage its shifts.
            </p>
            <p className="text-gray-400">
              Use the event selector above to choose an event.
            </p>
          </div>
        )}

        {/* No Category Selected Message */}
        {selectedEventId && !selectedCategoryId && (
          <div className="bg-secondary-light rounded-lg shadow-md p-6 border border-gray-600 text-center">
            <p className="text-white mb-4">
              Please select a category to manage its shifts.
            </p>
            <p className="text-gray-400">
              Use the category selector above to choose a category.
            </p>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        <ConfirmationModal
          isOpen={!!deletingShift}
          onClose={() => setDeletingShift(null)}
          onConfirm={handleDeleteConfirm}
          title="Delete Shift"
          message={`Are you sure you want to delete the shift "${deletingShift?.title}"? This action cannot be undone.`}
          confirmText="Delete"
          cancelText="Cancel"
          isLoading={deleteMutation.isPending}
          variant="danger"
        />
      </div>
    </VolunteerDashboardLayout>
  );
}
