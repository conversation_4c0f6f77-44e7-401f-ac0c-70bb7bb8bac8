"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../../../../contexts/AuthContext";
import {
  CashierDashboardLayout,
  TransactionsManager,
} from "../../../../components/cashier";
import { useTransactions } from "../../../../hooks/useBank";

export default function TransactionsPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);

  // Use the hook to fetch transactions with proper filter object
  const { data: transactions = [], isLoading: isLoadingTransactions } =
    useTransactions({
      /* No filters to get all transactions */
    });

  // Check authorization after auth is loaded
  useEffect(() => {
    // Only proceed with checks if auth loading is complete
    if (!isLoading) {
      if (!user || !user.roles?.banker) {
        router.push("/");
      } else {
        // User is authorized
        setIsAuthorized(true);
      }
    }
  }, [user, router, isLoading]);

  // Show loading state or nothing while checking authorization
  if (isLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <p className="text-white text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <CashierDashboardLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white mb-2">
          Transaction Lookup
        </h1>
        <p className="text-gray-400">
          Search and filter transaction history across all bank accounts.
        </p>
      </div>

      {isLoadingTransactions ? (
        <div className="bg-secondary-light rounded-lg shadow-md p-4 sm:p-6 border border-gray-600 text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-gray-400">Loading transactions...</p>
        </div>
      ) : (
        <div className="bg-secondary-light rounded-lg shadow-md p-4 sm:p-6 border border-gray-600">
          <TransactionsManager initialTransactions={transactions} />
        </div>
      )}
    </CashierDashboardLayout>
  );
}
