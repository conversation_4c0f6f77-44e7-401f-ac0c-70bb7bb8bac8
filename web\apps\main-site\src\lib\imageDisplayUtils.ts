/**
 * Utility functions for standardized image display handling
 * Used to ensure consistent image display across the application
 */

/**
 * Converts any image URL to the direct URL format
 * This ensures consistent display of images by using the direct path to the file
 * instead of the API endpoint which requires the database ID
 *
 * @param imageUrl The original image URL
 * @returns The standardized direct URL
 */
export function getStandardizedImageUrl(
  imageUrl: string | undefined | null,
): string {
  if (!imageUrl) {
    return "";
  }

  // If the URL starts with http or https, it's already a full URL
  if (imageUrl.startsWith("http")) {
    return imageUrl;
  }

  // If the URL is a direct path to the file in the uploads directory
  if (imageUrl.startsWith("/uploads/")) {
    return imageUrl;
  }

  // If the URL uses the new uploads API endpoint format
  if (imageUrl.startsWith("/api/uploads/")) {
    return imageUrl;
  }

  // If the URL uses the API endpoint format
  if (imageUrl.startsWith("/api/images/")) {
    // For now, we'll leave it as is, as we don't have a reliable way to convert from ID to filepath
    return imageUrl;
  }

  // If it doesn't match any known pattern, return as is
  return imageUrl;
}

/**
 * Returns a handler function for image loading errors
 * When an image fails to load with its original URL, this will try alternative formats
 *
 * @param originalSrc The original image source URL
 * @returns A function to handle the error event
 */
export function getImageErrorHandler(originalSrc: string | undefined | null) {
  return (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    if (!originalSrc) return;

    const target = e.target as HTMLImageElement;

    // Handle paths for deposit receipts
    if (originalSrc.includes("deposit")) {
      // Get just the filename
      const fileName = originalSrc.split("/").pop();

      if (fileName) {
        // If it's using the old path format, try the new API endpoint
        if (
          originalSrc.includes("/uploads/deposits/") &&
          !target.src.includes("/api/uploads/deposits/")
        ) {
          target.src = `/api/uploads/deposits/${fileName}`;
          return;
        }

        // If it's using the new API endpoint but failed, try the old path as fallback (for development)
        if (
          originalSrc.includes("/api/uploads/deposits/") &&
          !target.src.includes("/uploads/deposits/")
        ) {
          target.src = `/uploads/deposits/${fileName}`;
          return;
        }
      }
    }
  };
}

/**
 * Opens an image in a new window using the direct URL format when possible
 *
 * @param imageUrl The original image URL
 */
export function openImageInNewWindow(imageUrl: string | undefined | null) {
  if (!imageUrl) return;

  // For deposit receipts, we need to ensure the URL points to the API endpoint
  if (imageUrl.includes("deposit")) {
    // Extract the filename
    const fileName = imageUrl.split("/").pop();
    if (fileName) {
      // Open using the new API endpoint path
      window.open(`/api/uploads/deposits/${fileName}`, "_blank");
      return;
    }
  }

  // For other cases, use the standardized URL
  const standardUrl = getStandardizedImageUrl(imageUrl);
  window.open(standardUrl, "_blank");
}
