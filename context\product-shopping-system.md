# Product and Shopping System

## Overview
A comprehensive e-commerce system with products, categories, shopping cart, ticket hold system, and Stripe integration for payments.

## Core Components

### Product System
Products are items available for purchase, often linked to events (like tickets):

#### Product Structure
```typescript
// Products with event and category associations
const products = await prisma.product.findMany({
  where: filter,
  include: {
    category: true,
    event: {
      select: {
        id: true,
        name: true,
        startDate: true,
        endDate: true,
        capacity: true,
      },
    },
  },
  orderBy: { createdAt: "desc" },
});
```

#### Product Features
- **Event Association**: Products can be linked to events (e.g., event tickets)
- **Category Organization**: Products belong to categories for filtering
- **Pricing**: Decimal pricing with subtotal calculations
- **Status Control**: Active/inactive status for availability
- **Image Support**: Product images via upload system

### Ticket Hold System
A unique 15-minute hold system preventing overselling:

#### Ticket Allocation
```typescript
// Each product has individual tickets for inventory management
// When adding to cart, specific tickets are held
const availableTickets = await tx.ticket.findMany({
  where: {
    productId,
    status: TicketStatus.AVAILABLE,
  },
  take: quantity,
});

// Create temporary hold
const hold = await tx.ticketHold.create({
  data: {
    userId: user.id,
    cartItemId: cartItem.id,
    expiresAt: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
  },
});

// Mark tickets as held
await tx.ticket.updateMany({
  where: {
    id: { in: availableTickets.map((ticket) => ticket.id) },
  },
  data: {
    status: TicketStatus.HELD,
    holdId: hold.id,
  },
});
```

#### Hold Management
- **Duration**: 15-minute automatic expiration
- **Ticket States**: AVAILABLE → HELD → SOLD (or back to AVAILABLE)
- **Cron Job**: Automated cleanup of expired holds
- **User-specific**: Holds tied to individual users and cart items

#### Expired Hold Cleanup
```typescript
// Automated background job
const expiredTicketHolds = await tx.ticketHold.findMany({
  where: {
    expiresAt: { lt: now },
  },
});

// Release tickets back to available
await tx.ticket.updateMany({
  where: {
    holdId: { in: expiredTicketHolds.map((h) => h.id) },
    status: TicketStatus.HELD,
  },
  data: {
    status: TicketStatus.AVAILABLE,
    holdId: null,
  },
});
```

### Shopping Cart System
Persistent cart with hold integration:

#### Cart Management
```typescript
// Find or create cart for user
let cart = await prisma.cart.findFirst({
  where: { userId: user.id },
  include: {
    items: {
      include: {
        product: {
          include: {
            category: true,
            event: true,
          },
        },
        ticketHold: {
          include: {
            tickets: {
              where: {
                status: TicketStatus.HELD,
              },
            },
          },
        },
      },
    },
  },
});

if (!cart) {
  cart = await prisma.cart.create({
    data: { userId: user.id },
  });
}
```

#### Cart Features
- **Persistent Storage**: Cart persists across sessions
- **Quantity Management**: Update quantities with hold adjustment
- **Hold Integration**: Each cart item has associated ticket holds
- **Subtotal Calculation**: Real-time price calculations
- **Item Validation**: Ensures products are still available and active

#### Adding Items to Cart
```typescript
// Transaction ensures atomicity
const result = await prisma.$transaction(async (tx) => {
  // Validate product availability
  const product = await tx.product.findFirst({
    where: {
      id: productId,
      isActive: true,
    },
  });

  if (!product) {
    throw new Error("Product not found or inactive");
  }

  // Check ticket availability
  const availableTickets = await getAvailableTicketCount(productId);
  if (availableTickets < quantity) {
    throw new Error("Not enough tickets available");
  }

  // Create or update cart item with holds
  // ... (detailed implementation above)
});
```

### Category System
Product organization and filtering:

#### Category Features
- **Hierarchical Organization**: Products grouped by categories
- **Filtering Support**: API endpoints support category filtering
- **Admin Management**: Sales managers can create/edit categories
- **Event Association**: Categories can be event-specific

### Order Processing
Integration with Stripe for payment processing:

#### Order Creation
- **Cart to Order**: Convert cart items to orders
- **Payment Intent**: Stripe integration for secure payments
- **Inventory Management**: Tickets marked as SOLD after successful payment
- **Order History**: Complete purchase records

#### Payment Flow
1. **Cart Review**: User reviews cart with held tickets
2. **Checkout**: Create Stripe payment intent
3. **Payment**: Process payment via Stripe
4. **Fulfillment**: Mark tickets as sold, clear cart
5. **Confirmation**: Order confirmation and receipt

### Inventory Management
Sophisticated ticket-based inventory system:

#### Ticket States
```typescript
enum TicketStatus {
  AVAILABLE = "AVAILABLE",
  HELD = "HELD", 
  SOLD = "SOLD",
  CANCELLED = "CANCELLED"
}
```

#### Availability Checking
```typescript
export async function getAvailableTicketCount(productId: string): Promise<number> {
  const count = await prisma.ticket.count({
    where: {
      productId,
      status: TicketStatus.AVAILABLE,
    },
  });
  return count;
}
```

#### Inventory Features
- **Individual Tickets**: Each product unit is a separate ticket
- **Real-time Availability**: Instant availability checking
- **Overselling Prevention**: Hold system prevents double-booking
- **Automated Cleanup**: Expired holds automatically released

### Cron Job System
Automated background processes:

#### Hold Expiration
```typescript
// POST /api/cron/release-expired-holds
// Runs periodically to clean up expired holds

// Process both ticket holds and volunteer slot holds
const expiredTicketHolds = await tx.ticketHold.findMany({
  where: { expiresAt: { lt: now } },
});

const expiredVolunteerHolds = await tx.volunteerSlotHold.findMany({
  where: { expiresAt: { lt: now } },
});
```

#### Security
- **API Key Protection**: Cron endpoints protected with secret key
- **Transaction Safety**: All cleanup operations use database transactions
- **Monitoring**: Detailed logging of cleanup operations

## API Architecture

### Public Shopping Endpoints
- `GET /api/products` - Browse products with filtering
- `GET /api/products/[id]` - Product details
- `GET /api/product-categories` - Available categories
- `GET /api/products/search` - Product search

### Cart Management
- `GET /api/cart` - Get user's cart
- `POST /api/cart/items` - Add item to cart
- `PUT /api/cart/items/[id]` - Update cart item
- `DELETE /api/cart/items/[id]` - Remove from cart
- `POST /api/cart/clear` - Clear entire cart
- `POST /api/cart/refresh-holds` - Refresh hold expiration

### Checkout and Orders
- `POST /api/checkout/create-payment-intent` - Stripe payment setup
- `POST /api/checkout/webhook` - Stripe webhook handler
- `GET /api/orders` - User order history
- `GET /api/orders/[id]` - Order details

### Admin/Sales Management
- `GET /api/sales/products` - Product management
- `POST /api/sales/products` - Create product
- `PUT /api/sales/products/[id]` - Update product
- `GET /api/sales/product-categories` - Category management
- `GET /api/sales/orders` - Order management

### Background Jobs
- `POST /api/cron/release-expired-holds` - Cleanup expired holds

## Integration Features

### Event Integration
Products can be linked to events, creating event tickets:
- **Event Tickets**: Products representing event access
- **Capacity Management**: Ticket limits based on event capacity
- **Date Validation**: Cannot sell tickets for past events

### Stripe Integration
- **Payment Intents**: Secure payment processing
- **Webhook Handling**: Automated order fulfillment
- **Error Handling**: Payment failure management
- **Receipt Generation**: Automatic confirmation emails

### Banking System Integration
- **Revenue Tracking**: Sales integrated with banking system
- **Financial Reporting**: Revenue reports and analytics

## Database Schema Patterns

### Product Table
```sql
Product {
  id: String (UUID)
  name: String
  description: String
  price: Decimal
  image: String?
  isActive: Boolean
  categoryId: String?
  eventId: String?
  createdAt: DateTime
  updatedAt: DateTime
}
```

### Ticket System
```sql
Ticket {
  id: String (UUID)
  productId: String
  status: TicketStatus
  holdId: String?
  orderId: String?
  createdAt: DateTime
  updatedAt: DateTime
}

TicketHold {
  id: String (UUID)
  userId: String
  cartItemId: String
  expiresAt: DateTime
  createdAt: DateTime
}
```

### Cart System
```sql
Cart {
  id: String (UUID)
  userId: String
  createdAt: DateTime
  updatedAt: DateTime
}

CartItem {
  id: String (UUID)
  cartId: String
  productId: String
  quantity: Int
  createdAt: DateTime
  updatedAt: DateTime
}
```

## Performance Features

### Hold System Benefits
- **Prevents Overselling**: Temporary reservations prevent double-booking
- **User Experience**: Users have time to complete purchase
- **Automatic Cleanup**: No manual intervention required
- **Scalable**: Handles high-traffic sale scenarios

### Caching Strategy
- **Product Lists**: Cacheable product catalogs
- **Availability Counts**: Real-time but optimized queries
- **Category Filters**: Efficient filtering and pagination

## Important Files
- `src/app/api/products/route.ts` - Product catalog
- `src/app/api/cart/items/route.ts` - Cart management with holds
- `src/app/api/cron/release-expired-holds/route.ts` - Automated cleanup
- `src/lib/ticket-system.ts` - Inventory management utilities
- `src/app/api/checkout/` - Stripe integration
- `prisma/schema.prisma` - Product, cart, ticket, and order models