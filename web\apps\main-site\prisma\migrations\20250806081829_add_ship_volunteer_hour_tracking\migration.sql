-- AlterTable
ALTER TABLE `volunteer_hours` ADD COLUMN `creditShipId` VARCHAR(191) NULL,
    ADD COLUMN `isDockHours` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `isLandGrant` BO<PERSON>EAN NOT NULL DEFAULT false;

-- AddForeignKey
ALTER TABLE `volunteer_hours` ADD CONSTRAINT `volunteer_hours_creditShipId_fkey` FOREIGN KEY (`creditShipId`) REFERENCES `ships`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
