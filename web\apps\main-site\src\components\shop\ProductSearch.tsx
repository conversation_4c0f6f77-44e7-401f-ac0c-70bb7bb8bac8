import { useState } from "react";
import { useRouter } from "next/navigation";
import { Input, Button } from "@bank-of-styx/ui";

export const ProductSearch: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const router = useRouter();
  const inputId = "product-search-input"; // Stable ID

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/shop/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  return (
    <form onSubmit={handleSearch} className="flex w-full max-w-md">
      <Input
        id={inputId}
        type="text"
        placeholder="Search products..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        fullWidth
        className="rounded-r-none"
      />
      <Button type="submit" variant="primary" className="rounded-l-none">
        Search
      </Button>
    </form>
  );
};
