"use client";

import React, { useState, useEffect } from "react";
import { useNotifications } from "../../contexts/NotificationContext";

interface NotificationIconProps {
  className?: string;
}

export const NotificationIcon: React.FC<NotificationIconProps> = ({
  className = "",
}) => {
  const { unreadCount, openNotificationPanel } = useNotifications();
  const [mounted, setMounted] = useState(false);

  // Set mounted to true after component mounts to prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <button
      className={`relative p-1 rounded-full hover:bg-secondary-light transition ${className}`}
      onClick={openNotificationPanel}
      aria-label="Notifications"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        className="w-6 h-6"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
        />
      </svg>

      {/* Only show unread count badge after client-side hydration */}
      {mounted && unreadCount > 0 && (
        <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-primary rounded-full">
          {unreadCount > 99 ? "99+" : unreadCount}
        </span>
      )}
    </button>
  );
};

export default NotificationIcon;
