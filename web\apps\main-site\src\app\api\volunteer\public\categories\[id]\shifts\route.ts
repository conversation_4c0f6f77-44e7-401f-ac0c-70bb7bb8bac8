import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/volunteer/public/categories/[id]/shifts - Get shifts for a specific category
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id: categoryId } = params;

    // Check if user is authenticated
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    // Get the category to check if it exists and if the event is published
    const category = await prisma.volunteerCategory.findUnique({
      where: { id: categoryId },
      select: {
        id: true,
        eventId: true,
        event: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 },
      );
    }

    // Check if event is published
    if (category.event.status !== "published") {
      return NextResponse.json(
        { error: "Shifts not available" },
        { status: 403 },
      );
    }

    // Get current date
    const now = new Date();

    // Get shifts for the category
    const shifts = await prisma.volunteerShift.findMany({
      where: {
        categoryId,
        // Only include shifts that haven't ended yet
        endTime: {
          gte: now,
        },
      },
      orderBy: {
        startTime: "asc",
      },
      select: {
        id: true,
        title: true,
        description: true,
        startTime: true,
        endTime: true,
        location: true,
        maxVolunteers: true,
        categoryId: true,
        eventId: true,
        // Include category for name and pay rate
        category: {
          select: {
            name: true,
            payRate: true,
          },
        },
        // Include event for name
        event: {
          select: {
            name: true,
          },
        },
        // Include assignments to calculate vacancies
        assignments: {
          select: {
            id: true,
            status: true,
          },
          where: {
            status: {
              notIn: ["cancelled", "no_show"],
            },
          },
        },
      },
    });

    // Calculate vacancies for each shift
    const shiftsWithVacancies = shifts.map((shift) => {
      const { assignments, ...shiftData } = shift;
      const vacancies = shift.maxVolunteers - assignments.length;

      return {
        ...shiftData,
        vacancies,
      };
    });

    return NextResponse.json({ shifts: shiftsWithVacancies });
  } catch (error) {
    console.error("Error fetching shifts:", error);
    return NextResponse.json(
      { error: "Failed to fetch shifts" },
      { status: 500 },
    );
  }
}
