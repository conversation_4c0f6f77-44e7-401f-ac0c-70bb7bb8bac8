/**
 * Notification Broadcast API Endpoint
 *
 * This endpoint allows broadcasting notifications to users via SSE.
 * It requires admin privileges to broadcast to all users.
 */

import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser, userHasRole } from "../../../../lib/auth";
import { connectionStore } from "../../../../lib/connectionStore";
import prisma from "../../../../lib/prisma";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';


// POST endpoint to broadcast a notification
export async function POST(req: NextRequest) {
  try {
    // Get current user
    const currentUser = await getCurrentUser(req);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Parse request body
    const body = await req.json();
    const { userId, userIds, broadcast, notification } = body;

    // Validate required fields
    if (!notification) {
      return NextResponse.json(
        { error: "Notification data is required" },
        { status: 400 },
      );
    }

    // Check if user is trying to broadcast to all users
    if (broadcast) {
      // Check if user has admin role (only admins can broadcast to all users)
      const isAdmin = await userHasRole(req, "admin");
      if (!isAdmin) {
        return NextResponse.json(
          {
            error: "Admin privileges required to broadcast to all users",
          },
          { status: 403 },
        );
      }
    }

    // Check if user is trying to send to specific users
    if (userIds && Array.isArray(userIds) && userIds.length > 0) {
      // Check if user has admin role (only admins can send to multiple users)
      const isAdmin = await userHasRole(req, "admin");
      if (!isAdmin) {
        return NextResponse.json(
          {
            error: "Admin privileges required to send to multiple users",
          },
          { status: 403 },
        );
      }
    }

    // If sending to a single user, check if it's the current user or if user has admin privileges
    if (userId && userId !== currentUser.id) {
      const isAdmin = await userHasRole(req, "admin");
      if (!isAdmin) {
        return NextResponse.json(
          {
            error: "Admin privileges required to send to other users",
          },
          { status: 403 },
        );
      }
    }

    // Determine target users
    let targetUserIds: string[] = [];

    if (broadcast) {
      // Get all active users
      const users = await prisma.user.findMany({
        where: {
          status: "active",
        },
        select: {
          id: true,
        },
      });
      targetUserIds = users.map((user) => user.id);
    } else if (userIds && Array.isArray(userIds) && userIds.length > 0) {
      targetUserIds = userIds;
    } else if (userId) {
      targetUserIds = [userId];
    } else {
      // Default to current user
      targetUserIds = [currentUser.id];
    }

    // Send notification to target users
    const results = await Promise.all(
      targetUserIds.map(async (id) => {
        const sentCount = await connectionStore.sendToUser(id, {
          type: "notification",
          notification,
        });
        return { userId: id, sentCount };
      }),
    );

    // Count total connections that received the notification
    const totalSent = results.reduce(
      (sum, result) => sum + result.sentCount,
      0,
    );

    return NextResponse.json({
      success: true,
      message: `Notification sent to ${totalSent} connections across ${targetUserIds.length} users`,
      results,
    });
  } catch (error) {
    console.error("Error broadcasting notification:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
