import fetchClient from "@/lib/fetchClient";

export interface Ship {
  id: string;
  name: string;
  description: string;
  slogan?: string;
  logo?: string;
  tags?: string[];
  status: string;
  createdAt: string;
  updatedAt: string;
  captainId: string;
}

export interface User {
  id: string;
  username: string;
  displayName: string;
  avatar: string;
  email: string;
}

export interface Member {
  id: string;
  role: string;
  status: string;
  joinedAt: string;
  leftAt?: string;
  roleId?: string;
  customRole?: ShipRole;
  user: User;
}

export interface ShipRole {
  id: string;
  name: string;
  description?: string;
  _count?: {
    members: number;
  };
}

export interface JoinRequest {
  id: string;
  createdAt: string;
  message?: string;
  type: string;
  user: User;
}

export interface Invitation {
  id: string;
  createdAt: string;
  message?: string;
  type: string;
  user: User;
}

export interface DashboardData {
  ship: Ship;
  statistics: {
    totalMembers: number;
    pendingRequests: number;
    pendingInvitations: number;
    recentJoins: number;
  };
  members: Member[];
  pendingRequests: JoinRequest[];
  pendingInvitations: Invitation[];
  recentActivity: Array<{
    type: string;
    user: User;
    timestamp: string;
  }>;
}

export interface EventForm {
  id: string;
  name: string;
  description?: string;
  formStructure: any[];
  status: string;
  submissionDeadline?: string;
  event: {
    id: string;
    name: string;
    startDate: string;
    endDate: string;
  };
  hasSubmission: boolean;
  submissionStatus?: "draft" | "submitted" | "reviewed" | "approved" | "rejected";
  submittedAt?: string;
}

export interface FormsData {
  ship: Ship;
  forms: EventForm[];
}

export interface VolunteerRequirement {
  id: string;
  requiredHours: number;
  completedHours: number;
  status: "pending" | "in_progress" | "completed" | "overdue";
  createdAt: string;
  formSubmission: {
    form: {
      name: string;
      event: {
        id: string;
        name: string;
        startDate: string;
      };
    };
  };
  ship: {
    id: string;
    name: string;
  };
}

// API functions
export const getCaptainDashboard = async (): Promise<DashboardData> => {
  return await fetchClient.get("/api/captain/dashboard");
};

export const getCaptainRoles = async (): Promise<ShipRole[]> => {
  const response = await fetchClient.get("/api/captain/roles") as { roles: ShipRole[] };
  return response.roles || [];
};

export const getCaptainForms = async (): Promise<FormsData> => {
  return await fetchClient.get("/api/captain/forms/available");
};

export const getVolunteerRequirements = async (): Promise<VolunteerRequirement[]> => {
  return await fetchClient.get("/api/captain/volunteer-requirements");
};

export const updateMemberRole = async (userId: string, roleId: string | null, roleName: string) => {
  return await fetchClient.put(`/api/captain/members/${userId}/role`, {
    roleId,
    role: roleName
  });
};

export const removeMember = async (userId: string, reason?: string) => {
  return await fetchClient.delete(`/api/captain/members/${userId}`, reason ? {
    body: JSON.stringify({ reason })
  } : undefined);
};

export const createShipRole = async (name: string, description?: string) => {
  return await fetchClient.post("/api/captain/roles", {
    name,
    description
  });
};

export const deleteShipRole = async (roleId: string) => {
  return await fetchClient.delete(`/api/captain/roles/${roleId}`);
};

export const inviteUser = async (userId: string, roleId?: string, roleName?: string, message?: string) => {
  return await fetchClient.post("/api/captain/members/invite", {
    userId,
    roleId,
    message
  });
};

export const cancelInvitation = async (invitationId: string) => {
  return await fetchClient.delete(`/api/captain/invitations/${invitationId}`);
};

export const acceptJoinRequest = async (requestId: string, roleId?: string, roleName?: string) => {
  return await fetchClient.post(`/api/captain/requests/${requestId}`, {
    action: 'accept',
    roleId,
    roleName
  });
};

export const declineJoinRequest = async (requestId: string) => {
  return await fetchClient.post(`/api/captain/requests/${requestId}`, {
    action: 'decline'
  });
};

export const submitForm = async (formId: string, formData: any, files?: Record<string, File>) => {
  return await fetchClient.post(`/api/captain/forms/${formId}/submit`, {
    formData,
    files
  });
};