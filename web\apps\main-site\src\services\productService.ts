/**
 * Product API Service
 * Provides functions for interacting with the Product API endpoints
 */
import fetchClient from "@/lib/fetchClient";

// Types
export interface ProductCategory {
  id: string;
  name: string;
  description: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  _count?: {
    products: number;
  };
}

export interface Product {
  id: string;
  name: string;
  description: string | null;
  shortDescription: string | null;
  price: number;
  image: string | null;
  isActive: boolean;
  affectsCapacity: boolean;
  inventory: number | null;
  isFree: boolean;
  createdAt: string;
  updatedAt: string;
  categoryId: string;
  eventId: string | null;
  category: ProductCategory;
  event?: {
    id: string;
    name: string;
    startDate: string;
    endDate: string;
    capacity: number;
  } | null;
  redemptionCodes?: {
    id: string;
    code: string;
    isActive: boolean;
    createdAt: string;
  }[];
}

// Product Category API Functions

// Get all product categories (public)
export const getProductCategories = async (
  isActive?: boolean,
): Promise<{ categories: ProductCategory[] }> => {
  const params = new URLSearchParams();
  if (isActive !== undefined) params.append("isActive", String(isActive));

  return fetchClient.get<{ categories: ProductCategory[] }>(
    `/api/product-categories?${params.toString()}`,
  );
};

// Get all product categories (sales manager)
export const getSalesProductCategories = async (
  isActive?: boolean,
): Promise<{ categories: ProductCategory[] }> => {
  const params = new URLSearchParams();
  if (isActive !== undefined) params.append("isActive", String(isActive));

  return fetchClient.get<{ categories: ProductCategory[] }>(
    `/api/sales/product-categories?${params.toString()}`,
  );
};

// Get a product category by ID (sales manager)
export const getSalesProductCategory = async (
  id: string,
): Promise<ProductCategory> => {
  const response = await fetchClient.get<{ category: ProductCategory }>(
    `/api/sales/product-categories/${id}`,
  );
  return response.category;
};

// Create a product category (sales manager)
export const createProductCategory = async (data: {
  name: string;
  description?: string;
  isActive?: boolean;
}): Promise<ProductCategory> => {
  const response = await fetchClient.post<{ category: ProductCategory }>(
    "/api/sales/product-categories",
    data,
  );
  return response.category;
};

// Update a product category (sales manager)
export const updateProductCategory = async (
  id: string,
  data: {
    name?: string;
    description?: string;
    isActive?: boolean;
  },
): Promise<ProductCategory> => {
  const response = await fetchClient.put<{ category: ProductCategory }>(
    `/api/sales/product-categories/${id}`,
    data,
  );
  return response.category;
};

// Delete a product category (sales manager)
export const deleteProductCategory = async (
  id: string,
): Promise<{ success: boolean }> => {
  return fetchClient.delete<{ success: boolean }>(
    `/api/sales/product-categories/${id}`,
  );
};

// Product API Functions

// Get all products (public)
export const getProducts = async (filters?: {
  categoryId?: string;
  eventId?: string;
  isActive?: boolean;
}): Promise<Product[]> => {
  const params = new URLSearchParams();
  if (filters?.categoryId) params.append("categoryId", filters.categoryId);
  if (filters?.eventId) params.append("eventId", filters.eventId);
  if (filters?.isActive !== undefined)
    params.append("isActive", String(filters.isActive));

  const response = await fetchClient.get<{ products: Product[] }>(
    `/api/products?${params.toString()}`,
  );
  return response.products;
};

// Get a product by ID (public)
export const getProduct = async (id: string): Promise<{ product: Product }> => {
  return fetchClient.get<{ product: Product }>(`/api/products/${id}`);
};

// Get all products (sales manager)
export const getSalesProducts = async (filters?: {
  categoryId?: string;
  eventId?: string;
  isActive?: boolean;
}): Promise<Product[]> => {
  const params = new URLSearchParams();
  if (filters?.categoryId) params.append("categoryId", filters.categoryId);
  if (filters?.eventId) params.append("eventId", filters.eventId);
  if (filters?.isActive !== undefined)
    params.append("isActive", String(filters.isActive));

  const response = await fetchClient.get<{ products: Product[] }>(
    `/api/sales/products?${params.toString()}`,
  );
  return response.products;
};

// Get a product by ID (sales manager)
export const getSalesProduct = async (id: string): Promise<Product> => {
  const response = await fetchClient.get<{ product: Product }>(
    `/api/sales/products/${id}`,
  );
  return response.product;
};

// Create a product (sales manager)
export const createProduct = async (data: {
  name: string;
  description?: string;
  shortDescription?: string;
  price: number;
  image?: string;
  isActive?: boolean;
  affectsCapacity?: boolean;
  inventory?: number | null;
  categoryId: string;
  eventId?: string | null;
  isFree?: boolean;
}): Promise<Product> => {
  try {
    // Ensure inventory is properly formatted
    const formattedData = {
      ...data,
      // Convert inventory to a number or null
      inventory:
        data.inventory !== undefined
          ? data.inventory === null
            ? null
            : Number(data.inventory)
          : null,
      // Ensure price is a number
      price: Number(data.price),
      // Ensure eventId is a string or null
      eventId: data.eventId || null,
    };

    const response = await fetchClient.post<{ product: Product }>(
      "/api/sales/products",
      formattedData,
    );
    return response.product;
  } catch (error) {
    console.error("Error in createProduct service:", error);
    throw error;
  }
};

// Update a product (sales manager)
export const updateProduct = async (
  id: string,
  data: {
    name?: string;
    description?: string;
    shortDescription?: string;
    price?: number;
    image?: string;
    isActive?: boolean;
    affectsCapacity?: boolean;
    inventory?: number | null;
    categoryId?: string;
    eventId?: string | null;
    isFree?: boolean;
  },
): Promise<Product> => {
  try {
    // Format the data to ensure proper types
    const formattedData = {
      ...data,
      // Convert inventory to a number or null if provided
      inventory:
        data.inventory !== undefined
          ? data.inventory === null
            ? null
            : Number(data.inventory)
          : undefined,
      // Ensure price is a number if provided
      price: data.price !== undefined ? Number(data.price) : undefined,
      // Ensure eventId is a string or null if provided
      eventId: data.eventId === undefined ? undefined : data.eventId || null,
    };

    const response = await fetchClient.put<{ product: Product }>(
      `/api/sales/products/${id}`,
      formattedData,
    );
    return response.product;
  } catch (error) {
    console.error("Error in updateProduct service:", error);
    throw error;
  }
};

// Delete a product (sales manager)
export const deleteProduct = async (
  id: string,
): Promise<{ success: boolean }> => {
  return fetchClient.delete<{ success: boolean }>(`/api/sales/products/${id}`);
};
