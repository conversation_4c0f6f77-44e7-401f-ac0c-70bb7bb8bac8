import { NextRequest, NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import prisma from "../../../../lib/prisma";
import jwt from "jsonwebtoken";

const JWT_SECRET = process.env.JWT_SECRET || "your-development-secret-key";

export const OPTIONS = async (req: NextRequest) => {
  // Handle OPTIONS request for CORS preflight
  const response = new NextResponse(null, { status: 204 });

  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT, DELETE, OPTIONS",
  );
  response.headers.set(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization",
  );

  return response;
};

export const POST = async (req: NextRequest) => {
  try {
    const { username, email, password } = await req.json();

    // Validate input
    if (!username || !email || !password) {
      return NextResponse.json(
        { error: "Username, email and password are required" },
        { status: 400 },
      );
    }

    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [{ email }, { username }],
      },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "User with this email or username already exists" },
        { status: 409 },
      );
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create user in database with credentials
    const user = await prisma.user.create({
      data: {
        username,
        email,
        displayName: username, // Default to username initially
        passwordHash: hashedPassword, // Keep this for backward compatibility
        credentials: {
          create: {
            type: "email",
            identifier: email,
            passwordHash: hashedPassword,
          },
        },
      },
    });

    // Create JWT token
    const token = jwt.sign({ id: user.id, email: user.email }, JWT_SECRET, {
      expiresIn: "7d",
    });

    // Return user data (excluding password) and token
    return NextResponse.json({
      user: {
        id: user.id,
        username: user.username,
        displayName: user.displayName,
        email: user.email,
        avatar: user.avatar,
        balance: user.balance,
        isEmailVerified: user.isEmailVerified,
        preferences: {
          defaultView: user.defaultView,
          notifications: {
            transfers: user.notifyTransfers,
            deposits: user.notifyDeposits,
            withdrawals: user.notifyWithdrawals,
            newsAndEvents: user.notifyNewsEvents,
          },
        },
        connectedAccounts: {
          discord: user.discordConnected,
          discordId: user.discordId,
          facebook: user.facebookConnected,
          facebookId: user.facebookId,
        },
        merchant: {
          status: user.merchantStatus,
          merchantId: user.merchantId,
          slug: user.merchantSlug,
        },
        auctions: {
          hasCreated: user.hasCreatedAuctions,
          auctionCount: user.auctionCount,
        },
        roles: {
          admin: user.isAdmin,
          editor: user.isEditor,
          banker: user.isBanker,
          chatModerator: user.isChatModerator,
          volunteerCoordinator: user.isVolunteerCoordinator,
          leadManager: user.isLeadManager,
        },
      },
      token,
    });
  } catch (error: any) {
    console.error("Registration error:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error?.message },
      { status: 500 },
    );
  }
};
