"use client";

import React, { useState, useEffect } from "react";
import { UserSearchInput } from "./UserSearchInput";
import {
  VolunteerCategory,
  useCreateVolunteerCategory,
  useUpdateVolunteerCategory,
} from "@/hooks/useVolunteerCategories";

interface CategoryFormProps {
  eventId: string;
  category?: VolunteerCategory | null;
  onSuccess: () => void;
  onCancel: () => void;
}

export const CategoryForm: React.FC<CategoryFormProps> = ({
  eventId,
  category,
  onSuccess,
  onCancel,
}) => {
  const isEditing = !!category;

  // Form state
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [payRate, setPayRate] = useState("");
  const [leadManagerId, setLeadManagerId] = useState<string | null>(null);

  // Error state
  const [errors, setErrors] = useState<{
    name?: string;
    payRate?: string;
    general?: string;
  }>({});

  // Mutations
  const createMutation = useCreateVolunteerCategory(eventId);
  const updateMutation = useUpdateVolunteerCategory(
    category?.id || null,
    eventId,
  );

  // Set initial form values when editing
  useEffect(() => {
    if (category) {
      setName(category.name);
      setDescription(category.description || "");
      setPayRate(category.payRate ? category.payRate.toString() : "");
      setLeadManagerId(category.leadManager?.id || null);
    }
  }, [category]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const newErrors: {
      name?: string;
      payRate?: string;
      general?: string;
    } = {};

    if (!name.trim()) {
      newErrors.name = "Category name is required";
    }

    if (payRate && !/^\d+(\.\d{1,2})?$/.test(payRate)) {
      newErrors.payRate = "Pay rate must be a valid number";
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Clear errors
    setErrors({});

    try {
      const formData = {
        name,
        description: description || undefined,
        payRate: payRate ? parseFloat(payRate) : undefined,
        leadManagerId: leadManagerId || undefined,
      };

      if (isEditing) {
        await updateMutation.mutateAsync(formData);
      } else {
        await createMutation.mutateAsync(formData);
      }

      // Reset form and notify parent
      resetForm();
      onSuccess();
    } catch (error) {
      console.error("Error saving category:", error);
      setErrors({
        general:
          error instanceof Error ? error.message : "Failed to save category",
      });
    }
  };

  // Reset form
  const resetForm = () => {
    setName("");
    setDescription("");
    setPayRate("");
    setLeadManagerId(null);
    setErrors({});
  };

  // Handle lead manager selection
  const handleLeadManagerSelect = (user: any) => {
    setLeadManagerId(user?.id || null);
  };

  return (
    <div className="bg-secondary-light rounded-lg shadow-md p-6 border border-gray-600">
      <h2 className="text-xl font-bold text-white mb-4">
        {isEditing ? "Edit Category" : "Create New Category"}
      </h2>

      {errors.general && (
        <div className="mb-4 p-3 bg-accent bg-opacity-20 border border-accent rounded-md">
          <p className="text-accent">{errors.general}</p>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="space-y-4">
          {/* Category Name */}
          <div>
            <label
              htmlFor="name"
              className="block text-sm font-medium text-white mb-1"
            >
              Category Name *
            </label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className={`w-full px-4 py-2 bg-secondary border ${
                errors.name ? "border-accent" : "border-gray-600"
              } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary`}
              placeholder="Enter category name"
              disabled={createMutation.isPending || updateMutation.isPending}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-accent">{errors.name}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label
              htmlFor="description"
              className="block text-sm font-medium text-white mb-1"
            >
              Description
            </label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
              className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Enter category description"
              disabled={createMutation.isPending || updateMutation.isPending}
            />
          </div>

          {/* Pay Rate */}
          <div>
            <label
              htmlFor="payRate"
              className="block text-sm font-medium text-white mb-1"
            >
              Pay Rate (£ per hour)
            </label>
            <input
              type="text"
              id="payRate"
              value={payRate}
              onChange={(e) => setPayRate(e.target.value)}
              className={`w-full px-4 py-2 bg-secondary border ${
                errors.payRate ? "border-accent" : "border-gray-600"
              } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary`}
              placeholder="Enter pay rate (e.g., 10.50)"
              disabled={createMutation.isPending || updateMutation.isPending}
            />
            {errors.payRate && (
              <p className="mt-1 text-sm text-accent">{errors.payRate}</p>
            )}
          </div>

          {/* Lead Manager */}
          <div>
            <UserSearchInput
              selectedUserId={leadManagerId}
              onUserSelect={handleLeadManagerSelect}
              label="Lead Manager"
              placeholder="Search for a lead manager..."
              disabled={createMutation.isPending || updateMutation.isPending}
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 bg-secondary hover:bg-secondary-dark text-white rounded-md transition-colors"
              disabled={createMutation.isPending || updateMutation.isPending}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-md transition-colors flex items-center"
              disabled={createMutation.isPending || updateMutation.isPending}
            >
              {(createMutation.isPending || updateMutation.isPending) && (
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              )}
              {isEditing ? "Update Category" : "Create Category"}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};
