import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";

interface Params {
  params: {
    id: string;
  };
}

export async function GET(request: Request, { params }: Params) {
  const { id } = params;

  try {
    const article = await prisma.newsArticle.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    if (!article) {
      return NextResponse.json({ error: "Article not found" }, { status: 404 });
    }

    // Increment view count
    await prisma.newsArticle.update({
      where: { id },
      data: { views: { increment: 1 } },
    });

    return NextResponse.json(article);
  } catch (error) {
    console.error("Error fetching article:", error);
    return NextResponse.json(
      { error: "Failed to fetch article" },
      { status: 500 },
    );
  }
}

export async function PUT(request: Request, { params }: Params) {
  const { id } = params;

  try {
    const { title, content, excerpt, categoryId, image, status, featured } =
      await request.json();

    // Validate required fields
    if (!title || !content || !categoryId) {
      return NextResponse.json(
        {
          error:
            "Missing required fields: title, content, and categoryId are required",
        },
        { status: 400 },
      );
    }

    // Find the existing article
    const existingArticle = await prisma.newsArticle.findUnique({
      where: { id },
    });

    if (!existingArticle) {
      return NextResponse.json({ error: "Article not found" }, { status: 404 });
    }

    // Check if we need to generate a new slug (only if title changed)
    let slug = existingArticle.slug;
    if (title !== existingArticle.title) {
      const baseSlug = title
        .toLowerCase()
        .replace(/[^\w\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-");

      // Check if the new slug would conflict with others
      const slugExists = await prisma.newsArticle.findFirst({
        where: {
          slug: baseSlug,
          id: { not: id },
        },
      });

      slug = slugExists
        ? `${baseSlug}-${Date.now().toString().slice(-6)}`
        : baseSlug;
    }

    // Only set publishedAt if status is changing from draft to published
    const wasPublished = existingArticle.status === "published";
    const willBePublished = status === "published";
    const publishedAt =
      !wasPublished && willBePublished
        ? new Date()
        : existingArticle.publishedAt;

    // Update the article
    const updatedArticle = await prisma.newsArticle.update({
      where: { id },
      data: {
        title,
        content,
        excerpt: excerpt || title,
        categoryId,
        image,
        slug,
        status,
        featured,
        publishedAt,
        updatedAt: new Date(),
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    return NextResponse.json(updatedArticle);
  } catch (error) {
    console.error("Error updating article:", error);
    return NextResponse.json(
      { error: "Failed to update article" },
      { status: 500 },
    );
  }
}

export async function DELETE(request: Request, { params }: Params) {
  const { id } = params;

  try {
    // Check if article exists
    const article = await prisma.newsArticle.findUnique({
      where: { id },
    });

    if (!article) {
      return NextResponse.json({ error: "Article not found" }, { status: 404 });
    }

    // Delete the article
    await prisma.newsArticle.delete({
      where: { id },
    });

    return NextResponse.json({ message: "Article deleted successfully" });
  } catch (error) {
    console.error("Error deleting article:", error);
    return NextResponse.json(
      { error: "Failed to delete article" },
      { status: 500 },
    );
  }
}
