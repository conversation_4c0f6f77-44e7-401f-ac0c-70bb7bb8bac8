"use client";
import Link from "next/link";

import React, { useState } from "react";
import { DashboardLayout } from "../../../../components/bank";
import {
  useBankUser,
  useRecentWithdrawals,
  useCreateTransaction,
  useCancelWithdrawal,
} from "../../../../hooks/useBank";
import { toast } from "react-hot-toast";

export default function WithdrawPage() {
  const [amount, setAmount] = useState("");
  const [note, setNote] = useState("");
  const [nationAccount, setNationAccount] = useState("");
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false);
  const [selectedWithdrawalId, setSelectedWithdrawalId] = useState<
    string | null
  >(null);
  const [expandedWithdrawals, setExpandedWithdrawals] = useState<
    Record<string, boolean>
  >({});

  // Fetch user data and withdrawals
  const { data: bankUser, isLoading: isLoadingUser } = useBankUser();
  const { data: withdrawals = [], isLoading: isLoadingWithdrawals } =
    useRecentWithdrawals(20, true);
  const createTransaction = useCreateTransaction();
  const cancelWithdrawal = useCancelWithdrawal();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate amount
    if (!amount || parseFloat(amount) <= 0) {
      toast.error("Please enter a valid amount");
      return;
    }

    // Validate nation account
    if (!nationAccount) {
      toast.error("Please enter your nation account");
      return;
    }

    setShowConfirmation(true);
  };

  const handleConfirm = async () => {
    try {
      await createTransaction.mutateAsync({
        type: "withdrawal",
        amount: parseFloat(amount),
        description: `Withdrawal to ${nationAccount}`,
        note: note || undefined,
      });

      // Show success message
      toast.success(
        `Withdrawal request for NS ${amount} submitted successfully`,
      );

      // Reset form
      setAmount("");
      setNote("");
      setNationAccount("");
      setShowConfirmation(false);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "An error occurred";
      toast.error(errorMessage);
      setShowConfirmation(false);
    }
  };

  const handleCancel = () => {
    setShowConfirmation(false);
  };

  const handleCancelWithdrawal = (id: string) => {
    setSelectedWithdrawalId(id);
    setShowCancelConfirmation(true);
  };

  const confirmCancelWithdrawal = async () => {
    if (!selectedWithdrawalId) return;

    try {
      await cancelWithdrawal.mutateAsync(selectedWithdrawalId);
      toast.success("Withdrawal cancelled successfully");
      setShowCancelConfirmation(false);
      setSelectedWithdrawalId(null);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "An error occurred";
      toast.error(errorMessage);
      setShowCancelConfirmation(false);
    }
  };

  const closeCancelConfirmation = () => {
    setShowCancelConfirmation(false);
    setSelectedWithdrawalId(null);
  };

  const toggleWithdrawalDetails = (id: string) => {
    setExpandedWithdrawals((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  return (
    <DashboardLayout>
      <div className="mb-3">
        <Link
          href="/bank/dashboard"
          className="text-primary hover:text-primary-light"
        >
          &larr; Back to Dashboard
        </Link>
      </div>
      <div className="bg-secondary-light rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-2xl font-bold text-white mb-6">
          Request Withdrawal
        </h2>

        <div className="mb-6">
          <p className="text-gray-400">
            Current Balance:{" "}
            <span className="font-bold text-success">
              {isLoadingUser
                ? "Loading..."
                : `NS ${bankUser?.balance.toFixed(0)}`}
            </span>
          </p>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label
                htmlFor="amount"
                className="block text-white font-medium mb-2"
              >
                Amount
              </label>
              <div className="relative flex items-center">
                <span className="absolute left-3 text-gray-400 select-none pointer-events-none">
                  NS
                </span>
                <input
                  type="number"
                  id="amount"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="0"
                  min="1"
                  step="1"
                  max={bankUser?.balance || 0}
                  required
                />
              </div>
              <p className="text-xs text-gray-400 mt-1">
                Maximum withdrawal:{" "}
                {isLoadingUser
                  ? "Loading..."
                  : `NS ${bankUser?.balance.toFixed(0)}`}
              </p>
            </div>

            <div>
              <label
                htmlFor="nationAccount"
                className="block text-white font-medium mb-2"
              >
                Nation account
              </label>
              <input
                type="text"
                id="nationAccount"
                value={nationAccount}
                onChange={(e) => setNationAccount(e.target.value)}
                className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Enter your Nation account"
                required
              />
            </div>

            <div>
              <label
                htmlFor="note"
                className="block text-white font-medium mb-2"
              >
                Note <span className="text-gray-400 text-sm">(Optional)</span>
              </label>
              <input
                type="text"
                id="note"
                value={note}
                onChange={(e) => setNote(e.target.value)}
                className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Additional information about this withdrawal"
              />
            </div>

            <div className="flex justify-end space-x-4 pt-4">
              <button
                type="button"
                onClick={() => {
                  setAmount("");
                  setNote("");
                  setNationAccount("");
                }}
                className="px-4 py-2 border border-gray-600 rounded-md text-white hover:bg-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
              >
                Submit Withdrawal
              </button>
            </div>
          </div>
        </form>
      </div>

      <div className="bg-secondary-light rounded-lg shadow-md p-6">
        <h2 className="text-lg font-semibold text-white mb-4">Withdrawals</h2>

        {isLoadingWithdrawals ? (
          <div className="space-y-4">
            <div className="animate-pulse h-16 bg-gray-600 rounded w-full"></div>
            <div className="animate-pulse h-16 bg-gray-600 rounded w-full"></div>
            <div className="animate-pulse h-16 bg-gray-600 rounded w-full"></div>
          </div>
        ) : withdrawals.length > 0 ? (
          <div className="divide-y divide-gray-600">
            {withdrawals.map((withdrawal) => (
              <div
                key={withdrawal.id}
                className="py-3 border-b border-gray-600 last:border-0"
              >
                <div
                  className="grid grid-cols-4 md:grid-cols-2 items-center cursor-pointer hover:bg-secondary-light transition-colors"
                  onClick={() => toggleWithdrawalDetails(withdrawal.id)}
                >
                  {/* Date */}
                  {/* Left group: Date and Status */}
                  <div className="col-span-2 md:col-span-1 flex items-center space-x-4">
                    <div className="text-sm text-gray-400">
                      {new Date(withdrawal.createdAt).toLocaleDateString()}
                    </div>

                    <div
                      className={`text-sm ${
                        withdrawal.status === "pending"
                          ? "text-warning"
                          : withdrawal.status === "completed"
                          ? "text-success"
                          : "text-error"
                      }`}
                    >
                      {withdrawal.status.charAt(0).toUpperCase() +
                        withdrawal.status.slice(1)}
                    </div>
                  </div>

                  {/* Right group: Cancel button and Amount */}
                  <div className="col-span-2 md:col-span-1 flex items-center space-x-4 justify-end">
                    {/* Cancel button for pending withdrawals */}
                    <div onClick={(e) => e.stopPropagation()}>
                      {withdrawal.status === "pending" ? (
                        <button
                          onClick={() => handleCancelWithdrawal(withdrawal.id)}
                          className="px-2 py-1 bg-error text-white text-xs rounded hover:bg-error-dark transition-colors"
                        >
                          Cancel
                        </button>
                      ) : (
                        <div className="w-14 opacity-0">
                          {/* Placeholder for spacing */}
                        </div>
                      )}
                    </div>

                    {/* Amount */}
                    <div className="font-bold text-error flex items-center">
                      <span>NS {Math.abs(withdrawal.amount).toFixed(0)}</span>
                      <svg
                        className={`w-4 h-4 ml-2 transition-transform ${
                          expandedWithdrawals[withdrawal.id]
                            ? "transform rotate-180"
                            : ""
                        }`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Expandable details */}
                {expandedWithdrawals[withdrawal.id] && (
                  <div className="mt-2 text-sm text-gray-400 pl-4 py-2 border-t border-gray-700">
                    <div>Description: {withdrawal.description}</div>
                    {withdrawal.note && <div>Note: {withdrawal.note}</div>}
                    {withdrawal.processedAt && (
                      <div>
                        Processed:{" "}
                        {new Date(withdrawal.processedAt).toLocaleString()}
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-6 text-gray-400">
            No withdrawals found.
          </div>
        )}
      </div>

      {/* Cancel Withdrawal Confirmation Modal */}
      {showCancelConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-secondary-light rounded-lg p-6 max-w-md w-full">
            <h3 className="text-xl font-bold text-white mb-4">
              Cancel Withdrawal
            </h3>
            <p className="mb-4 text-white">
              Are you sure you want to cancel this withdrawal request? The funds
              will be returned to your account.
            </p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={closeCancelConfirmation}
                className="px-4 py-2 border border-gray-600 rounded-md text-white hover:bg-secondary"
              >
                No, Keep Request
              </button>
              <button
                onClick={confirmCancelWithdrawal}
                className="px-4 py-2 bg-error text-white rounded-md hover:bg-error-dark"
              >
                Yes, Cancel Withdrawal
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-secondary-light rounded-lg p-6 max-w-md w-full">
            <h3 className="text-xl font-bold text-white mb-4">
              Confirm Withdrawal
            </h3>
            <p className="mb-4 text-white">
              You are about to withdraw{" "}
              <span className="font-bold">NS {amount}</span>.
            </p>
            <p className="mb-4 text-white">Nation account: {nationAccount}</p>
            {note && <p className="mb-4 text-white">Note: {note}</p>}
            <div className="flex justify-end space-x-4">
              <button
                onClick={handleCancel}
                className="px-4 py-2 border border-gray-600 rounded-md text-white hover:bg-secondary"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirm}
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
}
