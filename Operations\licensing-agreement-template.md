# Software Licensing Agreement Template

## Community Banking Platform License Agreement

**This Agreement is entered into between:**
- **Licensor**: [Your Name/Company] ("Developer")
- **Licensee**: [Community Name] ("Community")

### 1. GRANT OF LICENSE

**1.1 License Grant**
Developer grants Community a non-exclusive, non-transferable license to use the Community Banking Platform software ("Software") for Community's internal operations.

**1.2 Scope of License**
- Right to use the Software for Community's members and activities
- Right to customize branding, colors, and community-specific content
- Right to modify configuration settings and user-facing content
- Right to host the Software on Community's chosen infrastructure

**1.3 Restrictions**
- Community may NOT resell, redistribute, or sublicense the Software
- Community may NOT reverse engineer or attempt to extract source code
- Community may NOT use the Software for other organizations without separate license
- Community may NOT claim ownership of the underlying Software technology

### 2. DEVELOPER RIGHTS RESERVED

**2.1 Intellectual Property**
Developer retains all rights, title, and interest in the Software, including:
- Source code and underlying architecture
- Proprietary algorithms and business logic
- Database schema and data structures
- All improvements and modifications made by Developer

**2.2 Right to License to Others**
Developer explicitly reserves the right to:
- License the Software to other communities and organizations
- Create similar or competing software solutions
- Use knowledge gained from this project for other clients
- Market and sell the Software without restriction

**2.3 No Exclusivity**
This license is NON-EXCLUSIVE. Developer may license identical or similar software to any other party without notice to or approval from Community.

### 3. DELIVERABLES AND CUSTOMIZATION

**3.1 Base Package Includes**
- Complete Software installation configured for Community
- Community-specific branding and visual customization
- Database setup with initial configuration
- Basic administrator training (as specified in service tier)
- Documentation package for Community's use

**3.2 Customization Scope**
- Visual branding (logos, colors, community name)
- Initial configuration settings
- Community-specific terminology and content
- Basic feature configuration

**3.3 Additional Customization**
Any customization beyond the base package will be quoted separately and requires a separate agreement.

### 4. SUPPORT AND MAINTENANCE

**4.1 Initial Support Period**
Developer will provide support for [X months] from delivery date, including:
- Bug fixes for Software defects
- Technical support for installation and configuration issues
- Basic user training and documentation

**4.2 Ongoing Support (Optional)**
After initial support period, ongoing support is available at Developer's standard rates:
- Technical support: $150/hour
- Feature development: $150/hour
- Emergency support: $200/hour

**4.3 Community Responsibilities**
Community is responsible for:
- Hosting infrastructure and costs
- Regular backups of data
- Basic system administration
- User support and training beyond initial period

### 5. PAYMENT TERMS

**5.1 License Fee**
Total license fee: $[AMOUNT]
Payment schedule: [Payment terms]

**5.2 Payment Methods**
- Bank transfer/wire
- Business check
- Cryptocurrency (if agreed)
- PayPal/Stripe (for smaller amounts)

**5.3 Late Payment**
Late payments incur 1.5% monthly interest charge.

### 6. WARRANTIES AND DISCLAIMERS

**6.1 Limited Warranty**
Developer warrants that the Software will perform substantially as described for 90 days from delivery.

**6.2 Disclaimer**
EXCEPT AS EXPRESSLY SET FORTH ABOVE, THE SOFTWARE IS PROVIDED "AS IS" WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED.

**6.3 Limitation of Liability**
Developer's total liability shall not exceed the license fee paid by Community.

### 7. DATA AND PRIVACY

**7.1 Data Ownership**
Community owns all data entered into their instance of the Software.

**7.2 Data Privacy**
Developer will not access Community's data except as necessary for support services with Community's permission.

**7.3 Data Security**
Community is responsible for implementing appropriate security measures for their data.

### 8. TERMINATION

**8.1 Termination Rights**
Either party may terminate this agreement with 30 days written notice.

**8.2 Effect of Termination**
Upon termination:
- Community retains right to use current Software version
- Developer's support obligations cease
- Community remains responsible for any outstanding payments

### 9. GENERAL PROVISIONS

**9.1 Governing Law**
This agreement is governed by [Your State/Country] law.

**9.2 Entire Agreement**
This agreement constitutes the entire agreement between the parties.

**9.3 Modifications**
Modifications must be in writing and signed by both parties.

**9.4 Severability**
If any provision is unenforceable, the remainder of the agreement remains in effect.

---

**SIGNATURES**

**Developer:**
Signature: _________________________
Name: [Your Name]
Date: _____________________________

**Community Representative:**
Signature: _________________________
Name: _____________________________
Title: ____________________________
Date: _____________________________

---

## SCHEDULE A: SERVICE TIERS

### Tier 1 - Essential ($4,000)
- Software license and installation
- Basic branding customization
- 30 days email support
- Basic documentation

### Tier 2 - Professional ($8,000)
- Everything in Tier 1
- Advanced branding and customization
- 90 days priority support
- Administrator training (10 hours)
- Deployment assistance

### Tier 3 - Premium ($12,000)
- Everything in Tier 2
- Extended training (20 hours)
- 6 months priority support
- Custom feature modifications (up to 20 hours)
- Ongoing consultation access

---

*This template should be reviewed by a qualified attorney before use.*
