import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";

export const dynamic = 'force-dynamic';

// GET /api/admin/events/[id]/stats - Get comprehensive event statistics
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if user is authenticated and has admin or sales manager role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 }
      );
    }

    const hasAdminRole = await userHasRole(req, "admin");
    const hasSalesRole = await userHasRole(req, "salesManager");
    
    if (!hasAdminRole && !hasSalesRole) {
      return NextResponse.json(
        { error: "Unauthorized - Admin or Sales Manager role required" },
        { status: 403 }
      );
    }

    const eventId = params.id;

    // Verify event exists and get basic info
    const event = await prisma.event.findUnique({
      where: { id: eventId },
      include: {
        category: {
          select: { id: true, name: true, color: true }
        }
      }
    });

    if (!event) {
      return NextResponse.json(
        { error: "Event not found" },
        { status: 404 }
      );
    }

    // Get all products associated with this event
    const eventProducts = await prisma.product.findMany({
      where: { eventId: eventId },
      include: {
        category: true,
        tickets: {
          select: {
            id: true,
            status: true
          }
        }
      }
    });

    if (eventProducts.length === 0) {
      // Return basic event info with zero stats if no products
      return NextResponse.json({
        event: {
          id: event.id,
          name: event.name,
          startDate: event.startDate,
          endDate: event.endDate,
          status: event.status,
          category: event.category
        },
        overview: {
          totalRevenue: 0,
          totalUnitsSold: 0,
          totalRefunds: 0,
          uniqueCustomers: 0,
          conversionRate: 0
        },
        products: [],
        salesTimeline: [],
        topCustomers: [],
        recentTransactions: []
      });
    }

    const productIds = eventProducts.map((p: any) => p.id);

    // Get all order items for event products with order and user info
    const orderItems = await prisma.orderItem.findMany({
      where: {
        productId: { in: productIds },
        order: {
          status: { in: ['paid', 'fulfilled'] }
        }
      },
      include: {
        product: {
          select: { id: true, name: true, price: true }
        },
        order: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                email: true,
                displayName: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Calculate overview statistics
    const totalRevenue = orderItems.reduce((sum, item) => {
      return sum + (item.quantity * item.price);
    }, 0);

    const totalUnitsSold = orderItems.reduce((sum, item) => {
      return sum + item.quantity;
    }, 0);

    // Get unique customers
    const uniqueCustomerIds = new Set(orderItems.map(item => item.order.user.id));
    const uniqueCustomers = uniqueCustomerIds.size;

    // Calculate product breakdown
    const productStats = eventProducts.map(product => {
      const productOrderItems = orderItems.filter(item => item.productId === product.id);
      const unitsSold = productOrderItems.reduce((sum, item) => sum + item.quantity, 0);
      const revenue = productOrderItems.reduce((sum, item) => sum + (item.quantity * item.price), 0);
      
      // Count tickets by status
      const availableTickets = product.tickets.filter(ticket => ticket.status === 'AVAILABLE').length;
      const heldTickets = product.tickets.filter(ticket => ticket.status === 'HELD').length;
      const soldTickets = product.tickets.filter(ticket => ticket.status === 'SOLD').length;

      return {
        id: product.id,
        name: product.name,
        price: product.price,
        unitsSold,
        revenue,
        availableTickets,
        heldTickets,
        soldTickets,
        totalTickets: product.tickets.length
      };
    });

    // Calculate daily sales timeline for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const timelineItems = orderItems.filter(item => 
      new Date(item.createdAt) >= thirtyDaysAgo
    );

    // Group by date
    const salesByDate = new Map();
    timelineItems.forEach(item => {
      const date = new Date(item.createdAt).toISOString().split('T')[0];
      const existing = salesByDate.get(date) || { revenue: 0, unitsSold: 0 };
      salesByDate.set(date, {
        revenue: existing.revenue + (item.quantity * item.price),
        unitsSold: existing.unitsSold + item.quantity
      });
    });

    const salesTimeline = Array.from(salesByDate.entries())
      .map(([date, data]) => ({ date, ...data }))
      .sort((a, b) => a.date.localeCompare(b.date));

    // Calculate top customers
    const customerPurchases = orderItems.reduce((acc, item) => {
      const userId = item.order.user.id;
      if (!acc[userId]) {
        acc[userId] = {
          user: item.order.user,
          totalSpent: 0,
          itemsPurchased: 0,
          orderCount: 0
        };
      }
      
      acc[userId].totalSpent += (item.quantity * item.price);
      acc[userId].itemsPurchased += item.quantity;
      acc[userId].orderCount += 1;
      
      return acc;
    }, {} as Record<string, any>);

    const topCustomers = Object.values(customerPurchases)
      .sort((a: any, b: any) => b.totalSpent - a.totalSpent)
      .slice(0, 10);

    // Recent transactions (last 20)
    const recentTransactions = orderItems.slice(0, 20).map(item => ({
      id: item.id,
      orderNumber: item.order.orderNumber,
      customer: item.order.user,
      productName: item.product.name,
      quantity: item.quantity,
      unitPrice: item.price,
      total: item.quantity * item.price,
      date: item.order.createdAt,
      status: item.order.status
    }));

    // Calculate refunds (simplified - would need refund tracking in real system)
    const totalRefunds = 0; // TODO: Implement refund tracking

    // Calculate conversion rate (would need view/interest tracking)
    const conversionRate = 0; // TODO: Implement conversion tracking

    return NextResponse.json({
      event: {
        id: event.id,
        name: event.name,
        startDate: event.startDate,
        endDate: event.endDate,
        status: event.status,
        category: event.category
      },
      overview: {
        totalRevenue,
        totalUnitsSold,
        totalRefunds,
        uniqueCustomers,
        conversionRate
      },
      products: productStats,
      salesTimeline,
      topCustomers,
      recentTransactions
    });

  } catch (error) {
    console.error("Error fetching event stats:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}