"use client";

import React, { useState, useRef, useEffect } from "react";
import { Modal, Button } from "@bank-of-styx/ui";
import ReactCrop, {
  Crop,
  PixelCrop,
  centerCrop,
  makeAspectCrop,
} from "react-image-crop";
import "react-image-crop/dist/ReactCrop.css";
import { uploadAvatar } from "@/services/avatarService";

interface AvatarUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (croppedImageUrl: string) => void;
  currentAvatar?: string;
}

function centerAspectCrop(
  mediaWidth: number,
  mediaHeight: number,
  aspect: number,
) {
  return centerCrop(
    makeAspectCrop(
      {
        unit: "%",
        width: 90,
      },
      aspect,
      mediaWidth,
      mediaHeight,
    ),
    mediaWidth,
    mediaHeight,
  );
}

export const AvatarUploadModal: React.FC<AvatarUploadModalProps> = ({
  isOpen,
  onClose,
  onSave,
  currentAvatar,
}) => {
  const [imgSrc, setImgSrc] = useState<string | null>(null);
  const imgRef = useRef<HTMLImageElement>(null);
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const [error, setError] = useState<string>("");
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setImgSrc(null);
      setCrop(undefined);
      setCompletedCrop(undefined);
      setError("");
      setIsUploading(false);
      setUploadError(null);
    }
  }, [isOpen]);

  // When an image is loaded, set up the initial crop
  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;
    setCrop(centerAspectCrop(width, height, 1)); // 1:1 aspect ratio for circular avatar
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];

      // Validate file type
      if (!file.type.match("image.*")) {
        setError("Please select an image file (JPEG, PNG, etc.)");
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setError("Image size should be less than 5MB");
        return;
      }

      setError("");

      const reader = new FileReader();
      reader.onload = () => {
        setImgSrc(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Generate the cropped image as a File object
  const getCroppedImg = (): Promise<File | null> => {
    return new Promise((resolve) => {
      if (!imgRef.current || !completedCrop) {
        resolve(null);
        return;
      }

      const image = imgRef.current;
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");

      if (!ctx) {
        resolve(null);
        return;
      }

      // Set canvas size to a smaller fixed size for avatars (100x100 pixels)
      const maxSize = 100;
      canvas.width = maxSize;
      canvas.height = maxSize;

      // Draw the cropped image onto the canvas, resizing it to the fixed size
      ctx.drawImage(
        image,
        completedCrop.x,
        completedCrop.y,
        completedCrop.width,
        completedCrop.height,
        0,
        0,
        maxSize,
        maxSize,
      );

      // Convert canvas to blob
      canvas.toBlob(
        (blob) => {
          if (!blob) {
            resolve(null);
            return;
          }

          // Create a File object from the blob
          const file = new File([blob], `avatar-${Date.now()}.jpg`, {
            type: "image/jpeg",
          });
          resolve(file);
        },
        "image/jpeg",
        0.9,
      ); // 90% quality
    });
  };

  // Handle save button click
  const handleSave = async () => {
    setIsUploading(true);
    setUploadError(null);

    try {
      const croppedImageFile = await getCroppedImg();

      if (!croppedImageFile) {
        throw new Error("Failed to crop image");
      }

      // Upload the cropped image
      const uploadedImage = await uploadAvatar(croppedImageFile);

      // Pass the URL to the parent component
      onSave(uploadedImage.url);
      onClose();
    } catch (error) {
      console.error("Error saving avatar:", error);
      setUploadError(
        error instanceof Error ? error.message : "Failed to save avatar",
      );
    } finally {
      setIsUploading(false);
    }
  };

  // Trigger file input click
  const handleBrowseClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Change Avatar"
      size="md"
      footer={
        <div className="flex justify-end space-x-3">
          <Button variant="secondary" onClick={onClose} disabled={isUploading}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            disabled={!completedCrop || !imgSrc || isUploading}
          >
            {isUploading ? (
              <span className="flex items-center">
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Uploading...
              </span>
            ) : (
              "Save"
            )}
          </Button>
        </div>
      }
    >
      <div className="p-4 bg-secondary-light">
        {(error || uploadError) && (
          <div className="mb-4 p-3 text-sm rounded-lg bg-accent bg-opacity-20 text-accent border border-accent">
            {error || uploadError}
          </div>
        )}

        <div className="mb-4">
          <div className="flex items-center mb-4">
            <label className="flex-1 flex items-center px-4 py-2 bg-secondary border border-gray-600 rounded-md cursor-pointer hover:bg-hover">
              <span className="text-gray-400">
                {imgSrc ? "Change Image" : "Choose Image"}
              </span>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                className="hidden"
                accept="image/*"
              />
            </label>
            <Button
              type="button"
              variant="secondary"
              size="md"
              className="ml-2"
              onClick={handleBrowseClick}
            >
              Browse
            </Button>
          </div>
          <p className="text-xs text-gray-400">
            Select a square image for best results. You can adjust the crop area
            after uploading. The image will be resized to 100x100 pixels.
          </p>
        </div>

        {imgSrc ? (
          <div className="flex flex-col items-center">
            <div className="mb-4 max-w-full overflow-hidden">
              <ReactCrop
                crop={crop}
                onChange={(c) => setCrop(c)}
                onComplete={(c) => setCompletedCrop(c)}
                aspect={1}
                circularCrop
                className="max-w-full"
              >
                <img
                  ref={imgRef}
                  src={imgSrc}
                  alt="Upload"
                  onLoad={onImageLoad}
                  className="max-w-full"
                />
              </ReactCrop>
            </div>
            <p className="text-sm text-gray-400 text-center">
              Drag to adjust the circular crop area
            </p>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center p-8 border-2 border-dashed border-gray-600 rounded-md">
            {currentAvatar ? (
              <div className="text-center">
                <img
                  src={currentAvatar}
                  alt="Current avatar"
                  className="w-32 h-32 rounded-full mx-auto mb-4"
                />
                <p className="text-sm text-gray-400">
                  This is your current avatar. Upload a new image to change it.
                </p>
              </div>
            ) : (
              <div className="text-center">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  stroke="currentColor"
                  fill="none"
                  viewBox="0 0 48 48"
                  aria-hidden="true"
                >
                  <path
                    d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                    strokeWidth={2}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <p className="mt-2 text-sm text-gray-400">
                  Click Browse or drag and drop to upload an image
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </Modal>
  );
};
