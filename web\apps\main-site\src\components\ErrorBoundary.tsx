/**
 * Error boundary for catching React hook errors and other component errors
 */

import React from "react";

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error?: Error }>;
}

class ErrorBoundary extends React.Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Only log in development
    if (process.env.NODE_ENV === "development") {
      console.group("🚨 Component Error Caught by ErrorBoundary");
      console.error("Error:", error);
      console.error("Error Info:", errorInfo);
      console.groupEnd();
    }
  }

  render() {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      return <FallbackComponent error={this.state.error} />;
    }

    return this.props.children;
  }
}

const DefaultErrorFallback: React.FC<{ error?: Error }> = ({ error }) => (
  <div className="min-h-screen bg-secondary-dark flex items-center justify-center p-4">
    <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-red-600 max-w-md w-full">
      <h2 className="text-white text-xl font-bold mb-4">
        ⚠️ Something went wrong
      </h2>
      <p className="text-gray-300 mb-4">
        We encountered an error while rendering this component. Please try
        refreshing the page.
      </p>
      {process.env.NODE_ENV === "development" && error && (
        <details className="bg-secondary-dark p-3 rounded border border-gray-600">
          <summary className="text-red-400 cursor-pointer font-mono text-sm">
            Error Details (Development Mode)
          </summary>
          <pre className="text-red-300 text-xs mt-2 overflow-auto">
            {error.stack}
          </pre>
        </details>
      )}
      <button
        onClick={() => window.location.reload()}
        className="mt-4 px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded transition-colors"
      >
        Refresh Page
      </button>
    </div>
  </div>
);

export default ErrorBoundary;
