# 06 - Guide to Strategic End-to-End (E2E) Testing

This document provides a strategic guide and template for implementing End-to-End (E2E) tests using a framework like Playwright. The goal is not 100% test coverage, but to create a robust **safety net** for the application's most critical user journeys.

---

## The Philosophy: The 80/20 Rule

We focus on testing the 20% of the application's functionality that 80% of users depend on. This provides the highest return on investment by catching critical bugs before they reach users, without the overwhelming effort of trying to test everything.

## The Process

Follow these steps to incrementally build a valuable E2E testing suite.

### Step 1: Identify Critical User Paths

A critical path is a sequence of actions a user takes to achieve a core goal in the application. Brainstorm and list the most important workflows.

**Instructions:** Review the list below, check the boxes relevant to this project, and add any that are missing.

**Critical Path Checklist:**
- [ ] **Authentication:** User can sign up, log in, and log out.
- [ ] **Event Discovery:** User can view the list of events, search/filter them, and navigate to a details page.
- [ ] **Event Interaction:** User can register for an event, volunteer, or purchase a ticket.
- [ ] **User Profile:** User can view and edit their own profile information.
- [ ] **Admin - Event Management:** An admin user can create, edit, and delete an event.
- [ ] **Admin - User Management:** An admin user can view and manage users.
- [ ] **Static Page Navigation:** User can successfully navigate to key pages like "About Us" or "Contact".
- [ ] *(Add custom path here)*
- [ ] *(Add custom path here)*

### Step 2: Prioritize

From the list above, create a ranked list from most critical to least critical. You will start by implementing the #1 test first.

**My Prioritized List:**
1.  ______________________________________________________
2.  ______________________________________________________
3.  ______________________________________________________
4.  ______________________________________________________
5.  ______________________________________________________

### Step 3: Plan and Write Your First Test

Take the #1 priority from your list and fill out the template below. This will be your first "smoke test."

**Tip:** Use a tool like Playwright Codegen to accelerate this process. Run `pnpm exec playwright codegen <your-site-url>` and perform the actions manually. Playwright will generate the script for you, which you can then refine.

### Step 4: Refactor for Reusability

As you write tests, identify common sequences of actions (like logging in or navigating to a specific page). Extract these into reusable functions to keep your tests clean and easy to write.

**Example:** Instead of repeating login steps in every test, create one `login()` function and call it.

### Step 5: Expand Incrementally

Once your first test is working and integrated, move to the next item on your priority list. Add tests incrementally, rather than trying to do it all at once. For all new features, part of the "definition of done" should be creating an E2E test for its critical path.

---

## E2E Test Plan Template

**Instructions:** Copy and fill out this section for each new test you plan to create.

**Test Plan for:** `(Name of the critical path, e.g., "User Login")`

*   **Priority:** `(e.g., 1)`
*   **Test Description:** `(A brief, one-sentence description of the test. e.g., "Verifies that a registered user can successfully log in and is redirected to the dashboard.")`

*   **Manual Steps (The "Happy Path"):**
    1.  `Go to the home page`
    2.  `Click the "Login" link`
    3.  `Fill in the username field with a valid username`
    4.  `Fill in the password field with a valid password`
    5.  `Click the "Submit" button`

*   **Expected Outcome (Assertions):**
    *   `The URL should change to /dashboard.`
    *   `The user's name should be visible in the header.`
    *   `A "Welcome back" message should be visible.`
