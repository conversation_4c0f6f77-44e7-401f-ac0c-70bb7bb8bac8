/**
 * Centralized fetch client for making authenticated API requests
 * This utility automatically includes authentication tokens in all API requests
 */

// Helper to get auth token
const getAuthToken = () => {
  if (typeof window !== "undefined") {
    return localStorage.getItem("auth_token");
  }
  return null;
};

// Helper to build URL with query parameters
const buildUrl = (url: string, params?: Record<string, string>) => {
  if (!params) return url;

  const queryParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, value);
    }
  });

  const queryString = queryParams.toString();
  return queryString ? `${url}?${queryString}` : url;
};

// Main fetch client
const fetchClient = {
  async request<T>(
    url: string,
    options: RequestInit & { params?: Record<string, string> } = {},
  ): Promise<T> {
    const { params, ...fetchOptions } = options;

    // Build URL with query parameters
    const fullUrl = buildUrl(url, params);

    // Get auth token
    const token = getAuthToken();

    // Prepare headers
    const headers = new Headers(options.headers);

    // Add content type if not provided and method is not GET
    // Skip for FormData to let the browser set the correct Content-Type with boundary
    if (
      !headers.has("Content-Type") &&
      options.method !== "GET" &&
      options.body &&
      !(options.body instanceof FormData)
    ) {
      headers.append("Content-Type", "application/json");
    }

    // Add auth token if available
    if (token) {
      headers.append("Authorization", `Bearer ${token}`);
    }

    // Make the request
    const response = await fetch(fullUrl, {
      ...fetchOptions,
      headers,
    });

    // Handle common errors
    if (!response.ok) {
      // Log the failed request details
      console.error(
        `API request failed: ${options.method || "GET"} ${fullUrl} - Status: ${
          response.status
        }`,
      );

      // Try to parse error response as JSON
      let errorMessage = "API request failed";
      let errorDetails = {};

      try {
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
          const errorData = await response.json();
          errorMessage =
            errorData.error ||
            errorData.message ||
            `API request failed with status ${response.status}`;
          errorDetails = errorData.details || {};

          // Log the error data for debugging
          console.error("API error response:", errorData);
        } else {
          // For non-JSON responses, try to get the text
          const errorText = await response.text();
          errorMessage =
            errorText || `API request failed with status ${response.status}`;

          // Log the error text for debugging
          console.error("API error text:", errorText);
        }
      } catch (parseError) {
        console.error("Error parsing error response:", parseError);
        errorMessage = `API request failed with status ${response.status}`;
      }

      // Create a detailed error object
      const error = new Error(errorMessage);
      (error as any).details = errorDetails;
      (error as any).status = response.status;
      (error as any).url = fullUrl;
      (error as any).method = options.method || "GET";
      throw error;
    }

    // Parse JSON response
    return response.json();
  },

  // Convenience methods
  get<T>(
    url: string,
    options: RequestInit & { params?: Record<string, string> } = {},
  ): Promise<T> {
    return this.request<T>(url, { ...options, method: "GET" });
  },

  post<T>(
    url: string,
    data: any,
    options: RequestInit & { params?: Record<string, string> } = {},
  ): Promise<T> {
    // If data is already FormData, don't stringify it
    const body = data instanceof FormData ? data : JSON.stringify(data);

    return this.request<T>(url, {
      ...options,
      method: "POST",
      body,
    });
  },

  put<T>(
    url: string,
    data: any,
    options: RequestInit & { params?: Record<string, string> } = {},
  ): Promise<T> {
    // If data is already FormData, don't stringify it
    const body = data instanceof FormData ? data : JSON.stringify(data);

    return this.request<T>(url, {
      ...options,
      method: "PUT",
      body,
    });
  },

  patch<T>(
    url: string,
    data: any,
    options: RequestInit & { params?: Record<string, string> } = {},
  ): Promise<T> {
    // If data is already FormData, don't stringify it
    const body = data instanceof FormData ? data : JSON.stringify(data);

    return this.request<T>(url, {
      ...options,
      method: "PATCH",
      body,
    });
  },

  delete<T>(
    url: string,
    options: RequestInit & { params?: Record<string, string> } = {},
  ): Promise<T> {
    return this.request<T>(url, { ...options, method: "DELETE" });
  },
};

export default fetchClient;
