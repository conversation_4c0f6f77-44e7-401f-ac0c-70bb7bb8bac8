import fs from "fs";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import { prisma } from "@/lib/prisma";
import { optimizeForWeb, getImageDimensions } from "@/lib/imageProcessingV2";

export interface UploadOptionsV2 {
  uploadType: string;
  entityId?: string;
  options?: {
    maxSize?: number;
    allowedTypes?: string[];
    processImage?: boolean;
    generateThumbnail?: boolean;
    quality?: number;
  };
}

export interface UploadResultV2 {
  id: string;
  filename: string;
  url: string;
  originalUrl: string;
  mimeType: string;
  size: number;
  dimensions?: {
    width: number;
    height: number;
  };
  uploadType: string;
  entityId?: string;
}

export async function saveImageV2(
  file: File,
  options: UploadOptionsV2,
): Promise<UploadResultV2> {
  const uploadId = uuidv4();

  try {
    const { uploadType, entityId, options: uploadOptions = {} } = options;

    // Validate file
    await validateFile(file, uploadOptions);

    // Create unique filename
    const timestamp = Date.now();
    const sanitizedName = file.name.replace(/[^a-zA-Z0-9.-]/g, "-");
    const filename = `${timestamp}-${uploadId.slice(0, 8)}-${sanitizedName}`;

    // Determine directory based on upload type
    const directory = getDirectoryForType(uploadType);
    const uploadDir = path.join(process.cwd(), "public", "uploads", directory);
    const relativePath = path.join("public", "uploads", directory, filename);
    const fullPath = path.join(process.cwd(), relativePath);

    // Ensure directory exists
    await fs.promises.mkdir(uploadDir, { recursive: true });

    // Get file buffer
    const arrayBuffer = await file.arrayBuffer();
    let fileBuffer = new Uint8Array(arrayBuffer);

    // Get image dimensions if it's an image
    let dimensions: { width: number; height: number } | undefined;
    if (
      file.type.startsWith("image/") &&
      uploadOptions.processImage !== false
    ) {
      try {
        const originalBuffer = Buffer.from(fileBuffer);

        // Get original dimensions
        dimensions = await getImageDimensions(originalBuffer);

        // Process and optimize image
        const processedBuffer = await optimizeForWeb(
          originalBuffer,
          uploadType,
        );
        fileBuffer = new Uint8Array(processedBuffer);
      } catch (error) {
        console.warn("Image processing failed, using original:", error);
        // Continue with original file if processing fails
        try {
          // At least try to get dimensions from original
          dimensions = await getImageDimensions(Buffer.from(fileBuffer));
        } catch (dimError) {
          console.warn("Could not get image dimensions:", dimError);
        }
      }
    }

    // Write file to disk
    await fs.promises.writeFile(fullPath, fileBuffer);

    // Store in database with new schema fields
    const uploadRecord = await prisma.uploadedImage.create({
      data: {
        id: uploadId,
        filename: filename,
        path: relativePath,
        url: `/api/uploads/${directory}/${filename}`, // Will be updated after we have the ID
        mimeType: file.type || "application/octet-stream",
        size: fileBuffer.length,
        entityType: uploadType,
        entityId: entityId || null,
        upload_type: uploadType,
        upload_config: uploadOptions as any,
        dimensions: dimensions as any,
      },
    });

    // Update the URL with the actual record ID
    await prisma.uploadedImage.update({
      where: { id: uploadId },
      data: { url: `/api/uploads/${directory}/${filename}` },
    });

    const result: UploadResultV2 = {
      id: uploadRecord.id,
      filename: uploadRecord.filename,
      url: uploadRecord.url,
      originalUrl: `/uploads/${directory}/${filename}`,
      mimeType: uploadRecord.mimeType,
      size: uploadRecord.size,
      dimensions,
      uploadType,
      entityId,
    };

    return result;
  } catch (error) {
    throw error;
  }
}

async function validateFile(
  file: File,
  options: UploadOptionsV2["options"] = {},
): Promise<void> {
  // Validate file type
  if (options.allowedTypes && !options.allowedTypes.includes(file.type)) {
    throw new Error(
      `Invalid file type. Allowed types: ${options.allowedTypes.join(", ")}`,
    );
  }

  // Validate file size
  if (options.maxSize && file.size > options.maxSize) {
    const maxSizeMB = options.maxSize / (1024 * 1024);
    throw new Error(`File size exceeds ${maxSizeMB}MB limit`);
  }

  // Additional validation for images
  if (file.type.startsWith("image/")) {
    // Check if file is actually an image by trying to create an image object
    try {
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      // Basic image validation - check for common image headers
      const header = buffer.toString("hex", 0, 4);
      const isValidImage =
        header.startsWith("ffd8") || // JPEG
        header.startsWith("8950") || // PNG
        header.startsWith("4749") || // GIF
        header.startsWith("5249"); // WEBP

      if (!isValidImage) {
        throw new Error("File does not appear to be a valid image");
      }
    } catch (error) {
      throw new Error("Invalid image file");
    }
  }
}

function getDirectoryForType(uploadType: string): string {
  const typeDirectoryMap: { [key: string]: string } = {
    avatar: "avatars",
    news: "news",
    event: "events",
    product: "products",
    deposit: "deposits",
    "ship-logo": "ships",
    general: "general",
  };

  return typeDirectoryMap[uploadType] || "general";
}
