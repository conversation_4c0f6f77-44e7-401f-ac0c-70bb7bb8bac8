"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../../../contexts/AuthContext";
import { AdminDashboardLayout } from "../../../components/admin";
import {
  getAdminDashboardStats,
  AdminDashboardStats,
} from "../../../services/adminService";
import { useQuery } from "@tanstack/react-query";

export default function AdminDashboardPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);

  // Fetch dashboard stats
  const {
    data: stats,
    isLoading: isLoadingStats,
    error,
  } = useQuery<AdminDashboardStats>({
    queryKey: ["adminDashboardStats"],
    queryFn: getAdminDashboardStats,
    enabled: !!user?.roles?.admin, // Only fetch if user is admin
  });

  // Check authorization after auth is loaded
  useEffect(() => {
    // Only proceed with checks if auth loading is complete
    if (!isLoading) {
      if (!user || !user.roles?.admin) {
        router.push("/");
      } else {
        // User is authorized
        setIsAuthorized(true);
      }
    }
  }, [user, router, isLoading]);

  // Show loading state or nothing while checking authorization
  if (isLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <p className="text-white text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <AdminDashboardLayout>
      <div className="py-2 px-2">
        <div className="bg-secondary-light rounded-lg">
          <h2 className="text-2xl font-bold text-white">Admin Dashboard</h2>
          {isLoadingStats ? (
            <div className="text-center py-2">
              <p className="text-white">Loading dashboard data...</p>
            </div>
          ) : error ? (
            <div className="text-center py-2">
              <p className="text-red-400">
                Error loading dashboard data. Please try again later.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div className="bg-secondary rounded-lg p-4 border border-gray-600">
                <h3 className="text-xl font-semibold mb-2 text-white">
                  Quick Stats
                </h3>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Total Users:</span>
                    <span className="text-white font-bold">
                      {stats?.quickStats.totalUsers.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Active Merchants:</span>
                    <span className="text-white font-bold">
                      {stats?.quickStats.activeMerchants.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Pending Applications:</span>
                    <span className="text-white font-bold">
                      {stats?.quickStats.pendingApplications.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Active Auctions:</span>
                    <span className="text-white font-bold">
                      {stats?.quickStats.activeAuctions.toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-secondary rounded-lg p-4 border border-gray-600">
                <h3 className="text-xl font-semibold mb-2 text-white">
                  Recent Activity
                </h3>
                <ul className="space-y-3">
                  <li className="text-gray-400">
                    <span className="text-primary">User Management:</span>{" "}
                    {stats?.recentActivity.userManagement}
                  </li>
                  <li className="text-gray-400">
                    <span className="text-primary">Featured Content:</span>{" "}
                    {stats?.recentActivity.featuredContent}
                  </li>
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>
    </AdminDashboardLayout>
  );
}
