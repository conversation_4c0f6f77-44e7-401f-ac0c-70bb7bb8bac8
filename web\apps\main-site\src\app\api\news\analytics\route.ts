import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";

export async function GET(request: Request) {
  try {
    // Get total counts
    const totalArticles = await prisma.newsArticle.count();
    const publishedArticles = await prisma.newsArticle.count({
      where: { status: "published" },
    });
    const draftArticles = await prisma.newsArticle.count({
      where: { status: "draft" },
    });
    const pausedArticles = await prisma.newsArticle.count({
      where: { status: "paused" },
    });
    const featuredArticles = await prisma.newsArticle.count({
      where: { featured: true },
    });

    // Get top viewed articles
    const topViewedArticles = await prisma.newsArticle.findMany({
      where: { status: "published" },
      orderBy: { views: "desc" },
      take: 5,
      select: {
        id: true,
        title: true,
        slug: true,
        views: true,
        publishedAt: true,
      },
    });

    // Get articles by category
    const articlesByCategory = await prisma.newsCategory.findMany({
      select: {
        id: true,
        name: true,
        _count: {
          select: { articles: true },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    // Get recent articles
    const recentArticles = await prisma.newsArticle.findMany({
      orderBy: { updatedAt: "desc" },
      take: 5,
      select: {
        id: true,
        title: true,
        slug: true,
        status: true,
        updatedAt: true,
        author: {
          select: {
            displayName: true,
          },
        },
      },
    });

    return NextResponse.json({
      counts: {
        total: totalArticles,
        published: publishedArticles,
        draft: draftArticles,
        paused: pausedArticles,
        featured: featuredArticles,
      },
      topViewed: topViewedArticles,
      byCategory: articlesByCategory.map((category) => ({
        id: category.id,
        name: category.name,
        count: category._count.articles,
      })),
      recent: recentArticles,
    });
  } catch (error) {
    console.error("Error fetching analytics:", error);
    return NextResponse.json(
      { error: "Failed to fetch analytics" },
      { status: 500 },
    );
  }
}
