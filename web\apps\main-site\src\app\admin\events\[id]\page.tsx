"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { format } from "date-fns";
import { useColorTheme } from "@/contexts/ColorThemeContext";
import { useAuth } from "@/contexts/AuthContext";
import { ImageUploader } from "@/components/common/ImageUploader";
import fetchClient from "@/lib/fetchClient";

// Define types
interface Category {
  id: string;
  name: string;
  description: string | null;
  color: string | null;
}

interface Event {
  id: string;
  name: string;
  description: string;
  shortDescription: string | null;
  startDate: string;
  endDate: string;
  location: string | null;
  address: string | null;
  virtualLink: string | null;
  isVirtual: boolean;
  image: string | null;
  status: string;
  capacity: number | null;
  categoryId: string;
  category: Category;
}

export default function EditEventPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { id } = params;
  const { isDarkMode } = useColorTheme();
  const { user, isAuthenticated } = useAuth();

  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [event, setEvent] = useState<Event | null>(null);

  // Define the form data type with optional fields
  interface EventFormData {
    name: string;
    description: string;
    shortDescription: string;
    startDate: string;
    startTime?: string; // Make optional so it can be destructured
    endDate: string;
    endTime?: string; // Make optional so it can be destructured
    location: string;
    address: string;
    virtualLink: string;
    isVirtual: boolean;
    image: string;
    status: string;
    capacity: string;
    categoryId: string;
  }

  // Form state
  const [formData, setFormData] = useState<EventFormData>({
    name: "",
    description: "",
    shortDescription: "",
    startDate: "",
    startTime: "09:00",
    endDate: "",
    endTime: "17:00",
    location: "",
    address: "",
    virtualLink: "",
    isVirtual: false,
    image: "",
    status: "draft",
    capacity: "",
    categoryId: "",
  });

  // Fetch event data
  const fetchEvent = async () => {
    try {
      const data = await fetchClient.get<Event>(`/api/admin/events/${id}`);
      setEvent(data);

      // Format dates and times for form
      const startDate = new Date(data.startDate);
      const endDate = new Date(data.endDate);

      setFormData({
        name: data.name,
        description: data.description,
        shortDescription: data.shortDescription || "",
        startDate: format(startDate, "yyyy-MM-dd"),
        startTime: format(startDate, "HH:mm"),
        endDate: format(endDate, "yyyy-MM-dd"),
        endTime: format(endDate, "HH:mm"),
        location: data.location || "",
        address: data.address || "",
        virtualLink: data.virtualLink || "",
        isVirtual: data.isVirtual,
        image: data.image || "",
        status: data.status,
        capacity: data.capacity ? data.capacity.toString() : "",
        categoryId: data.categoryId,
      });

      setError(null);
    } catch (err: any) {
      console.error("Error fetching event:", err);
      setError(err.message || "Failed to load event. Please try again.");
      setEvent(null);
    }
  };

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const data = await fetchClient.get<Category[]>(
        "/api/admin/event-categories",
      );
      setCategories(data);
    } catch (err) {
      console.error("Error loading categories:", err);
      setError("Failed to load categories. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Combine date and time
      const startDateTime = new Date(
        `${formData.startDate}T${formData.startTime}`,
      );
      const endDateTime = new Date(`${formData.endDate}T${formData.endTime}`);

      // Validate dates
      if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
        throw new Error("Invalid date or time format");
      }

      if (endDateTime < startDateTime) {
        throw new Error("End date/time must be after start date/time");
      }

      // Create a copy of the form data to modify
      const { startTime, endTime, ...restFormData } = formData;

      // Prepare data for API without the fields we don't want to send
      const eventData = {
        ...restFormData,
        startDate: startDateTime.toISOString(),
        endDate: endDateTime.toISOString(),
        capacity: formData.capacity ? parseInt(formData.capacity) : null,
      };

      // Submit to API
      await fetchClient.put(`/api/admin/events/${id}`, eventData);
      setSuccess("Event updated successfully!");

      // Refresh event data
      fetchEvent();
    } catch (err: any) {
      console.error("Error updating event:", err);
      setError(err.message || "Failed to update event. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Initialize
  useEffect(() => {
    fetchEvent();
    fetchCategories();
  }, [id]);

  if (loading && !event) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading event...</div>
      </div>
    );
  }

  if (!loading && !event) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-900/30 border border-red-700 text-white px-4 py-3 rounded mb-4">
          {error || "Event not found"}
        </div>
        <div className="text-center mt-4">
          <Link
            href="/admin/events"
            className="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded"
          >
            Back to Events
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Edit Event</h1>
        <Link
          href="/admin/events"
          className="bg-secondary hover:bg-secondary-light text-white px-4 py-2 rounded"
        >
          Back to Events
        </Link>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-900/30 border border-red-700 text-white px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Success message */}
      {success && (
        <div className="bg-green-900/30 border border-green-700 text-white px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}

      {/* Event form */}
      <div className="bg-card shadow rounded-lg p-6">
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Event name */}
            <div className="col-span-2">
              <label
                className="block text-sm font-medium mb-1 text-white"
              >
                Event Name *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full bg-input border border-gray-600 text-white rounded px-3 py-2 focus:border-primary focus:outline-none"
                placeholder="Enter event name"
              />
            </div>

            {/* Event Image Upload */}
            <div className="col-span-2">
              <label
                className="block text-sm font-medium mb-1 text-white"
              >
                Event Image
              </label>
              <div className="flex flex-col space-y-2">
                <ImageUploader
                  onImageUploaded={(imageUrl) => {
                    setFormData((prev) => ({ ...prev, image: imageUrl }));
                    setUploadError(null);
                  }}
                  onError={(error) => setUploadError(error)}
                  buttonText="Upload Event Image"
                  acceptedFormats="image/jpeg, image/png, image/webp"
                  maxSizeMB={5}
                />
                {uploadError && (
                  <p
                    className="text-sm"
                    style={{ color: "var(--color-error)" }}
                  >
                    {uploadError}
                  </p>
                )}
                {formData.image && (
                  <div className="mt-2">
                    <p
                      className="text-sm"
                      style={{ color: "var(--color-text-muted)" }}
                    >
                      Current image: {formData.image.split("/").pop()}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Short description */}
            <div className="col-span-2">
              <label
                className="block text-sm font-medium mb-1 text-white"
              >
                Short Description
              </label>
              <input
                type="text"
                name="shortDescription"
                value={formData.shortDescription}
                onChange={handleInputChange}
                className="w-full bg-input border border-gray-600 text-white rounded px-3 py-2 focus:border-primary focus:outline-none"
                placeholder="Brief description (max 500 characters)"
                maxLength={500}
              />
              <p
                className="text-xs mt-1"
                style={{ color: "var(--color-text-muted)" }}
              >
                {formData.shortDescription.length}/500 characters
              </p>
            </div>

            {/* Full description */}
            <div className="col-span-2">
              <label
                className="block text-sm font-medium mb-1 text-white"
              >
                Full Description *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                required
                rows={6}
                className="w-full bg-input border border-gray-600 text-white rounded px-3 py-2 focus:border-primary focus:outline-none"
                placeholder="Detailed event description"
              />
            </div>

            {/* Start date and time */}
            <div>
              <label
                className="block text-sm font-medium mb-1 text-white"
              >
                Start Date *
              </label>
              <input
                type="date"
                name="startDate"
                value={formData.startDate}
                onChange={handleInputChange}
                required
                className="w-full bg-input border border-gray-600 text-white rounded px-3 py-2 focus:border-primary focus:outline-none"
              />
            </div>

            <div>
              <label
                className="block text-sm font-medium mb-1 text-white"
              >
                Start Time *
              </label>
              <input
                type="time"
                name="startTime"
                value={formData.startTime}
                onChange={handleInputChange}
                required
                className="w-full bg-input border border-gray-600 text-white rounded px-3 py-2 focus:border-primary focus:outline-none"
              />
            </div>

            {/* End date and time */}
            <div>
              <label
                className="block text-sm font-medium mb-1 text-white"
              >
                End Date *
              </label>
              <input
                type="date"
                name="endDate"
                value={formData.endDate}
                onChange={handleInputChange}
                required
                className="w-full bg-input border border-gray-600 text-white rounded px-3 py-2 focus:border-primary focus:outline-none"
              />
            </div>

            <div>
              <label
                className="block text-sm font-medium mb-1 text-white"
              >
                End Time *
              </label>
              <input
                type="time"
                name="endTime"
                value={formData.endTime}
                onChange={handleInputChange}
                required
                className="w-full bg-input border border-gray-600 text-white rounded px-3 py-2 focus:border-primary focus:outline-none"
              />
            </div>

            {/* Virtual event toggle */}
            <div className="col-span-2">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isVirtual"
                  name="isVirtual"
                  checked={formData.isVirtual}
                  onChange={handleCheckboxChange}
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-600 bg-input rounded"
                />
                <label
                  htmlFor="isVirtual"
                  className="ml-2 block text-sm text-white"
                >
                  This is a virtual event
                </label>
              </div>
            </div>

            {/* Location */}
            {!formData.isVirtual && (
              <>
                <div>
                  <label
                    className="block text-sm font-medium mb-1 text-white"
                  >
                    Location *
                  </label>
                  <input
                    type="text"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    className="w-full bg-input border border-gray-600 text-white rounded px-3 py-2 focus:border-primary focus:outline-none"
                    placeholder="Event venue or location name"
                  />
                </div>

                <div>
                  <label
                    className="block text-sm font-medium mb-1 text-white"
                  >
                    Address
                  </label>
                  <input
                    type="text"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    className="w-full bg-input border border-gray-600 text-white rounded px-3 py-2 focus:border-primary focus:outline-none"
                    placeholder="Full address"
                  />
                </div>
              </>
            )}

            {/* Virtual link */}
            {formData.isVirtual && (
              <div className="col-span-2">
                <label
                  className="block text-sm font-medium mb-1 text-white"
                >
                  Virtual Meeting Link
                </label>
                <input
                  type="url"
                  name="virtualLink"
                  value={formData.virtualLink}
                  onChange={handleInputChange}
                  className="w-full bg-input border border-gray-600 text-white rounded px-3 py-2 focus:border-primary focus:outline-none"
                  placeholder="Zoom, Teams, or other meeting link"
                />
              </div>
            )}

            {/* Category */}
            <div>
              <label
                className="block text-sm font-medium mb-1 text-white"
              >
                Category *
              </label>
              <select
                name="categoryId"
                value={formData.categoryId}
                onChange={handleInputChange}
                required
                className="w-full bg-input border border-gray-600 text-white rounded px-3 py-2 focus:border-primary focus:outline-none"
              >
                <option value="">Select a category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Capacity */}
            <div>
              <label
                className="block text-sm font-medium mb-1 text-white"
              >
                Capacity
              </label>
              <input
                type="number"
                name="capacity"
                value={formData.capacity}
                onChange={handleInputChange}
                min="1"
                className="w-full bg-input border border-gray-600 text-white rounded px-3 py-2 focus:border-primary focus:outline-none"
                placeholder="Maximum number of attendees"
              />
            </div>

            {/* Status */}
            <div>
              <label
                className="block text-sm font-medium mb-1 text-white"
              >
                Status
              </label>
              <select
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="w-full bg-input border border-gray-600 text-white rounded px-3 py-2 focus:border-primary focus:outline-none"
              >
                <option value="draft">Draft</option>
                <option value="published">Published</option>
                <option value="cancelled">Cancelled</option>
                <option value="completed">Completed</option>
              </select>
            </div>
          </div>

          {/* Submit button */}
          <div className="mt-8">
            <button
              type="submit"
              disabled={loading}
              className={`w-full md:w-auto px-6 py-2 rounded text-white font-medium border ${
                loading 
                  ? "cursor-not-allowed bg-gray-600 border-gray-600" 
                  : "bg-primary hover:bg-primary-dark border-primary"
              }`}
            >
              {loading ? "Updating Event..." : "Update Event"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
