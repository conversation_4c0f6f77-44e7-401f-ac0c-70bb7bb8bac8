{"name": "@bank-of-styx/config", "version": "0.1.0", "private": true, "license": "MIT", "main": "eslint-preset.js", "files": ["eslint-preset.js", "tailwind.config.js", "tsconfig.base.json"], "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.59.5", "@typescript-eslint/parser": "^5.59.5", "eslint": "^8.40.0", "eslint-config-next": "^13.4.2", "eslint-config-prettier": "^8.8.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "typescript": "^5.0.4"}}