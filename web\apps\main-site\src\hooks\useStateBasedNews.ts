"use client";

import { useQuery } from "@tanstack/react-query";
import { useUserState } from "@/contexts/UserStateContext";
import fetchClient from "@/lib/fetchClient";

/**
 * Interface for news query options
 */
interface NewsQueryOptions {
  page?: number;
  featured?: boolean;
  category?: string;
  limit?: number;
  search?: string;
  sortBy?: string;
  order?: string;
  maxAgeMinutes?: number;
}

/**
 * Custom hook for fetching news articles with state-based caching
 * @param {Object} options - Query options
 * @param {number} options.page - Page number for pagination
 * @param {boolean} options.featured - Filter by featured status
 * @param {string} options.category - Filter by category slug
 * @param {number} options.limit - Limit the number of results
 * @param {string} options.search - Search query string
 * @param {string} options.sortBy - Sort field
 * @param {string} options.order - Sort order (asc/desc)
 * @param {number} options.maxAgeMinutes - Maximum age of cached data in minutes
 * @returns {Object} Query result with data, isLoading, and error
 */
export function useStateBasedNews({
  page = 1,
  featured,
  category,
  limit = 10,
  search,
  sortBy = "publishedAt",
  order = "desc",
  maxAgeMinutes = 60,
}: NewsQueryOptions = {}) {
  const { userState, updateUserState, needsFreshData } = useUserState();

  // Build query parameters
  const params = new URLSearchParams();
  if (page) params.append("page", page.toString());
  if (featured !== undefined) params.append("featured", featured.toString());
  if (category) params.append("category", category);
  if (limit) params.append("limit", limit.toString());
  if (search) params.append("search", search);
  if (sortBy) params.append("sortBy", sortBy);
  if (order) params.append("order", order);

  // Create query key that includes ALL parameters for proper caching
  const queryKey = ["news", page, featured, category, limit, search, sortBy, order];

  // Fetch news articles
  const query = useQuery({
    queryKey,
    queryFn: async () => {
      // Use public news endpoint for non-authenticated users
      const endpoint = userState ? `/api/news/articles` : `/api/news/public`;

    

      // Convert params to query params object
      const queryParams: Record<string, string> = {};
      if (page) queryParams.page = page.toString();
      if (featured !== undefined) queryParams.featured = featured.toString();
      if (category) queryParams.category = category;
      if (limit) queryParams.limit = limit.toString();
      if (search) queryParams.search = search;
      if (sortBy) queryParams.sortBy = sortBy;
      if (order) queryParams.order = order;

      const data = await fetchClient.get(endpoint, { params: queryParams });

      // Update last sync time if user state is available
      if (userState) {
        updateUserState({ lastNewsSync: new Date().toISOString() });
      }

      return data;
    },
    // Let TanStack Query handle caching with global defaults (5min staleTime, 10min gcTime)
    enabled: true, // Always run the query, even for non-authenticated users
  });

  return query;
}

/**
 * Interface for category query options
 */
interface CategoryQueryOptions {
  maxAgeMinutes?: number;
}

/**
 * Custom hook for fetching news categories with state-based caching
 * @param {Object} options - Query options
 * @param {number} options.maxAgeMinutes - Maximum age of cached data in minutes
 * @returns {Object} Query result with data, isLoading, and error
 */
export function useStateBasedCategories({
  maxAgeMinutes = 1440,
}: CategoryQueryOptions = {}) {
  const { userState, updateUserState } = useUserState();

  // Fetch categories
  const query = useQuery({
    queryKey: ["categories"],
    queryFn: async () => {
      const data = await fetchClient.get("/api/news/categories");

      // Update last sync time if user state is available
      if (userState) {
        updateUserState({ lastCategorySync: new Date().toISOString() });
      }

      return data;
    },
    // Let TanStack Query handle caching with global defaults (5min staleTime, 10min gcTime)
    enabled: true, // Always run the query, even for non-authenticated users
  });

  return query;
}
