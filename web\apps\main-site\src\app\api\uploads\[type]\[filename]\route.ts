import { NextRequest, NextResponse } from "next/server";
import path from "path";
import fs from "fs";

/**
 * API endpoint to serve files from the uploads directory outside of public
 * This handles all types of uploads including deposits, news articles, and avatars
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { type: string; filename: string } },
) {
  const { type, filename } = params;

  // Validate type to prevent directory traversal
  const validTypes = ["deposits", "avatars", "news", "ships"];
  if (!validTypes.includes(type)) {
    return new NextResponse("Not Found", { status: 404 });
  }

  // Construct the file path to the uploads directory in public
  const filePath = path.join(
    process.cwd(),
    "public",
    "uploads",
    type,
    filename,
  );

  // For fallback during transition, check the uploads directory outside public if file doesn't exist in public
  const fallbackFilePath = path.join(
    process.cwd(),
    "..",
    "..",
    "uploads",
    type,
    filename,
  );

  // Determine which file to serve
  let fileToServe = filePath;

  // Check if file exists in the new location
  try {
    const stat = fs.statSync(filePath);
    if (!stat.isFile()) {
      // If not a file in public, check the fallback directory (outside public)
      if (
        fs.existsSync(fallbackFilePath) &&
        fs.statSync(fallbackFilePath).isFile()
      ) {
        fileToServe = fallbackFilePath;
      } else {
        return new NextResponse("Not Found", { status: 404 });
      }
    }

    // Determine content type based on file extension
    let contentType = "application/octet-stream";
    if (filename.endsWith(".jpg") || filename.endsWith(".jpeg")) {
      contentType = "image/jpeg";
    } else if (filename.endsWith(".png")) {
      contentType = "image/png";
    } else if (filename.endsWith(".gif")) {
      contentType = "image/gif";
    } else if (filename.endsWith(".pdf")) {
      contentType = "application/pdf";
    } else if (filename.endsWith(".svg")) {
      contentType = "image/svg+xml";
    } else if (filename.endsWith(".webp")) {
      contentType = "image/webp";
    }

    // Read and return the file
    const fileBuffer = fs.readFileSync(fileToServe);
    return new NextResponse(fileBuffer, {
      headers: {
        "Content-Type": contentType,
        "Cache-Control": "public, max-age=86400", // Cache for 24 hours
      },
    });
  } catch (error) {
    console.error(`Error serving file ${filePath}:`, error);
    return new NextResponse("Not Found", { status: 404 });
  }
}
