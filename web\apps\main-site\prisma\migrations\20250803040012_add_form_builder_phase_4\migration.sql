-- CreateTable
CREATE TABLE `form_templates` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `description` TEXT NULL,
    `isReusable` BOOLEAN NOT NULL DEFAULT true,
    `formStructure` JSON NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `createdById` VARCHAR(191) NOT NULL,

    INDEX `form_templates_createdById_idx`(`createdById`),
    INDEX `form_templates_isReusable_idx`(`isReusable`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `event_forms` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `description` TEXT NULL,
    `formStructure` JSON NOT NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'draft',
    `submissionDeadline` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `eventId` VARCHAR(191) NOT NULL,
    `templateId` VARCHAR(191) NULL,
    `createdById` VARCHAR(191) NOT NULL,

    INDEX `event_forms_eventId_idx`(`eventId`),
    INDEX `event_forms_templateId_idx`(`templateId`),
    INDEX `event_forms_createdById_idx`(`createdById`),
    INDEX `event_forms_status_idx`(`status`),
    INDEX `event_forms_submissionDeadline_idx`(`submissionDeadline`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `form_submissions` (
    `id` VARCHAR(191) NOT NULL,
    `submissionData` JSON NOT NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'submitted',
    `reviewNotes` TEXT NULL,
    `submittedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `reviewedAt` DATETIME(3) NULL,
    `formId` VARCHAR(191) NOT NULL,
    `shipId` VARCHAR(191) NOT NULL,
    `submittedById` VARCHAR(191) NOT NULL,
    `reviewedById` VARCHAR(191) NULL,

    INDEX `form_submissions_formId_idx`(`formId`),
    INDEX `form_submissions_shipId_idx`(`shipId`),
    INDEX `form_submissions_submittedById_idx`(`submittedById`),
    INDEX `form_submissions_reviewedById_idx`(`reviewedById`),
    INDEX `form_submissions_status_idx`(`status`),
    UNIQUE INDEX `form_submissions_formId_shipId_key`(`formId`, `shipId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `form_templates` ADD CONSTRAINT `form_templates_createdById_fkey` FOREIGN KEY (`createdById`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `event_forms` ADD CONSTRAINT `event_forms_eventId_fkey` FOREIGN KEY (`eventId`) REFERENCES `events`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `event_forms` ADD CONSTRAINT `event_forms_templateId_fkey` FOREIGN KEY (`templateId`) REFERENCES `form_templates`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `event_forms` ADD CONSTRAINT `event_forms_createdById_fkey` FOREIGN KEY (`createdById`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `form_submissions` ADD CONSTRAINT `form_submissions_formId_fkey` FOREIGN KEY (`formId`) REFERENCES `event_forms`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `form_submissions` ADD CONSTRAINT `form_submissions_shipId_fkey` FOREIGN KEY (`shipId`) REFERENCES `ships`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `form_submissions` ADD CONSTRAINT `form_submissions_submittedById_fkey` FOREIGN KEY (`submittedById`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `form_submissions` ADD CONSTRAINT `form_submissions_reviewedById_fkey` FOREIGN KEY (`reviewedById`) REFERENCES `user`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
