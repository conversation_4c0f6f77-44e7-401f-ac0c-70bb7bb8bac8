"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../../../../contexts/AuthContext";
import { AdminDashboardLayout } from "../../../../components/admin";
import {
  getTickets,
  updateTicket,
  SupportTicket,
} from "../../../../services/ticketService";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button, Card, Input } from "@bank-of-styx/ui";
import toast from "react-hot-toast";

export default function TicketManagementPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [priorityFilter, setPriorityFilter] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("");
  const [assignedToMeFilter, setAssignedToMeFilter] = useState(false);
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [selectedTicket, setSelectedTicket] = useState<string | null>(null);
  const queryClient = useQueryClient();

  // Fetch tickets
  const {
    data,
    isLoading: isLoadingTickets,
    error,
  } = useQuery({
    queryKey: [
      "adminTickets",
      page,
      limit,
      searchTerm,
      statusFilter,
      priorityFilter,
      categoryFilter,
      assignedToMeFilter,
    ],
    queryFn: () =>
      getTickets({
        page,
        limit,
        search: searchTerm,
        status: statusFilter,
        priority: priorityFilter,
        category: categoryFilter,
        assignedToMe: assignedToMeFilter,
      }),
    enabled: !!user?.roles?.admin, // Only fetch if user is admin
  });

  // Mutation for updating ticket status
  const updateTicketMutation = useMutation({
    mutationFn: ({
      ticketId,
      status,
      priority,
    }: {
      ticketId: string;
      status?: "open" | "in_progress" | "resolved" | "closed";
      priority?: "low" | "medium" | "high" | "urgent";
    }) =>
      updateTicket(ticketId, {
        status,
        priority,
      }),
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ["adminTickets"] });
      toast.success("Ticket updated successfully");
    },
    onError: (error) => {
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to update ticket. Please try again.",
      );
    },
  });

  // Mutation for assigning ticket to self
  const assignToSelfMutation = useMutation({
    mutationFn: (ticketId: string) =>
      updateTicket(ticketId, {
        assignedToId: user?.id,
      }),
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ["adminTickets"] });
      toast.success("Ticket assigned to you");
    },
    onError: (error) => {
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to assign ticket. Please try again.",
      );
    },
  });

  // Check authorization after auth is loaded
  useEffect(() => {
    // Only proceed with checks if auth loading is complete
    if (!isLoading) {
      if (!user || !user.roles?.admin) {
        router.push("/");
      } else {
        // User is authorized
        setIsAuthorized(true);
      }
    }
  }, [user, router, isLoading]);

  // Handle ticket selection
  const handleTicketSelect = (ticketId: string) => {
    router.push(`/admin/dashboard/tickets/${ticketId}`);
  };

  // Handle status change
  const handleStatusChange = (
    ticketId: string,
    status: "open" | "in_progress" | "resolved" | "closed",
  ) => {
    updateTicketMutation.mutate({ ticketId, status });
  };

  // Handle priority change
  const handlePriorityChange = (
    ticketId: string,
    priority: "low" | "medium" | "high" | "urgent",
  ) => {
    updateTicketMutation.mutate({ ticketId, priority });
  };

  // Handle assign to self
  const handleAssignToSelf = (ticketId: string) => {
    assignToSelfMutation.mutate(ticketId);
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1); // Reset to first page on new search
  };

  // Handle filter reset
  const handleResetFilters = () => {
    setSearchTerm("");
    setStatusFilter("");
    setPriorityFilter("");
    setCategoryFilter("");
    setAssignedToMeFilter(false);
    setPage(1);
  };

  // Show loading state or nothing while checking authorization
  if (isLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <p className="text-white text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-blue-500 bg-opacity-20 text-blue-400 border-blue-500";
      case "in_progress":
        return "bg-yellow-500 bg-opacity-20 text-yellow-400 border-yellow-500";
      case "resolved":
        return "bg-green-500 bg-opacity-20 text-green-400 border-green-500";
      case "closed":
        return "bg-gray-500 bg-opacity-20 text-gray-400 border-gray-500";
      default:
        return "bg-gray-500 bg-opacity-20 text-gray-400 border-gray-500";
    }
  };

  // Get priority badge color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "low":
        return "bg-blue-500 bg-opacity-20 text-blue-400 border-blue-500";
      case "medium":
        return "bg-yellow-500 bg-opacity-20 text-yellow-400 border-yellow-500";
      case "high":
        return "bg-orange-500 bg-opacity-20 text-orange-400 border-orange-500";
      case "urgent":
        return "bg-red-500 bg-opacity-20 text-red-400 border-red-500";
      default:
        return "bg-gray-500 bg-opacity-20 text-gray-400 border-gray-500";
    }
  };

  return (
    <AdminDashboardLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-white">Support Tickets</h2>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={() => {
                // Export to CSV
                if (!data?.tickets || data.tickets.length === 0) {
                  toast.error("No tickets to export");
                  return;
                }

                // Create CSV content
                const headers = [
                  "ID",
                  "Subject",
                  "Status",
                  "Priority",
                  "Category",
                  "Created At",
                  "Email",
                  "Name",
                ];
                const csvRows = [
                  headers.join(","),
                  ...data.tickets.map((ticket) =>
                    [
                      ticket.id,
                      `"${ticket.subject.replace(/"/g, '""')}"`,
                      ticket.status,
                      ticket.priority,
                      ticket.category,
                      new Date(ticket.createdAt).toLocaleString(),
                      `"${ticket.email.replace(/"/g, '""')}"`,
                      ticket.name ? `"${ticket.name.replace(/"/g, '""')}"` : "",
                    ].join(","),
                  ),
                ];
                const csvContent = csvRows.join("\n");

                // Create and download the CSV file
                const blob = new Blob([csvContent], {
                  type: "text/csv;charset=utf-8;",
                });
                const url = URL.createObjectURL(blob);
                const link = document.createElement("a");
                link.setAttribute("href", url);
                link.setAttribute(
                  "download",
                  `support-tickets-${
                    new Date().toISOString().split("T")[0]
                  }.csv`,
                );
                link.style.visibility = "hidden";
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                toast.success("Tickets exported to CSV");
              }}
            >
              Export to CSV
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-secondary rounded-lg shadow-md p-4 border border-gray-600 mb-6">
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Input
                label="Search"
                id="search"
                value={searchTerm}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setSearchTerm(e.target.value)
                }
                placeholder="Search tickets..."
                fullWidth
              />

              <div>
                <label
                  htmlFor="status"
                  className="block text-sm font-medium text-white mb-1"
                >
                  Status
                </label>
                <select
                  id="status"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary appearance-none"
                >
                  <option value="">All Statuses</option>
                  <option value="open">Open</option>
                  <option value="in_progress">In Progress</option>
                  <option value="resolved">Resolved</option>
                  <option value="closed">Closed</option>
                </select>
              </div>

              <div>
                <label
                  htmlFor="priority"
                  className="block text-sm font-medium text-white mb-1"
                >
                  Priority
                </label>
                <select
                  id="priority"
                  value={priorityFilter}
                  onChange={(e) => setPriorityFilter(e.target.value)}
                  className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary appearance-none"
                >
                  <option value="">All Priorities</option>
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="urgent">Urgent</option>
                </select>
              </div>

              <div>
                <label
                  htmlFor="category"
                  className="block text-sm font-medium text-white mb-1"
                >
                  Category
                </label>
                <select
                  id="category"
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary appearance-none"
                >
                  <option value="">All Categories</option>
                  <option value="general">General</option>
                  <option value="account">Account</option>
                  <option value="banking">Banking</option>
                  <option value="technical">Technical</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="assignedToMe"
                  checked={assignedToMeFilter}
                  onChange={(e) => setAssignedToMeFilter(e.target.checked)}
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-600 rounded"
                />
                <label
                  htmlFor="assignedToMe"
                  className="ml-2 text-sm text-white"
                >
                  Assigned to me
                </label>
              </div>

              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  type="button"
                  onClick={handleResetFilters}
                >
                  Reset Filters
                </Button>
                <Button variant="primary" type="submit">
                  Apply Filters
                </Button>
              </div>
            </div>
          </form>
        </div>

        {/* Tickets Cards */}
        {isLoadingTickets ? (
          <div className="text-center py-8 bg-secondary rounded-lg border border-gray-600">
            <p className="text-white">Loading tickets...</p>
          </div>
        ) : error ? (
          <div className="text-center py-8 bg-secondary rounded-lg border border-gray-600">
            <p className="text-error">
              Error loading tickets. Please try again.
            </p>
          </div>
        ) : !data?.tickets || data.tickets.length === 0 ? (
          <div className="text-center py-8 bg-secondary rounded-lg border border-gray-600">
            <p className="text-white mb-2">No tickets found</p>
            <p className="text-gray-400 text-sm">
              Try adjusting your filters or search criteria
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {data.tickets.map((ticket) => (
              <div
                key={ticket.id}
                className="bg-secondary rounded-lg shadow-md border border-gray-600 overflow-hidden hover:border-primary transition-colors duration-200 cursor-pointer"
                onClick={() => handleTicketSelect(ticket.id)}
              >
                <div className="p-4">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-white font-medium truncate">
                      {ticket.subject}
                    </h3>
                    <span
                      className={`ml-2 flex-shrink-0 px-2 py-1 text-xs leading-5 font-semibold rounded-full border ${getPriorityColor(
                        ticket.priority,
                      )}`}
                    >
                      {ticket.priority}
                    </span>
                  </div>

                  <div className="flex items-center text-sm text-gray-400 mb-3">
                    <span className="truncate">{ticket.email}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span
                      className={`px-2 py-1 text-xs leading-5 font-semibold rounded-full border ${getStatusColor(
                        ticket.status,
                      )}`}
                    >
                      {ticket.status.replace("_", " ")}
                    </span>
                    <span className="text-xs text-gray-400">
                      {new Date(ticket.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>

                <div className="bg-secondary-dark p-3 border-t border-gray-600 flex justify-between items-center">
                  <div className="text-xs text-gray-400">{ticket.category}</div>

                  <div>
                    {ticket.assignedTo ? (
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-6 w-6">
                          <img
                            className="h-6 w-6 rounded-full"
                            src={ticket.assignedTo.avatar}
                            alt={ticket.assignedTo.displayName}
                          />
                        </div>
                        <div className="ml-2 text-xs text-gray-400">
                          {ticket.assignedTo.displayName}
                        </div>
                      </div>
                    ) : (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e: React.MouseEvent) => {
                          e.stopPropagation();
                          handleAssignToSelf(ticket.id);
                        }}
                      >
                        Assign
                      </Button>
                    )}
                  </div>
                </div>

                <div className="bg-secondary-dark border-t border-gray-600 p-2 flex justify-end space-x-2">
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      handleTicketSelect(ticket.id);
                    }}
                  >
                    View
                  </Button>
                  {ticket.status === "open" && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e: React.MouseEvent) => {
                        e.stopPropagation();
                        handleStatusChange(ticket.id, "in_progress");
                      }}
                    >
                      Start
                    </Button>
                  )}
                  {ticket.status === "in_progress" && (
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={(e: React.MouseEvent) => {
                        e.stopPropagation();
                        handleStatusChange(ticket.id, "resolved");
                      }}
                    >
                      Resolve
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {data && data.pagination && data.pagination.pages > 1 && (
          <div className="flex justify-between items-center mt-6 bg-secondary rounded-lg shadow-md p-4 border border-gray-600">
            <div className="text-sm text-gray-400">
              Showing{" "}
              <span className="font-medium text-white">
                {(data.pagination.page - 1) * data.pagination.limit + 1}
              </span>{" "}
              to{" "}
              <span className="font-medium text-white">
                {Math.min(
                  data.pagination.page * data.pagination.limit,
                  data.pagination.total,
                )}
              </span>{" "}
              of{" "}
              <span className="font-medium text-white">
                {data.pagination.total}
              </span>{" "}
              tickets
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                disabled={page === 1}
                onClick={() => setPage(page - 1)}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                disabled={page === data.pagination.pages}
                onClick={() => setPage(page + 1)}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </AdminDashboardLayout>
  );
}
