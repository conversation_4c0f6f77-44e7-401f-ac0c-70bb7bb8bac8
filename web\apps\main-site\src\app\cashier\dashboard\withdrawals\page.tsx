"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../../../../contexts/AuthContext";
import {
  CashierDashboardLayout,
  WithdrawalsManager,
} from "../../../../components/cashier";
import { usePendingTransactions } from "../../../../hooks/useBank";
import { toast } from "react-hot-toast";

export default function WithdrawalsPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);

  // Use the hook to fetch pending withdrawals
  const {
    data: pendingTransactions = [],
    isLoading: isLoadingTransactions,
    refetch,
  } = usePendingTransactions("withdrawal");

  // Check authorization after auth is loaded
  useEffect(() => {
    // Only proceed with checks if auth loading is complete
    if (!isLoading) {
      if (!user || !user.roles?.banker) {
        router.push("/");
      } else {
        // User is authorized
        setIsAuthorized(true);
      }
    }
  }, [user, router, isLoading]);

  const handleRefresh = () => {
    refetch();
    toast.success("Withdrawals list refreshed");
  };

  // Show loading state or nothing while checking authorization
  if (isLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <p className="text-white text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <CashierDashboardLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white mb-2">
          Withdrawals Management
        </h1>
        <p className="text-gray-400">
          Review and process pending withdrawal requests from bank members.
        </p>
      </div>

      {isLoadingTransactions ? (
        <div className="bg-secondary-light rounded-lg shadow-md p-4 sm:p-6 border border-gray-600 text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-gray-400">Loading withdrawals...</p>
        </div>
      ) : (
        <div className="bg-secondary-light rounded-lg shadow-md p-4 sm:p-6 border border-gray-600">
          <WithdrawalsManager
            withdrawals={pendingTransactions}
            onRefresh={handleRefresh}
          />
        </div>
      )}
    </CashierDashboardLayout>
  );
}
