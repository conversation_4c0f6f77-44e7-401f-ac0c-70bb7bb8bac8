import React, { useState } from "react";
import { BankStatistics } from "../../services/bankService";

interface StatisticsDisplayProps {
  statistics: BankStatistics;
}

export const StatisticsDisplay: React.FC<StatisticsDisplayProps> = ({
  statistics,
}) => {
  const [dateRange, setDateRange] = useState<"7days" | "30days" | "90days">(
    "7days",
  );

  // Get the appropriate number of days based on the selected range
  const getDaysToShow = () => {
    switch (dateRange) {
      case "7days":
        return 7;
      case "30days":
        return 30;
      case "90days":
        return 90;
      default:
        return 7;
    }
  };

  // Get the daily activity data for the selected range
  const getActivityData = () => {
    const days = getDaysToShow();
    return statistics.dailyActivity?.slice(0, days) || [];
  };

  // Calculate totals for the selected period
  const calculatePeriodTotals = () => {
    const activityData = getActivityData();

    return {
      deposits: activityData.reduce((sum, day) => sum + day.deposits, 0),
      withdrawals: activityData.reduce((sum, day) => sum + day.withdrawals, 0),
      transfers: activityData.reduce((sum, day) => sum + day.transfers, 0),
      total: activityData.reduce(
        (sum, day) => sum + day.deposits + day.withdrawals + day.transfers,
        0,
      ),
    };
  };

  const periodTotals = calculatePeriodTotals();

  // Find the max value for scaling the chart
  const findMaxValue = () => {
    const activityData = getActivityData();
    let max = 0;

    activityData.forEach((day) => {
      const dayTotal = day.deposits + day.withdrawals + day.transfers;
      if (dayTotal > max) max = dayTotal;
    });

    return max;
  };

  const maxValue = findMaxValue();

  // Calculate the height percentage for a bar
  const calculateBarHeight = (value: number) => {
    if (maxValue === 0) return 0;
    return (value / maxValue) * 100;
  };

  return (
    <div>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-3">
        <h3 className="text-xl font-semibold text-white">Bank Statistics</h3>
        <div className="flex space-x-2">
          <button
            onClick={() => setDateRange("7days")}
            className={`px-3 py-1 rounded-md ${
              dateRange === "7days"
                ? "bg-primary text-white"
                : "bg-secondary text-gray-400 hover:bg-secondary-light"
            }`}
          >
            7 Days
          </button>
          <button
            onClick={() => setDateRange("30days")}
            className={`px-3 py-1 rounded-md ${
              dateRange === "30days"
                ? "bg-primary text-white"
                : "bg-secondary text-gray-400 hover:bg-secondary-light"
            }`}
          >
            30 Days
          </button>
          <button
            onClick={() => setDateRange("90days")}
            className={`px-3 py-1 rounded-md ${
              dateRange === "90days"
                ? "bg-primary text-white"
                : "bg-secondary text-gray-400 hover:bg-secondary-light"
            }`}
          >
            90 Days
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-secondary-dark p-4 rounded-lg border border-gray-600">
          <h4 className="font-semibold text-white mb-3">Overall Statistics</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-secondary p-3 rounded-lg">
              <div className="text-gray-400 text-sm">Total Deposits</div>
              <div className="text-success font-bold text-xl">
                {statistics.totalDeposits?.count || 0} transactions
              </div>
              <div className="text-success text-lg">
                NS {(statistics.totalDeposits?.amount || 0).toLocaleString()}
              </div>
            </div>
            <div className="bg-secondary p-3 rounded-lg">
              <div className="text-gray-400 text-sm">Total Withdrawals</div>
              <div className="text-error font-bold text-xl">
                {statistics.totalWithdrawals?.count || 0} transactions
              </div>
              <div className="text-error text-lg">
                NS {(statistics.totalWithdrawals?.amount || 0).toLocaleString()}
              </div>
            </div>
            <div className="bg-secondary p-3 rounded-lg">
              <div className="text-gray-400 text-sm">Pending Deposits</div>
              <div className="text-warning font-bold text-xl">
                {statistics.pendingTransactions?.deposits || 0}
              </div>
            </div>
            <div className="bg-secondary p-3 rounded-lg">
              <div className="text-gray-400 text-sm">Pending Withdrawals</div>
              <div className="text-warning font-bold text-xl">
                {statistics.pendingTransactions?.withdrawals || 0}
              </div>
            </div>
          </div>
        </div>

        <div className="bg-secondary-dark p-4 rounded-lg border border-gray-600">
          <h4 className="font-semibold text-white mb-3">
            {dateRange === "7days"
              ? "Last 7 Days"
              : dateRange === "30days"
              ? "Last 30 Days"
              : "Last 90 Days"}{" "}
            Activity
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-secondary p-3 rounded-lg">
              <div className="text-gray-400 text-sm">Deposits</div>
              <div className="text-success font-bold text-xl">
                {periodTotals.deposits}
              </div>
            </div>
            <div className="bg-secondary p-3 rounded-lg">
              <div className="text-gray-400 text-sm">Withdrawals</div>
              <div className="text-error font-bold text-xl">
                {periodTotals.withdrawals}
              </div>
            </div>
            <div className="bg-secondary p-3 rounded-lg">
              <div className="text-gray-400 text-sm">Transfers</div>
              <div className="text-primary font-bold text-xl">
                {periodTotals.transfers}
              </div>
            </div>
            <div className="bg-secondary p-3 rounded-lg">
              <div className="text-gray-400 text-sm">Total Transactions</div>
              <div className="text-white font-bold text-xl">
                {periodTotals.total}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-secondary-dark p-4 rounded-lg border border-gray-600 mb-6">
        <h4 className="font-semibold text-white mb-3">
          Transaction Activity Chart
        </h4>
        <div className="h-64 flex items-end space-x-1 sm:space-x-2 overflow-x-auto pb-2">
          {getActivityData()
            .slice()
            .reverse()
            .map((day, index) => {
              const depositHeight = calculateBarHeight(day.deposits);
              const withdrawalHeight = calculateBarHeight(day.withdrawals);
              const transferHeight = calculateBarHeight(day.transfers);

              return (
                <div
                  key={index}
                  className="flex-1 min-w-[30px] sm:min-w-0 flex flex-col items-center"
                >
                  <div className="w-full h-48 flex flex-col-reverse">
                    <div
                      className="w-full bg-success"
                      style={{ height: `${depositHeight}%` }}
                      title={`Deposits: ${day.deposits}`}
                    ></div>
                    <div
                      className="w-full bg-error"
                      style={{ height: `${withdrawalHeight}%` }}
                      title={`Withdrawals: ${day.withdrawals}`}
                    ></div>
                    <div
                      className="w-full bg-primary"
                      style={{ height: `${transferHeight}%` }}
                      title={`Transfers: ${day.transfers}`}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-400 mt-2 whitespace-nowrap overflow-hidden text-ellipsis w-full text-center">
                    {new Date(day.date).toLocaleDateString(undefined, {
                      month: "short",
                      day: "numeric",
                    })}
                  </div>
                </div>
              );
            })}
        </div>
        <div className="flex flex-wrap justify-center mt-4 gap-4 sm:gap-6">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-success mr-2"></div>
            <span className="text-xs sm:text-sm text-gray-400">Deposits</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-error mr-2"></div>
            <span className="text-xs sm:text-sm text-gray-400">
              Withdrawals
            </span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-primary mr-2"></div>
            <span className="text-xs sm:text-sm text-gray-400">Transfers</span>
          </div>
        </div>
      </div>

      <div className="bg-secondary-dark p-4 rounded-lg border border-gray-600">
        <h4 className="font-semibold text-white mb-3">
          Top Users by Transaction Volume
        </h4>
        <div className="overflow-x-auto">
          <table className="min-w-full border-collapse">
            <thead>
              <tr className="bg-secondary">
                <th className="px-3 py-2 sm:p-3 text-left text-white text-sm sm:text-base">
                  User
                </th>
                <th className="px-3 py-2 sm:p-3 text-left text-white text-sm sm:text-base">
                  Transactions
                </th>
                <th className="px-3 py-2 sm:p-3 text-left text-white text-sm sm:text-base">
                  Total Amount
                </th>
              </tr>
            </thead>
            <tbody>
              {(statistics.userActivity || []).map((user, index) => (
                <tr
                  key={index}
                  className="border-t border-gray-600 hover:bg-secondary"
                >
                  <td className="px-3 py-2 sm:p-3 text-white text-sm sm:text-base">
                    {user.username}
                  </td>
                  <td className="px-3 py-2 sm:p-3 text-white text-sm sm:text-base">
                    {user.transactionCount}
                  </td>
                  <td className="px-3 py-2 sm:p-3 text-white text-sm sm:text-base">
                    NS {user.totalAmount.toLocaleString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default StatisticsDisplay;
