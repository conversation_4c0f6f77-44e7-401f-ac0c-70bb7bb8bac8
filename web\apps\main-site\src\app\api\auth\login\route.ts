import { NextRequest, NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import prisma from "@/lib/prisma";
import jwt from "jsonwebtoken";

const JWT_SECRET = process.env.JWT_SECRET || "your-development-secret-key";

export const OPTIONS = async (req: NextRequest) => {
  // Handle OPTIONS request for CORS preflight
  const response = new NextResponse(null, { status: 204 });

  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT, DELETE, OPTIONS",
  );
  response.headers.set(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization",
  );

  return response;
};

export const POST = async (req: NextRequest) => {
  try {
    const { email, password } = await req.json();

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { error: "Email and password are required" },
        { status: 400 },
      );
    }

    // First, try to find a credential with this email
    const credential = await prisma.userCredential.findFirst({
      where: {
        type: "email",
        identifier: email,
      },
      include: {
        user: true,
      },
    });

    // If credential exists, verify password against it
    if (credential && credential.passwordHash) {
      const isPasswordValid = await bcrypt.compare(
        password,
        credential.passwordHash,
      );

      if (isPasswordValid) {
        // Update last used timestamp
        await prisma.userCredential.update({
          where: { id: credential.id },
          data: { lastUsedAt: new Date() },
        });

        // Use the user from the credential relation
        const user = credential.user;

        // Continue with the user object
        if (user) {
          // Create JWT token
          const token = jwt.sign(
            { id: user.id, email: user.email },
            JWT_SECRET,
            {
              expiresIn: "7d",
            },
          );

          // Return user data and token
          return NextResponse.json({
            user: {
              id: user.id,
              username: user.username,
              displayName: user.displayName,
              email: user.email,
              avatar: user.avatar,
              balance: user.balance,
              isEmailVerified: user.isEmailVerified,
              preferences: {
                defaultView: user.defaultView,
                notifications: {
                  transfers: user.notifyTransfers,
                  deposits: user.notifyDeposits,
                  withdrawals: user.notifyWithdrawals,
                  newsAndEvents: user.notifyNewsEvents,
                },
              },
              connectedAccounts: {
                discord: user.discordConnected,
                discordId: user.discordId,
                facebook: user.facebookConnected,
                facebookId: user.facebookId,
              },
              merchant: {
                status: user.merchantStatus,
                merchantId: user.merchantId,
                slug: user.merchantSlug,
              },
              auctions: {
                hasCreated: user.hasCreatedAuctions,
                auctionCount: user.auctionCount,
              },
              roles: {
                admin: user.isAdmin,
                editor: user.isEditor,
                banker: user.isBanker,
                chatModerator: user.isChatModerator,
                volunteerCoordinator: user.isVolunteerCoordinator,
                leadManager: user.isLeadManager,
                salesManager: user.isSalesManager,
              },
            },
            token,
          });
        }
      }
    }

    // Fallback to legacy authentication method (for backward compatibility)
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      return NextResponse.json(
        { error: "Invalid credentials" },
        { status: 401 },
      );
    }

    // Verify password against the user's passwordHash
    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);

    if (!isPasswordValid) {
      return NextResponse.json(
        { error: "Invalid credentials" },
        { status: 401 },
      );
    }

    // If we get here using the legacy method, create a credential record for future use
    // First check if a credential already exists to avoid unique constraint errors
    const existingCredential = await prisma.userCredential.findFirst({
      where: {
        type: "email",
        identifier: email,
      },
    });

    if (!existingCredential) {
      // Only create if it doesn't exist
      await prisma.userCredential.create({
        data: {
          userId: user.id,
          type: "email",
          identifier: email,
          passwordHash: user.passwordHash,
          lastUsedAt: new Date(),
        },
      });
    } else {
      // Update the last used timestamp on the existing credential
      await prisma.userCredential.update({
        where: { id: existingCredential.id },
        data: { lastUsedAt: new Date() },
      });
    }

    // Create JWT token
    const token = jwt.sign({ id: user.id, email: user.email }, JWT_SECRET, {
      expiresIn: "7d",
    });

    // Return user data and token
    return NextResponse.json({
      user: {
        id: user.id,
        username: user.username,
        displayName: user.displayName,
        email: user.email,
        avatar: user.avatar,
        balance: user.balance,
        isEmailVerified: user.isEmailVerified,
        preferences: {
          defaultView: user.defaultView,
          notifications: {
            transfers: user.notifyTransfers,
            deposits: user.notifyDeposits,
            withdrawals: user.notifyWithdrawals,
            newsAndEvents: user.notifyNewsEvents,
          },
        },
        connectedAccounts: {
          discord: user.discordConnected,
          discordId: user.discordId,
          facebook: user.facebookConnected,
          facebookId: user.facebookId,
        },
        merchant: {
          status: user.merchantStatus,
          merchantId: user.merchantId,
          slug: user.merchantSlug,
        },
        auctions: {
          hasCreated: user.hasCreatedAuctions,
          auctionCount: user.auctionCount,
        },
        roles: {
          admin: user.isAdmin,
          editor: user.isEditor,
          banker: user.isBanker,
          chatModerator: user.isChatModerator,
          volunteerCoordinator: user.isVolunteerCoordinator,
          leadManager: user.isLeadManager,
        },
      },
      token,
    });
  } catch (error: any) {
    console.error("Login error:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error?.message },
      { status: 500 },
    );
  }
};
