# UI Component Library

Shared UI components library providing reusable React components, hooks, and design system elements used across Bank of Styx applications.

## Structure

- **src/** - Source code for UI components organized by component type
- **dist/** - Built package output for distribution
- **package.json** - Package configuration and dependencies
- **tsconfig.json** - TypeScript configuration for the UI library

## Component Categories

The UI library includes:

- Button components with variants and states
- Card components for content display
- Form input components
- Modal and dialog components
- Navigation and sidebar components
- Loading and feedback components
- Rich text editor components
- Search and pagination components

This package promotes design consistency and code reuse across the entire Bank of Styx platform.
