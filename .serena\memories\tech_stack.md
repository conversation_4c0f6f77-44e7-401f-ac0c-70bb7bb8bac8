# Bank of Styx - Technology Stack

## Frontend Stack
- **Framework**: Next.js 13.4.2 with App Router
- **UI Library**: React 18.2.0
- **Language**: TypeScript 5.8.3
- **Styling**: TailwindCSS 3.3.2
- **State Management**: TanStack Query 5.17.19 (server state)
- **Rich Text**: React Quill 2.0.0
- **Charts**: Recharts 2.15.3
- **Toast Notifications**: React Hot Toast
- **Image Processing**: Sharp, React Image Crop

## Backend & Database
- **Runtime**: Node.js >= 18.0.0
- **Database**: MySQL 8.0
- **ORM**: Prisma 6.6.0
- **Authentication**: JWT (jsonwebtoken) + Discord OAuth
- **Email**: Nodemailer 6.10.1
- **Payments**: Stripe integration
- **Real-time**: Server-Sent Events (SSE)

## Development Tools
- **Package Manager**: PNPM 8.6.0
- **Linting**: ESLint with Next.js config
- **Code Formatting**: Prettier
- **Testing**: <PERSON><PERSON> (E2E), <PERSON> (Load Testing)
- **Type Checking**: TypeScript compiler

## Monorepo Structure
- **Main App**: `web/apps/main-site` (Next.js application)
- **API Modules**: `web/apps/api` (Standalone API endpoints)
- **Shared UI**: `web/packages/ui` (Reusable components)
- **Config**: `web/packages/config` (Shared configurations)

## Key Dependencies
- `@bank-of-styx/ui`: Internal shared UI components
- `@prisma/client`: Database client
- `@tanstack/react-query`: Server state management
- `bcryptjs`: Password hashing
- `uuid`: Unique ID generation
- `dompurify`: HTML sanitization