"use client";

import { Button } from "@bank-of-styx/ui";
import Image from "next/image";
import { CartItem } from "@/hooks/useCart";
import { CartHoldTimer } from "./CartHoldTimer";

interface CartItemCardProps {
  item: CartItem;
  onQuantityChange: (itemId: string, quantity: number) => void;
  onRemove: (itemId: string) => void;
  isProcessing: boolean;
}

export function CartItemCard({
  item,
  onQuantityChange,
  onRemove,
  isProcessing,
}: CartItemCardProps) {
  // Check for both individual ticket holds and event capacity holds
  const hasIndividualHold = !!item.ticketHold;
  const hasEventCapacityHold = !!item.eventCapacityHold;
  const isOnHold = hasIndividualHold || hasEventCapacityHold || !!item.holdExpiresAt;
  const isCodeRedemption = item.isCodeRedemption;
  
  // Get hold expiration time from either hold type
  const holdExpiresAt = 
    item.ticketHold?.expiresAt || 
    item.eventCapacityHold?.expiresAt || 
    item.holdExpiresAt;
  
  // Calculate ticket count based on hold type
  const ticketsCount = hasEventCapacityHold 
    ? item.eventCapacityHold?.quantity || item.quantity
    : hasIndividualHold
    ? item.ticketHold?.tickets?.filter((t) => t.status === "HELD").length || 0
    : item.ticketCount || item.quantity;
    
  const hasSeating = item.ticketHold?.tickets?.some((t) => t.seatInfo);

  return (
    <div className="p-4">
      {/* Hold Timer */}
      {isOnHold && holdExpiresAt && (
        <CartHoldTimer expiresAt={holdExpiresAt} ticketCount={ticketsCount} />
      )}

      <div className="flex flex-col md:flex-row">
        {/* Product Image */}
        <div className="w-full md:w-24 h-24 flex-shrink-0 mb-4 md:mb-0">
          {item.product.image ? (
            <Image
              src={item.product.image}
              alt={item.product.name}
              width={96}
              height={96}
              className="w-full h-full object-cover rounded-md"
            />
          ) : (
            <div className="w-full h-full bg-secondary flex items-center justify-center rounded-md">
              <span className="text-text-muted text-sm">No image</span>
            </div>
          )}
        </div>

        {/* Product Details */}
        <div className="flex-grow md:ml-4">
          <div className="flex flex-col md:flex-row md:justify-between">
            <div>
              <h3 className="font-semibold">{item.product.name}</h3>
              <p className="text-text-muted text-sm">
                {item.product.category.name}
              </p>
              {item.product.event && (
                <p className="text-text-muted text-sm">
                  Event: {item.product.event.name}
                </p>
              )}

              {/* Code Redemption Indicator */}
              {isCodeRedemption && (
                <div className="mt-2">
                  <span className="bg-success-light text-success-dark text-xs px-2 py-1 rounded">
                    🎁 Added via redemption code
                  </span>
                  {item.redemptionCode && (
                    <span className="ml-2 text-xs text-text-muted font-mono">
                      {item.redemptionCode.code}
                    </span>
                  )}
                </div>
              )}

              {/* Ticket Information */}
              {isOnHold && (
                <div className="mt-2">
                  <div className="flex items-center gap-2">
                    <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                      {ticketsCount} {ticketsCount === 1 ? "ticket" : "tickets"}{" "}
                      reserved
                    </span>
                    {hasSeating && (
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                        Seats assigned
                      </span>
                    )}
                  </div>

                  {hasSeating && item.ticketHold?.tickets && (
                    <div className="mt-1 text-xs text-gray-600">
                      Seat{ticketsCount > 1 ? "s" : ""}:{" "}
                      {item.ticketHold.tickets
                        .filter((t) => t.status === "HELD")
                        .map((t) => t.seatInfo?.label || "-")
                        .join(", ")}
                    </div>
                  )}
                </div>
              )}
            </div>
            <div className="mt-2 md:mt-0 text-right">
              {isCodeRedemption ? (
                <p className="font-semibold text-success">FREE</p>
              ) : (
                <>
                  <p className="font-semibold">${item.product.price.toFixed(2)}</p>
                  {item.quantity > 1 && (
                    <p className="text-sm text-text-muted">
                      ${(item.product.price * item.quantity).toFixed(2)} total
                    </p>
                  )}
                </>
              )}
            </div>
          </div>

          {/* Quantity Controls */}
          <div className="flex items-center justify-between mt-4">
            {isCodeRedemption ? (
              // For code-redeemed items, show locked quantity
              <div className="flex items-center">
                <span className="text-text-muted text-sm">Quantity: </span>
                <span className="mx-2 font-medium">{item.quantity}</span>
                <span className="text-xs text-text-muted">(Fixed for redemption codes)</span>
              </div>
            ) : (
              // Normal quantity controls for regular items
              <div className="flex items-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onQuantityChange(item.id, item.quantity - 1)}
                  disabled={item.quantity <= 1 || isProcessing}
                >
                  -
                </Button>
                <span className="mx-3 font-medium">{item.quantity}</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onQuantityChange(item.id, item.quantity + 1)}
                  disabled={
                    (item.product.inventory !== null &&
                      item.quantity >= item.product.inventory) ||
                    isProcessing
                  }
                >
                  +
                </Button>
              </div>
            )}
            <Button
              variant="primary"
              color="danger"
              size="sm"
              onClick={() => onRemove(item.id)}
              disabled={isProcessing}
            >
              Remove
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
