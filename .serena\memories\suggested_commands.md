# Bank of Styx - Development Commands

## Primary Development Commands

### Development Server
```bash
# Start development server (from root)
pnpm dev
# Alternative: Start with specific host
cd web && pnpm dev -- -H ************
```

### Building and Quality Checks
```bash
# Build entire project
pnpm build

# Lint all packages  
pnpm lint

# Format code with Prettier
pnpm format

# Type check without emit
cd web/apps/main-site && pnpm check
```

### Database Operations (Critical Workflow)
```bash
# Navigate to main site
cd web/apps/main-site

# Run database migrations
pnpm prisma migrate dev

# Apply migrations to database (after creating migration files)
npx prisma migrate deploy

# Generate Prisma client after schema changes
npx prisma generate

# Check migration status
npx prisma migrate status

# Seed the database
pnpm prisma:seed

# Open Prisma Studio
pnpm prisma:studio
```

### Testing Commands
```bash
cd web/apps/main-site

# Setup test users
pnpm test:setup

# Run E2E tests
pnpm test:e2e

# Load testing
pnpm test:load
pnpm test:concurrent

# Full test suite
pnpm test:full
```

### UI Package Development
```bash
# Build shared UI package
pnpm ui:build

# Develop shared UI package  
pnpm ui:dev
```

## Windows-Specific Commands
- Use `dir` instead of `ls`
- Use `cd` for navigation
- Use PowerShell or Command Prompt
- File paths use backslashes (`\`) 

## Critical Workflow Reminder
**After creating Prisma migration files, ALWAYS:**
1. `npx prisma migrate deploy` - Apply migrations to database
2. `npx prisma generate` - Update Prisma client 
3. `npx prisma migrate status` - Verify success