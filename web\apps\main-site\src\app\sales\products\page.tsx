"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { SalesDashboardLayout } from "@/components/sales/SalesDashboardLayout";
import { ProductList } from "@/components/sales/ProductList";
import { Button, Card } from "@bank-of-styx/ui";
import Link from "next/link";

export default function ProductsPage() {
  const { user, isLoading } = useAuth();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const router = useRouter();

  // Check if user is authorized to access this page
  useEffect(() => {
    if (!isLoading) {
      if (!user || !user.roles?.salesManager) {
        router.push("/");
      } else {
        setIsAuthorized(true);
      }
    }
  }, [user, isLoading, router]);

  if (isLoading) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  if (!isAuthorized) {
    return null; // Don't render anything while redirecting
  }

  return (
    <SalesDashboardLayout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Products</h1>
        <Link href="/sales/products/create">
          <Button variant="primary">Create Product</Button>
        </Link>
      </div>

      <Card>
        <ProductList />
      </Card>
    </SalesDashboardLayout>
  );
}
