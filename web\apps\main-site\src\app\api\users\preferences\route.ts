import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../lib/prisma";
import jwt from "jsonwebtoken";

const JWT_SECRET = process.env.JWT_SECRET || "your-development-secret-key";

// Helper function to verify token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

export const OPTIONS = async (req: NextRequest) => {
  // Handle OPTIONS request for CORS preflight
  const response = new NextResponse(null, { status: 204 });

  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set("Access-Control-Allow-Methods", "GET, PUT, OPTIONS");
  response.headers.set(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization",
  );

  return response;
};

export async function PUT(req: NextRequest) {
  try {
    // Get authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    // Get request body
    const { defaultView, notifications } = await req.json();

    // Validate input
    if (!defaultView || !notifications) {
      return NextResponse.json(
        { error: "Default view and notifications are required" },
        { status: 400 },
      );
    }

    // Validate defaultView value
    const validViews = [
      "dashboard",
      "transactions",
      "transfer",
      "pay-code",
      "donate",
    ];
    if (!validViews.includes(defaultView)) {
      return NextResponse.json(
        { error: "Invalid default view value" },
        { status: 400 },
      );
    }

    // Update user preferences
    const updatedUser = await prisma.user.update({
      where: { id: decoded.id as string },
      data: {
        defaultView,
        notifyTransfers: notifications.transfers,
        notifyDeposits: notifications.deposits,
        notifyWithdrawals: notifications.withdrawals,
        notifyNewsEvents: notifications.newsAndEvents,
        notifyAuctions:
          notifications.auctions !== undefined ? notifications.auctions : true,
        notifyChat:
          notifications.chat !== undefined ? notifications.chat : true,
        notifyAdmin:
          notifications.admin !== undefined ? notifications.admin : true,
      },
    });

    // Return updated user preferences
    return NextResponse.json({
      success: true,
      preferences: {
        defaultView: updatedUser.defaultView,
        notifications: {
          transfers: updatedUser.notifyTransfers,
          deposits: updatedUser.notifyDeposits,
          withdrawals: updatedUser.notifyWithdrawals,
          newsAndEvents: updatedUser.notifyNewsEvents,
          auctions: updatedUser.notifyAuctions,
          chat: updatedUser.notifyChat,
          admin: updatedUser.notifyAdmin,
        },
      },
    });
  } catch (error: any) {
    console.error("Preferences update error:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error?.message },
      { status: 500 },
    );
  }
}
