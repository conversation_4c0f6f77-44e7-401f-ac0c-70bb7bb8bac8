# News System Components

Components for the news and content management system.

## Components

- **NewsDashboardLayout.tsx** - Layout component for news management dashboard
- **NewsEditor.tsx** - Rich text editor for creating and editing news articles
- **NewsArticleCard.tsx** - Card component for displaying news articles
- **ArticlePreviewModal.tsx** - Modal for previewing articles before publishing
- **CategorySelector.tsx** - Component for selecting news categories
- **FeaturedImageUploader.tsx** - Image upload component for article featured images
- **ArticleViewTracker.js** - Analytics component for tracking article views
- **index.ts** - Component exports

These components provide a complete content management system for creating, editing, and displaying news articles with rich media support and analytics tracking.
