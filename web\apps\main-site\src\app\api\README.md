# API Routes

Backend API endpoints for the Bank of Styx platform providing RESTful services for all application functionality.

## API Categories

### Core Services

- **auth/** - Authentication and user session management
- **user/** - Individual user profile and account operations
- **users/** - User management and administration
- **images/** - Image processing and serving
- **uploads/** - File upload handling and management

### Banking & Financial

- **bank/** - Banking operations, transactions, and account management
- **cashier/** - Cashier portal operations and transaction processing

### E-commerce

- **cart/** - Shopping cart management and item operations
- **checkout/** - Payment processing and order completion
- **products/** - Product catalog and inventory management
- **product-categories/** - Product category management
- **orders/** - Order processing and fulfillment
- **sales/** - Sales management and reporting

### Content & Communication

- **news/** - News article management and publishing
- **notifications/** - Real-time notification system
- **support/** - Support ticket system and help desk
- **contact/** - Contact form and communication handling

### Events & Volunteers

- **events/** - Event management and calendar operations
- **event-categories/** - Event category management
- **volunteer/** - Volunteer management and shift scheduling

### Administration

- **admin/** - Administrative functions and system management
- **setup/** - System setup and configuration
- **cron/** - Scheduled tasks and background jobs
- **test/** - Development and testing endpoints

All API routes follow RESTful conventions and return JSON responses with appropriate HTTP status codes.
