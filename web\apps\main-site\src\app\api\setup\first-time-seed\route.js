import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";

// Simple Lorem Ipsum generator for the seed endpoint
function generateLoremArticle(paragraphs = 4) {
  let article = "";
  
  // Add a main heading
  article += `<h2>Lorem Ipsum Heading</h2>\n`;
  
  // Add first paragraph
  article += `<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>\n`;
  
  // Add second paragraph
  article += `<p>Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>\n`;
  
  // Add a subheading
  article += `<h3>Subheading</h3>\n`;
  
  // Add a list
  article += `<ul>\n`;
  for (let i = 0; i < 4; i++) {
    article += `  <li>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>\n`;
  }
  article += `</ul>\n`;
  
  // Add remaining paragraphs
  for (let i = 0; i < paragraphs - 2; i++) {
    article += `<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>\n`;
  }
  
  return article;
}

// This endpoint handles first-time database seeding with categories and sample articles
export async function GET() {
  try {
    console.log("Checking if database needs first-time seeding...");

    // Check if categories exist
    const existingCategories = await prisma.newsCategory.count();

    // Check if articles exist
    const existingArticles = await prisma.newsArticle.count();

    // If we already have both categories and articles, no need to seed
    if (existingCategories > 0 && existingArticles > 0) {
      return NextResponse.json({
        success: true,
        message: "Database already seeded",
        seeded: false,
        categoriesCount: existingCategories,
        articlesCount: existingArticles
      });
    }

    console.log("First-time seeding required. Creating categories and sample articles...");

    // Create categories if they don't exist
    let categories = [];
    if (existingCategories === 0) {
      // Create the four default categories
      await prisma.newsCategory.createMany({
        data: [
          {
            name: "Featured",
            slug: "featured",
            description: "Featured articles from the Bank of Styx",
          },
          {
            name: "News",
            slug: "news",
            description: "Latest news and updates",
          },
          {
            name: "Announcements",
            slug: "announcements",
            description: "Official announcements from the Bank of Styx",
          },
          {
            name: "Events",
            slug: "events",
            description: "Upcoming and past events",
          },
        ],
        skipDuplicates: true,
      });

      // Fetch the created categories to get their IDs
      categories = await prisma.newsCategory.findMany();
      console.log(`Created ${categories.length} categories`);
    } else {
      // Fetch existing categories
      categories = await prisma.newsCategory.findMany();
      console.log(`Using ${categories.length} existing categories`);
    }

    // Create sample articles if they don't exist
    let articles = [];
    if (existingArticles === 0 && categories.length > 0) {
      // Find an admin user to be the author
      let adminUser = await prisma.user.findFirst({
        where: { isAdmin: true }
      });
      
      // If no admin user exists, create one
      if (!adminUser) {
        try {
          adminUser = await prisma.user.create({
            data: {
              username: "ChesterBait",
              displayName: "ChesterBait",
              email: "<EMAIL>",
              passwordHash: "$2b$10$cqY6g1kYtfwYZKPjsnWBLudBfIGpDqyU1yp26l8L5LcKTgNvq8Hhm", 
              isAdmin: true,
              isEditor: true,
              isEmailVerified: true,
              balance: 1000,
              status: "active",
              notifyTransfers: true,
              notifyDeposits: true,
              notifyWithdrawals: true,
              notifyNewsEvents: true
            }
          });
          console.log("Created admin user for article authorship");
        } catch (error) {
          console.error("Error creating admin user:", error);
          // Try to find any user as a fallback
          const anyUser = await prisma.user.findFirst();
          if (anyUser) {
            adminUser = anyUser;
            console.log("Using existing user for article authorship");
          } else {
            throw new Error("No users found in the database and couldn't create one");
          }
        }
      }

      // Find the "Featured" and "News" categories
      const featuredCategory = categories.find(c => c.slug === "featured") || categories[0];
      const newsCategory = categories.find(c => c.slug === "news") || categories[0];
      
      // Create two sample articles
      const now = new Date();
      
      // Article 1 - Featured
      const article1 = await prisma.newsArticle.create({
        data: {
          title: "Welcome to the Bank of Styx",
          slug: "welcome-to-bank-of-styx",
          content: `<h2>Welcome to the Bank of Styx!</h2>
<p>Welcome to the official website of the Bank of Styx, your trusted financial institution for the Pirate Rinfair community.</p>
<p>We're excited to have you here and look forward to serving all your banking needs.</p>
<h3>Our Services</h3>
<ul>
  <li>Secure banking for all your needs</li>
  <li>Easy transfers between accounts</li>
  <li>Pay code system for quick payments</li>
  <li>Merchant integration</li>
</ul>
<p>Visit our help section to learn more about how to use our services!</p>
${generateLoremArticle(2)}`,
          excerpt: "Welcome to the Bank of Styx, your trusted financial institution for the Pirate Rinfair community.",
          image: "/images/bank-building.jpg",
          authorId: adminUser.id,
          categoryId: featuredCategory.id,
          status: "published",
          featured: true,
          publishedAt: now,
          views: 0
        }
      });
      
      // Article 2 - News
      const article2 = await prisma.newsArticle.create({
        data: {
          title: "New Features Coming Soon",
          slug: "new-features-coming-soon",
          content: `<h2>Exciting New Features Coming Soon!</h2>
<p>We're constantly working to improve your banking experience at the Bank of Styx.</p>
<p>Here's a preview of some exciting new features we're developing for you.</p>
<h3>Coming Soon</h3>
<ul>
  <li>Enhanced security features</li>
  <li>Mobile app for on-the-go banking</li>
  <li>Improved user interface</li>
  <li>New merchant partnerships</li>
</ul>
<p>Stay tuned for more updates!</p>
${generateLoremArticle(3)}`,
          excerpt: "We're working on exciting new features to enhance your banking experience.",
          image: "/images/coming-soon.jpg",
          authorId: adminUser.id,
          categoryId: newsCategory.id,
          status: "published",
          featured: false,
          publishedAt: new Date(now.getTime() - ********), // 1 day ago
          views: 0
        }
      });
      
      articles = [article1, article2];
      console.log(`Created ${articles.length} sample articles`);
    } else {
      console.log(`Using ${existingArticles} existing articles`);
    }

    return NextResponse.json({
      success: true,
      message: "First-time database seeding completed successfully",
      seeded: true,
      categoriesCount: categories.length,
      articlesCount: articles.length
    });
  } catch (error) {
    console.error("Error during first-time seeding:", error);
    return NextResponse.json(
      {
        error: "Failed to seed database",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
