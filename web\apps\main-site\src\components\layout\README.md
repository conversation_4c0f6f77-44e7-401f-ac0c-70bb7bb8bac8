# Layout Components

Core layout components that structure the application's navigation and page organization.

## Components

- **MainLayout.tsx** - Primary layout wrapper component used across the application
- **Header.tsx** - Application header with branding, navigation, and user controls
- **Navigation.tsx** - Main navigation component with menu items and user-specific links
- **Footer.tsx** - Application footer with links and platform information
- **Breadcrumbs.tsx** - Breadcrumb navigation component for page hierarchy
- **Navigation.backup.tsx** - Backup version of navigation component

These layout components provide the consistent structure and navigation experience across all pages of the Bank of Styx platform, ensuring a cohesive user interface.
