import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/users/[id] - Get a specific user
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;

    // Check if user is authenticated and has volunteer coordinator role
    const currentUser = await getCurrentUser(req);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");
    if (!hasCoordinatorRole) {
      return NextResponse.json(
        { error: "Unauthorized - Coordinator role required" },
        { status: 403 },
      );
    }

    // Get the user
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        displayName: true,
        email: true,
        avatar: true,
      },
    });

    // Map the database fields to the expected response format
    const mappedUser = {
      id: user?.id,
      name: user?.displayName,
      email: user?.email,
      image: user?.avatar,
    };

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    return NextResponse.json(mappedUser);
  } catch (error) {
    console.error("Error fetching user:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch user",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
