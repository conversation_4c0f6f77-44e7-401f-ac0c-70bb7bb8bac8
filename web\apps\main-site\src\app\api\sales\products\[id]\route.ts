import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";
import { generateMultipleRedemptionCodes } from "@/lib/redemptionCodes";
import { TicketStatus } from "@prisma/client";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
interface Params {
  params: {
    id: string;
  };
}

// GET /api/sales/products/[id] - Get a specific product (sales manager)
export async function GET(req: NextRequest, { params }: Params) {
  try {
    const { id } = params;

    // Check if user is authenticated and has sales manager role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasSalesRole = await userHasRole(req, "salesManager");
    if (!hasSalesRole) {
      return NextResponse.json(
        { error: "Unauthorized - Sales Manager role required" },
        { status: 403 },
      );
    }

    // Get the product
    const product = await prisma.product.findUnique({
      where: { id },
      include: {
        category: true,
        event: {
          select: {
            id: true,
            name: true,
            startDate: true,
            endDate: true,
            capacity: true,
          },
        },
        redemptionCodes: {
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    });

    if (!product) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 });
    }

    return NextResponse.json({ product });
  } catch (error) {
    console.error("Error fetching product:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}

// PUT /api/sales/products/[id] - Update a product (sales manager)
export async function PUT(req: NextRequest, { params }: Params) {
  try {
    const { id } = params;

    // Check if user is authenticated and has sales manager role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasSalesRole = await userHasRole(req, "salesManager");
    if (!hasSalesRole) {
      return NextResponse.json(
        { error: "Unauthorized - Sales Manager role required" },
        { status: 403 },
      );
    }

    // Get request body
    const {
      name,
      description,
      shortDescription,
      price,
      image,
      isActive,
      affectsCapacity,
      inventory,
      categoryId,
      eventId,
      isFree,
    } = await req.json();

    // Validate required fields
    if (!name || !categoryId || price === undefined) {
      return NextResponse.json(
        { error: "Name, categoryId, and price are required" },
        { status: 400 },
      );
    }

    // Validate price
    if (isNaN(price) || price < 0) {
      return NextResponse.json(
        { error: "Price must be a non-negative number" },
        { status: 400 },
      );
    }

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id },
      include: {
        redemptionCodes: true,
      },
    });

    if (!existingProduct) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 });
    }

    // Check if category exists
    const category = await prisma.productCategory.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 },
      );
    }

    // Check if event exists if eventId is provided
    if (eventId) {
      const event = await prisma.event.findUnique({
        where: { id: eventId },
      });

      if (!event) {
        return NextResponse.json({ error: "Event not found" }, { status: 404 });
      }
    }

    // Parse inventory
    const parsedInventory = inventory !== undefined ? (inventory === null || inventory === '' ? null : parseInt(inventory)) : existingProduct.inventory;

    // Update the product with potential redemption code generation
    const product = await prisma.$transaction(async (tx) => {
      // Update the product
      const updatedProduct = await tx.product.update({
        where: { id },
        data: {
          name,
          description,
          shortDescription,
          price: parseFloat(price),
          image,
          isActive: isActive !== undefined ? isActive : existingProduct.isActive,
          affectsCapacity:
            affectsCapacity !== undefined
              ? affectsCapacity
              : existingProduct.affectsCapacity,
          inventory: parsedInventory,
          categoryId,
          eventId,
          isFree: isFree !== undefined ? isFree : existingProduct.isFree,
        },
      });

      // Handle redemption codes if product is now free
      const wasFree = existingProduct.isFree;
      const nowFree = isFree !== undefined ? isFree : existingProduct.isFree;
      
      if (nowFree && parsedInventory && parsedInventory > 0) {
        const existingCodeCount = existingProduct.redemptionCodes.length;
        
        // If we need more codes than we have, generate additional ones
        if (parsedInventory > existingCodeCount) {
          const additionalCodesNeeded = parsedInventory - existingCodeCount;
          const newCodes = generateMultipleRedemptionCodes(additionalCodesNeeded);
          const redemptionCodes = newCodes.map(code => ({
            code,
            productId: id,
          }));

          await tx.redemptionCode.createMany({
            data: redemptionCodes,
          });

          // Create corresponding HELD tickets to reserve inventory
          const tickets = Array(additionalCodesNeeded)
            .fill(null)
            .map(() => ({
              productId: id,
              status: TicketStatus.HELD,
            }));

          await tx.ticket.createMany({
            data: tickets,
          });
        }
        // If we have too many codes, deactivate the excess ones
        else if (parsedInventory < existingCodeCount) {
          const excessCodes = existingProduct.redemptionCodes
            .slice(parsedInventory)
            .map(code => code.id);
          
          await tx.redemptionCode.updateMany({
            where: {
              id: { in: excessCodes },
            },
            data: {
              isActive: false,
            },
          });

          // Remove corresponding HELD tickets
          const ticketsToRemove = await tx.ticket.findMany({
            where: {
              productId: id,
              status: TicketStatus.HELD,
            },
            take: existingCodeCount - parsedInventory,
          });

          if (ticketsToRemove.length > 0) {
            await tx.ticket.deleteMany({
              where: {
                id: { in: ticketsToRemove.map(t => t.id) },
              },
            });
          }
        }
      }
      // If product is no longer free, deactivate all redemption codes and remove HELD tickets
      else if (wasFree && !nowFree) {
        await tx.redemptionCode.updateMany({
          where: {
            productId: id,
          },
          data: {
            isActive: false,
          },
        });

        // Remove all HELD tickets for this product
        await tx.ticket.deleteMany({
          where: {
            productId: id,
            status: TicketStatus.HELD,
          },
        });
      }

      return updatedProduct;
    });

    return NextResponse.json({ product });
  } catch (error) {
    console.error("Error updating product:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}

// DELETE /api/sales/products/[id] - Delete a product (sales manager)
export async function DELETE(req: NextRequest, { params }: Params) {
  try {
    const { id } = params;

    // Check if user is authenticated and has sales manager role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasSalesRole = await userHasRole(req, "salesManager");
    if (!hasSalesRole) {
      return NextResponse.json(
        { error: "Unauthorized - Sales Manager role required" },
        { status: 403 },
      );
    }

    // Check if product exists and get related data
    const existingProduct = await prisma.product.findUnique({
      where: { id },
      include: {
        redemptionCodes: true,
        tickets: true,
        cartItems: {
          include: {
            cart: true,
          },
        },
        orderItems: {
          include: {
            order: true,
          },
        },
      },
    });

    if (!existingProduct) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 });
    }

    // Check for active orders - prevent deletion if product has been sold
    const activeOrders = existingProduct.orderItems.filter(
      item => item.order.status === 'paid' || item.order.status === 'fulfilled'
    );

    if (activeOrders.length > 0) {
      return NextResponse.json(
        { 
          error: "Cannot delete product - it has been sold in completed orders. Please deactivate it instead." 
        },
        { status: 400 }
      );
    }

    // Check for items currently in carts - warn but allow deletion
    const activeCartItems = existingProduct.cartItems.length;
    if (activeCartItems > 0) {
      console.log(`Warning: Deleting product ${id} that has ${activeCartItems} items in user carts`);
    }

    // Comprehensive delete with transaction
    await prisma.$transaction(async (tx) => {
      // 1. Delete all redemption codes (if any)
      if (existingProduct.redemptionCodes.length > 0) {
        await tx.redemptionCode.deleteMany({
          where: { productId: id },
        });
      }

      // 2. Delete all ticket holds related to this product
      await tx.ticketHold.deleteMany({
        where: {
          tickets: {
            some: {
              productId: id,
            },
          },
        },
      });

      // 3. Delete all event capacity holds related to this product
      await tx.eventCapacityHold.deleteMany({
        where: {
          cartItem: {
            productId: id,
          },
        },
      });

      // 4. Delete all volunteer slot holds if related to this product (edge case)
      // This handles cases where volunteer slots might be linked to products
      await tx.volunteerSlotHold.deleteMany({
        where: {
          user: {
            carts: {
              some: {
                items: {
                  some: {
                    productId: id,
                  },
                },
              },
            },
          },
        },
      });

      // 5. Delete all cart items for this product
      await tx.cartItem.deleteMany({
        where: { productId: id },
      });

      // 6. Delete all tickets for this product (both HELD and AVAILABLE)
      await tx.ticket.deleteMany({
        where: { productId: id },
      });

      // 7. Delete all order items for this product (pending orders only)
      // We already checked that there are no completed orders above
      await tx.orderItem.deleteMany({
        where: { productId: id },
      });

      // 8. Finally, delete the product itself
      await tx.product.delete({
        where: { id },
      });
    });

    return NextResponse.json({ 
      success: true,
      message: `Product "${existingProduct.name}" and all related data deleted successfully`,
      deletedItems: {
        redemptionCodes: existingProduct.redemptionCodes.length,
        tickets: existingProduct.tickets.length,
        cartItems: existingProduct.cartItems.length,
        orderItems: existingProduct.orderItems.length,
      }
    });
  } catch (error) {
    console.error("Error deleting product:", error);
    
    // Provide more detailed error information
    if (error && typeof error === "object" && "code" in error) {
      if (error.code === "P2003") {
        return NextResponse.json(
          { 
            error: "Cannot delete product due to existing references. Please ensure all related orders are handled first." 
          },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
