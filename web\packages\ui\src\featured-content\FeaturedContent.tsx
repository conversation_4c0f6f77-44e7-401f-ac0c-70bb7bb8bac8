"use client";

import React from "react";

export interface FeaturedContentProps {
  title?: string;
  items: FeaturedItem[];
  className?: string;
  itemClassName?: string;
  showMoreLink?: {
    text: string;
    href: string;
  };
}

export interface FeaturedItem {
  id: string | number;
  title: string;
  description?: string;
  image?: string;
  link?: string;
  category?: string;
  date?: string;
}

export const FeaturedContent: React.FC<FeaturedContentProps> = ({
  title,
  items,
  className = "",
  itemClassName = "",
  showMoreLink,
}) => {
  return (
    <div className={`${className}`}>
      <div className="flex justify-between items-center mb-3 md:mb-4 lg:mb-6">
        {title && <h2 className="text-2xl font-bold text-white">{title}</h2>}
        {showMoreLink && (
          <a
            href={showMoreLink.href}
            className="text-primary hover:text-primary-light text-sm font-medium"
          >
            {showMoreLink.text}
          </a>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-5 lg:gap-6">
        {items.map((item) => (
          <FeaturedContentItem
            key={item.id}
            item={item}
            className={itemClassName}
          />
        ))}
      </div>
    </div>
  );
};

interface FeaturedContentItemProps {
  item: FeaturedItem;
  className?: string;
}

const FeaturedContentItem: React.FC<FeaturedContentItemProps> = ({
  item,
  className = "",
}) => {
  const content = (
    <div
      className={`bg-secondary-light rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 ${className}`}
    >
      {item.image && (
        <div className="relative h-80 w-full overflow-hidden">
          <img
            src={item.image}
            alt={item.title}
            className="w-full h-full object-cover"
          />
          {item.category && (
            <span className="absolute top-4 right-4 bg-primary text-white text-xs font-bold px-3 py-1 rounded-full">
              {item.category}
            </span>
          )}
        </div>
      )}
      <div className="p-3 sm:p-4 md:p-5">
        <h3 className="text-xl font-bold mb-1 md:mb-2 text-white">
          {item.title}
        </h3>
        {item.date && (
          <div className="text-sm text-gray-400 mb-3">{item.date}</div>
        )}
        {item.description && (
          <p className="text-gray-400 line-clamp-3">{item.description}</p>
        )}
      </div>
    </div>
  );

  if (item.link) {
    return (
      <a href={item.link} className="block">
        {content}
      </a>
    );
  }

  return content;
};
