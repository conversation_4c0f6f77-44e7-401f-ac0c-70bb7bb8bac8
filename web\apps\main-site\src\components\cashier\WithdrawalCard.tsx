import React from "react";
import { Transaction } from "../../services/bankService";

interface WithdrawalCardProps {
  withdrawal: Transaction;
  onSelect: (withdrawal: Transaction) => void;
}

export const WithdrawalCard: React.FC<WithdrawalCardProps> = ({
  withdrawal,
  onSelect,
}) => {
  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return (
      date.toLocaleDateString() +
      " " +
      date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
    );
  };

  return (
    <div
      className="bg-secondary rounded-lg shadow-md p-4 border border-gray-600 hover:border-primary transition-colors cursor-pointer flex flex-col"
      onClick={() => onSelect(withdrawal)}
    >
      <div className="flex items-center mb-3">
        <div className="flex-shrink-0 h-12 w-12 bg-error/20 rounded-full flex items-center justify-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6 text-error"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 10l7-7m0 0l7 7m-7-7v18"
            />
          </svg>
        </div>
        <div className="ml-4">
          <h3 className="text-lg font-medium text-white">Withdrawal Request</h3>
          <p className="text-sm text-gray-400">
            ID: {withdrawal.id.substring(0, 8)}...
          </p>
        </div>
      </div>

      <div className="mb-3">
        <div className="flex justify-between mb-1">
          <span className="text-gray-400">From:</span>
          <span className="text-white font-medium">
            {withdrawal.sender?.displayName || "Unknown"}
          </span>
        </div>
        <div className="flex justify-between mb-1">
          <span className="text-gray-400">Amount:</span>
          <span className="text-error font-bold">
            NS {withdrawal.amount.toFixed(0)}
          </span>
        </div>
        <div className="flex justify-between mb-1">
          <span className="text-gray-400">Date:</span>
          <span className="text-white">{formatDate(withdrawal.createdAt)}</span>
        </div>
      </div>

      {withdrawal.description && (
        <div className="mb-3 bg-secondary-dark p-2 rounded-md">
          <p className="text-sm text-gray-400">{withdrawal.description}</p>
        </div>
      )}

      <div className="mt-auto pt-3 border-t border-gray-600">
        <button
          onClick={(e) => {
            e.stopPropagation();
            onSelect(withdrawal);
          }}
          className="w-full px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
        >
          Review Withdrawal
        </button>
      </div>
    </div>
  );
};
