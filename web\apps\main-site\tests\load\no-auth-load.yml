config:
  target: 'http://192.168.1.87:3000'
  phases:
    # Quick test without authentication requirements
    - duration: 30
      arrivalRate: 30
    - duration: 30
      arrivalRate: 50
    - duration: 30
      arrivalRate: 70
    - duration: 30
      arrivalRate: 90
    - duration: 30
      arrivalRate: 110

scenarios:
  - name: "Public Pages Load Test"
    weight: 50
    flow:
      - get:
          url: "/events"
      - think: 1
      - get:
          url: "/volunteer"
      - think: 1
      - get:
          url: "/help"
      - think: 1
      - get:
          url: "/shop"
      - think: 1
      - get:
          url: "/bank"
      - think: 1
      - get:
          url: "/ships"
      - think: 1
      - get:
          url: "/about"
      - think: 1
      - get:
          url: "/shop/products/3fea768c-6bbe-4162-b029-f4ee9b13cd1b"
      - think: 1


  - name: "Public API Load Test"  
    weight: 30
    flow:
      - get:
          url: "/api/events"
      - think: 1
      - get:
          url: "/api/products"
      - think: 1
      - get:
          url: "/api/event-categories"

  - name: "Static Resources Test"
    weight: 20
    flow:
      - get:
          url: "/images/hero-background.png"
      - get:
          url: "/_next/static/css/app/layout.css"
      - think: 1