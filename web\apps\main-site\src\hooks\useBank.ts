"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import fetchClient from "@/lib/fetchClient";
import {
  getBankUser,
  getAccountSummary,
  getTransactions,
  getRecentTransactions,
  getBankStatistics,
  createTransaction,
  getPendingDeposits,
  getDeposits,
  createDeposit,
  getRecentWithdrawals,
  cancelWithdrawal,
  getRecentTransfers,
  getRecentDonations,
  getCashierNotifications,
  markNotificationsAsRead,
  getPayCodes,
  getActiveCodes,
  getRedeemedCodes,
  createPayCode,
  redeemPayCode,
  validatePayCode,
  updatePayCode,
  updatePayCodeUses,
  getPayCodeDetails,
  cancelPayCode,
  getPendingTransactions,
  getCashierRecentTransactions,
  processTransaction,
  getLedgerEntries,
  createLedgerEntry,
  verifyLedgerEntry,
  searchUsers,
  getMemberDetails,
  Transaction,
  PayCode,
  Ledger,
  UserSearchResult,
  Notification,
} from "../services/bankService";

// Query keys
export const bankQueryKeys = {
  user: "bankUser",
  accountSummary: "accountSummary",
  transactions: "transactions",
  recentTransactions: "recentTransactions",
  statistics: "bankStatistics",
  pendingDeposits: "pendingDeposits",
  deposits: "deposits",
  recentWithdrawals: "recentWithdrawals",
  recentTransfers: "recentTransfers",
  recentDonations: "recentDonations",
  payCodes: "payCodes",
  activeCodes: "activeCodes",
  redeemedCodes: "redeemedCodes",
  payCode: "payCode",
  pendingTransactions: "pendingTransactions",
  cashierRecentTransactions: "cashierRecentTransactions",
  cashierNotifications: "cashierNotifications",
  ledgerEntries: "ledgerEntries",
  userSearch: "userSearch",
};

// Bank user hook
export function useBankUser() {
  return useQuery({
    queryKey: [bankQueryKeys.user],
    queryFn: () => getBankUser(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Account summary hook
export function useAccountSummary() {
  return useQuery({
    queryKey: [bankQueryKeys.accountSummary],
    queryFn: () => getAccountSummary(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Transactions hook
export function useTransactions(filters?: {
  type?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
}) {
  return useQuery({
    queryKey: [bankQueryKeys.transactions, filters],
    queryFn: () => getTransactions(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Recent transactions hook
export function useRecentTransactions(limit = 5) {
  return useQuery({
    queryKey: [bankQueryKeys.recentTransactions, limit],
    queryFn: () => getRecentTransactions(limit),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Bank statistics hook
export function useBankStatistics() {
  return useQuery({
    queryKey: [bankQueryKeys.statistics],
    queryFn: () => getBankStatistics(),
  });
}

// Create transaction hook
export function useCreateTransaction() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {
      type: string;
      amount: number;
      recipient?: string;
      description?: string;
      note?: string;
    }) => createTransaction(data),
    onSuccess: () => {
      // Invalidate relevant queries to refetch data
      queryClient.invalidateQueries({ queryKey: [bankQueryKeys.transactions] });
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.recentTransactions],
      });
      queryClient.invalidateQueries({ queryKey: [bankQueryKeys.user] });
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.accountSummary],
      });

      // Invalidate specific transaction types based on the transaction type
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.pendingDeposits],
      });
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.recentWithdrawals],
      });
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.recentTransfers],
      });
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.recentDonations],
      });
    },
  });
}

// Pending deposits hook
export function usePendingDeposits() {
  return useQuery({
    queryKey: [bankQueryKeys.pendingDeposits],
    queryFn: () => getPendingDeposits(),
  });
}

// Deposits hook
export function useDeposits(limit = 5, includeAll = false) {
  return useQuery({
    queryKey: [bankQueryKeys.deposits, limit, includeAll],
    queryFn: () => getDeposits(limit, includeAll),
  });
}

// Create deposit hook
export function useCreateDeposit() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: FormData) => createDeposit(data),
    onSuccess: () => {
      // Invalidate relevant queries to refetch data
      queryClient.invalidateQueries({ queryKey: [bankQueryKeys.deposits] });
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.pendingDeposits],
      });
      queryClient.invalidateQueries({ queryKey: [bankQueryKeys.transactions] });
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.recentTransactions],
      });
      queryClient.invalidateQueries({ queryKey: [bankQueryKeys.user] });
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.accountSummary],
      });
    },
  });
}

// Recent withdrawals hook
export function useRecentWithdrawals(
  limit = 5,
  includeAll = false,
) {
  return useQuery({
    queryKey: [bankQueryKeys.recentWithdrawals, limit, includeAll],
    queryFn: () => getRecentWithdrawals(limit, includeAll),
  });
}

// Cancel withdrawal hook
export function useCancelWithdrawal() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => cancelWithdrawal(id),
    onSuccess: () => {
      // Invalidate relevant queries to refetch data
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.recentWithdrawals],
      });
      queryClient.invalidateQueries({ queryKey: [bankQueryKeys.transactions] });
      queryClient.invalidateQueries({ queryKey: [bankQueryKeys.user] });
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.accountSummary],
      });
    },
  });
}

// Recent transfers hook
export function useRecentTransfers(limit = 5) {
  return useQuery({
    queryKey: [bankQueryKeys.recentTransfers, limit],
    queryFn: () => getRecentTransfers(limit),
  });
}

// Recent donations hook
export function useRecentDonations(limit = 5) {
  return useQuery({
    queryKey: [bankQueryKeys.recentDonations, limit],
    queryFn: () => getRecentDonations(limit),
  });
}

// All pay codes hook with pagination and filtering
export function usePayCodes(params?: {
  status?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}) {
  return useQuery({
    queryKey: [bankQueryKeys.payCodes, params],
    queryFn: () => getPayCodes(params),
  });
}

// Active pay codes hook
export function useActiveCodes() {
  return useQuery({
    queryKey: [bankQueryKeys.activeCodes],
    queryFn: () => getActiveCodes(),
  });
}

// Redeemed pay codes hook
export function useRedeemedCodes() {
  return useQuery({
    queryKey: [bankQueryKeys.redeemedCodes],
    queryFn: () => getRedeemedCodes(),
  });
}

// Create pay code hook
export function useCreatePayCode() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {
      amount: number;
      expiresAt: string;
      maxUses?: number;
    }) => createPayCode(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [bankQueryKeys.payCodes] });
      queryClient.invalidateQueries({ queryKey: [bankQueryKeys.activeCodes] });
    },
  });
}

// Validate pay code hook
export function useValidatePayCode() {
  return useMutation({
    mutationFn: (code: string) => validatePayCode(code),
  });
}

// Redeem pay code hook
export function useRedeemPayCode() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      code,
      trialRun = false,
    }: {
      code: string;
      trialRun?: boolean;
    }) => redeemPayCode(code, trialRun),
    onSuccess: (data) => {
      // Only invalidate queries if this was not a trial run
      if (!data || !(data as any).trialRun) {
        queryClient.invalidateQueries({
          queryKey: [bankQueryKeys.payCodes],
        });
        queryClient.invalidateQueries({
          queryKey: [bankQueryKeys.activeCodes],
        });
        queryClient.invalidateQueries({
          queryKey: [bankQueryKeys.redeemedCodes],
        });
        queryClient.invalidateQueries({ queryKey: [bankQueryKeys.user] });
        queryClient.invalidateQueries({
          queryKey: [bankQueryKeys.transactions],
        });
        queryClient.invalidateQueries({
          queryKey: [bankQueryKeys.recentTransactions],
        });
      }
    },
  });
}

// Update pay code hook
export function useUpdatePayCode() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: { status: "active" | "paused" | "cancelled" };
    }) => updatePayCode(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: [bankQueryKeys.payCodes] });
      queryClient.invalidateQueries({ queryKey: [bankQueryKeys.activeCodes] });
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.redeemedCodes],
      });
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.payCode, id],
      });
    },
  });
}

// Update pay code uses hook
export function useUpdatePayCodeUses() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, maxUses }: { id: string; maxUses: number }) =>
      updatePayCodeUses(id, maxUses),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: [bankQueryKeys.payCodes] });
      queryClient.invalidateQueries({ queryKey: [bankQueryKeys.activeCodes] });
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.payCode, data.id],
      });
    },
  });
}

// Cancel pay code hook
export function useCancelPayCode() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => cancelPayCode(id),
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: [bankQueryKeys.payCodes] });
      queryClient.invalidateQueries({ queryKey: [bankQueryKeys.activeCodes] });
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.redeemedCodes],
      });
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.payCode, result.payCode.id],
      });
      queryClient.invalidateQueries({ queryKey: [bankQueryKeys.user] });
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.transactions],
      });
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.accountSummary],
      });
    },
  });
}

// Delete pay code hook
export function useDeletePayCode() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => fetchClient.delete(`/api/bank/pay-codes/${id}`),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: [bankQueryKeys.payCodes] });
      queryClient.invalidateQueries({ queryKey: [bankQueryKeys.activeCodes] });
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.redeemedCodes],
      });
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.payCode, id],
      });
    },
  });
}

// Get pay code details hook
export function usePayCodeDetails(id: string) {
  return useQuery({
    queryKey: [bankQueryKeys.payCode, id],
    queryFn: () => getPayCodeDetails(id),
    enabled: !!id,
  });
}

// Pending transactions hook (for cashier)
export function usePendingTransactions(type?: string) {
  return useQuery({
    queryKey: [bankQueryKeys.pendingTransactions, type],
    queryFn: () => getPendingTransactions(type),
  });
}

// Cashier recent transactions hook (for all users)
export function useCashierRecentTransactions(
  limit = 5,
  status?: string,
  type?: string,
) {
  return useQuery({
    queryKey: [bankQueryKeys.cashierRecentTransactions, limit, status, type],
    queryFn: () => getCashierRecentTransactions(limit, status, type),
  });
}

// Process transaction hook (for cashier)
export function useProcessTransaction() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: { status: "approved" | "rejected"; note?: string };
    }) => processTransaction(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.pendingTransactions],
      });
      queryClient.invalidateQueries({ queryKey: [bankQueryKeys.transactions] });
      queryClient.invalidateQueries({ queryKey: [bankQueryKeys.statistics] });
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.cashierNotifications],
      });
    },
  });
}

// Cashier notifications hook
export function useCashierNotifications(
  limit = 20,
  unreadOnly = false,
) {
  return useQuery({
    queryKey: [bankQueryKeys.cashierNotifications, limit, unreadOnly],
    queryFn: () => getCashierNotifications(limit, unreadOnly),
  });
}

// Mark notifications as read hook
export function useMarkNotificationsAsRead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { ids?: string[]; all?: boolean }) =>
      markNotificationsAsRead(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.cashierNotifications],
      });
    },
  });
}

// Ledger entries hook
export function useLedgerEntries(filters?: {
  startDate?: string;
  endDate?: string;
}) {
  return useQuery({
    queryKey: [bankQueryKeys.ledgerEntries, filters],
    queryFn: () => getLedgerEntries(filters),
  });
}

// Create ledger entry hook
export function useCreateLedgerEntry() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {
      description: string;
      totalDeposits: number;
      totalWithdrawals: number;
      totalTransfers: number;
      netChange: number;
    }) => createLedgerEntry(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.ledgerEntries],
      });
    },
  });
}

// Verify ledger entry hook
export function useVerifyLedgerEntry() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => verifyLedgerEntry(id),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [bankQueryKeys.ledgerEntries],
      });
    },
  });
}

// User search hook
export function useSearchUsers(query: string, limit = 10) {
  return useQuery({
    queryKey: [bankQueryKeys.userSearch, query, limit],
    queryFn: () => searchUsers(query, limit),
    enabled: query.length >= 3, // Only run the query if the search term is at least 3 characters
    staleTime: 1000 * 60 * 5, // Cache results for 5 minutes
  });
}

// Member details hook (for cashier)
export function useMemberDetails(memberId: string) {
  return useQuery({
    queryKey: ["memberDetails", memberId],
    queryFn: () => getMemberDetails(memberId),
    enabled: !!memberId, // Only run the query if memberId is provided
  });
}
