"use client";

import React, { useState, useRef } from "react";
import fetchClient from "@/lib/fetchClient";
import { resizeImageForPreview } from "@/lib/clientImageProcessing";

interface NewsImageUploaderProps {
  imageUrl: string;
  onChange: (imageUrl: string) => void;
  error?: boolean;
  errorMessage?: string;
  disabled?: boolean;
  articleId?: string; // For associating image with specific article
}

interface UploadResponse {
  success: boolean;
  file?: { url: string };
  url?: string;
  error?: string;
}

/**
 * News Image Uploader using the new Universal Upload System
 * Drop-in replacement for FeaturedImageUploader with same interface
 */
export const NewsImageUploader: React.FC<NewsImageUploaderProps> = ({
  imageUrl,
  onChange,
  error = false,
  errorMessage = "Featured image is required",
  disabled = false,
  articleId,
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled) return;

    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
    if (!allowedTypes.includes(file.type)) {
      setUploadError(
        "File type not supported. Please upload an image (JPEG, PNG, GIF, WEBP)",
      );
      return;
    }

    // Validate file size (10MB max)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
      setUploadError("File size exceeds 10MB limit");
      return;
    }

    // Generate preview immediately
    try {
      // Resize the image for preview (max width 400px, max height 300px)
      const resizedImageUrl = await resizeImageForPreview(file, 400, 300);
      setPreviewUrl(resizedImageUrl);
    } catch (error) {
      console.error("Error resizing image:", error);
      // Fallback to original method if resizing fails
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }

    setIsUploading(true);
    setUploadError(null);

    try {
      // Create form data for the universal upload API v2
      const formData = new FormData();
      formData.append("file", file);
      formData.append("type", "news");
      if (articleId) {
        formData.append("entityId", articleId);
      }
      formData.append(
        "options",
        JSON.stringify({
          maxSize: 10 * 1024 * 1024, // 10MB
          allowedTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
          width: 1200,
          height: 675, // 16:9 aspect ratio
          quality: 0.9,
          processImage: true,
        }),
      );

      // Upload the file using the v2 endpoint
      const data = (await fetchClient.request("/api/uploads/v2", {
        method: "POST",
        body: formData,
      })) as UploadResponse;

      // Handle response - new format has response.file.url, legacy has response.url
      if (data.success && data.file?.url) {
        onChange(data.file.url);
        // Clear preview since we now have the final URL
        if (previewUrl && previewUrl.startsWith("blob:")) {
          URL.revokeObjectURL(previewUrl);
          setPreviewUrl(null);
        }
      } else if (data.success && data.url) {
        // Legacy format support
        onChange(data.url);
        if (previewUrl && previewUrl.startsWith("blob:")) {
          URL.revokeObjectURL(previewUrl);
          setPreviewUrl(null);
        }
      } else {
        // If we can't find a valid URL, throw an error
        throw new Error(
          data.error || "Invalid response format: URL not found in response",
        );
      }
    } catch (error) {
      console.error("Error uploading image:", error);
      setUploadError(
        error instanceof Error ? error.message : "Failed to upload image",
      );
      // Clear preview on error
      if (previewUrl && previewUrl.startsWith("blob:")) {
        URL.revokeObjectURL(previewUrl);
        setPreviewUrl(null);
      }
    } finally {
      setIsUploading(false);
    }
  };

  const handleBrowseClick = () => {
    if (disabled) return;

    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleRemoveImage = () => {
    if (disabled || isUploading) return;

    // Clear the preview URL
    if (previewUrl) {
      if (previewUrl.startsWith("blob:")) {
        URL.revokeObjectURL(previewUrl);
      }
      setPreviewUrl(null);
    }

    // Clear the image URL
    onChange("");
    setUploadError(null);
  };

  // Use preview URL if available, otherwise use the imageUrl
  const displayImageUrl = imageUrl || previewUrl;

  return (
    <div
      className={`border ${
        error ? "border-accent" : "border-gray-600"
      } border-dashed rounded-md p-4`}
    >
      {displayImageUrl ? (
        <div className="relative">
          <div className="mb-2">
            <p className="text-sm text-gray-400 mb-1">
              Preview (how it will appear in news articles):
            </p>
            <div
              className="border border-gray-600 rounded-md overflow-hidden"
              style={{
                height: "15.6rem", // Match the NewsArticleCard dimensions
                maxHeight: "15.6rem",
              }}
            >
              <img
                src={displayImageUrl}
                alt="Featured Image"
                className="w-full h-full object-cover"
              />
              {isUploading && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                  <div className="flex flex-col items-center">
                    <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mb-3"></div>
                    <p className="text-sm text-white">Uploading image...</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="mt-3 flex justify-end">
            <button
              type="button"
              onClick={handleRemoveImage}
              className={`px-3 py-1 text-sm ${
                disabled || isUploading
                  ? "bg-gray-600 cursor-not-allowed"
                  : "bg-accent hover:bg-accent/80"
              } text-white rounded-md`}
              disabled={disabled || isUploading}
            >
              Remove
            </button>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center pt-5 pb-6">
          {isUploading ? (
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mb-3"></div>
              <p className="text-sm text-gray-400">Uploading image...</p>
            </div>
          ) : (
            <>
              <svg
                className="w-10 h-10 text-gray-400 mb-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                />
              </svg>
              <button
                type="button"
                onClick={handleBrowseClick}
                className={`px-4 py-2 ${
                  disabled
                    ? "bg-gray-600 cursor-not-allowed"
                    : "bg-secondary hover:bg-secondary-dark"
                } text-white rounded-md`}
                disabled={disabled}
              >
                Browse Files
              </button>
              <p className="text-xs text-gray-400 mt-2">
                JPEG, PNG, GIF or WEBP (MAX. 10MB)
              </p>
            </>
          )}
          <input
            ref={fileInputRef}
            type="file"
            className="hidden"
            accept="image/jpeg,image/png,image/gif,image/webp"
            onChange={handleFileChange}
          />
        </div>
      )}

      {(uploadError || (error && errorMessage)) && (
        <p className="mt-1 text-sm text-accent text-center">
          {uploadError || errorMessage}
        </p>
      )}
    </div>
  );
};
