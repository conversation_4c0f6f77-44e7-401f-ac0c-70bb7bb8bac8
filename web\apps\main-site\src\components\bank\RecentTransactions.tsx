"use client";

import React from "react";
import Link from "next/link";
import { Transaction } from "../../services/bankService";
import { TransactionItem } from "./TransactionItem";
import { useRecentTransactions } from "../../hooks/useBank";

interface RecentTransactionsProps {
  transactions?: Transaction[];
  title?: string;
  viewAllLink?: string;
  emptyMessage?: string;
  limit?: number;
  useApi?: boolean;
  isLoading?: boolean;
}

export const RecentTransactions: React.FC<RecentTransactionsProps> = ({
  transactions: propTransactions,
  title = "Recent Transactions",
  viewAllLink,
  emptyMessage = "No transactions to display.",
  limit = 5,
  useApi = false,
  isLoading: propIsLoading,
}) => {
  // Use API data if useApi is true, otherwise use prop data
  const {
    data: apiTransactions,
    isLoading: apiIsLoading,
    error,
  } = useRecentTransactions(limit);

  // Determine which loading state to use
  const isLoading = useApi ? apiIsLoading : propIsLoading;

  // Determine which transactions to use
  const transactions = useApi ? apiTransactions : propTransactions;

  // Loading state
  if (useApi && isLoading) {
    return (
      <div className="bg-secondary-light rounded-lg shadow-md p-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">{title}</h2>
          {viewAllLink && (
            <Link
              href={viewAllLink}
              className="text-primary hover:text-primary-light text-sm font-medium"
            >
              View All
            </Link>
          )}
        </div>
        <div className="space-y-4">
          <div className="animate-pulse h-16 bg-gray-600 rounded w-full"></div>
          <div className="animate-pulse h-16 bg-gray-600 rounded w-full"></div>
          <div className="animate-pulse h-16 bg-gray-600 rounded w-full"></div>
        </div>
      </div>
    );
  }

  // Error state
  if (useApi && error) {
    return (
      <div className="bg-secondary-light rounded-lg shadow-md p-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">{title}</h2>
          {viewAllLink && (
            <Link
              href={viewAllLink}
              className="text-primary hover:text-primary-light text-sm font-medium"
            >
              View All
            </Link>
          )}
        </div>
        <div className="text-error text-center py-6">
          Failed to load transactions. Please try again later.
        </div>
      </div>
    );
  }

  return (
    <div className="bg-secondary-light rounded-lg shadow-md p-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">{title}</h2>
        {viewAllLink && (
          <Link
            href={viewAllLink}
            className="text-primary hover:text-primary-light text-sm font-medium"
          >
            View All
          </Link>
        )}
      </div>

      {transactions && transactions.length > 0 ? (
        <div className="divide-y divide-gray-600">
          {transactions.map((transaction) => (
            <TransactionItem key={transaction.id} transaction={transaction} />
          ))}
        </div>
      ) : (
        <div className="text-center py-6 text-text-secondary">
          {emptyMessage}
          {useApi && (
            <div className="mt-2 text-sm">
              <p>No transactions have been recorded yet.</p>
              <p>
                Your transaction history will appear here once you make your
                first transaction.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
