# Fix Verification and PR Guide

**Purpose:** This guide shows you how to verify that automated fixes are working correctly, remove bad fixes, and create proper pull requests for development and main branches.

---

## Step 1: Checking if Fixes Are Actually Good

### After the FixMeNow Agent Completes
When the agent finishes implementing fixes, it will create a new git branch (format: `prob-YYYY-MM-DD`). Here's how to verify the fixes:

### 1.1 Switch to the Fix Branch
```bash
# Check what branch the agent created
git branch

# Switch to the fix branch (replace date with actual branch name)
git checkout prob-2025-08-09
```

### 1.2 Test the Fixes Manually
1. **Start the development server**
   ```bash
   pnpm dev
   ```

2. **Navigate to the URL** that was reported in your problem template
3. **Follow the test steps** you wrote in the "Success Test" section of your template
4. **Check each fix** listed in the Required/High Priority/Optional sections

### 1.3 Document Your Testing Results
Create a simple checklist for each fix:

**Required Fixes:**
- [ ] Fix 1: [Description] - ✅ Working / ❌ Still broken / ⚠️ Partially working
- [ ] Fix 2: [Description] - ✅ Working / ❌ Still broken / ⚠️ Partially working
- [ ] Fix 3: [Description] - ✅ Working / ❌ Still broken / ⚠️ Partially working

**High Priority Fixes:**
- [ ] Fix 4: [Description] - ✅ Working / ❌ Still broken / ⚠️ Partially working

**Optional Fixes:**
- [ ] Fix 7: [Description] - ✅ Working / ❌ Still broken / ⚠️ Partially working

---

## Step 2: Removing Bad Fixes

If some fixes aren't working correctly, you can remove specific commits from the branch.

### 2.1 See What the Agent Changed
```bash
# View all commits the agent made
git log --oneline

# See changes in a specific commit (replace COMMIT_HASH)
git show COMMIT_HASH
```

### 2.2 Remove a Specific Bad Fix
**Option A: Revert a specific commit (safest)**
```bash
# Revert a specific commit (this creates a new commit that undoes the changes)
git revert COMMIT_HASH_OF_BAD_FIX
```

**Option B: Remove the commit entirely (more advanced)**
```bash
# Interactive rebase to remove specific commits
git rebase -i HEAD~5

# In the editor that opens:
# - Change "pick" to "drop" for commits you want to remove
# - Save and exit
```

### 2.3 Test Again After Removal
1. Restart your development server: `pnpm dev`
2. Test the page again to make sure removing the bad fix didn't break anything else
3. Update your testing checklist

---

## Step 3: Creating a Pull Request to Dev Branch

Once you've verified the fixes are good (and removed any bad ones), create a PR to merge into the development branch.

### 3.1 Check Current Branch Status
```bash
# Make sure you're on the fix branch
git branch

# See what files were changed
git status
```

### 3.2 Create Pull Request to Dev Branch
```bash
# Push your fix branch to remote
git push -u origin prob-2025-08-09

# Create PR using GitHub CLI
gh pr create --base dev --title "Fix: [Brief description of problems solved]" --body "$(cat <<'EOF'
## Problems Fixed
- Fixed [problem 1 from your template]
- Fixed [problem 2 from your template] 
- Fixed [problem 3 from your template]

## Testing Done
- [ ] Manually tested all fixes on development server
- [ ] Verified fixes work as expected
- [ ] Removed any non-working fixes

## Original Problem Report
From: [your problem template filename]
URL: [the URL that had problems]

## Test Steps
[Copy the test steps from your original problem template]

🤖 Generated with [Claude Code](https://claude.ai/code)
EOF
)"
```

### 3.3 Review Your PR
1. **Go to the PR URL** that the command returns
2. **Review the files changed** - make sure only the intended files were modified
3. **Request review** from your development team
4. **Wait for approval** before merging

---

## Step 4: Merging to Development Branch

### 4.1 After PR Approval
```bash
# Make sure your PR is approved, then merge it
gh pr merge --merge

# Or if you prefer to merge via GitHub web interface:
# 1. Go to your PR page
# 2. Click "Merge pull request"
# 3. Choose "Create a merge commit"
# 4. Click "Confirm merge"
```

### 4.2 Clean Up
```bash
# Switch back to main branch
git checkout main

# Delete the fix branch (it's now merged)
git branch -d prob-2025-08-09

# Clean up remote branch
git push origin --delete prob-2025-08-09
```

---

## Step 5: Creating PR from Dev to Main

After your fixes have been tested on the dev branch and you're confident they're stable, create a PR to main.

### 5.1 Switch to Dev Branch
```bash
# Make sure dev branch is up to date
git checkout dev
git pull origin dev
```

### 5.2 Create PR from Dev to Main
```bash
# Create PR from dev to main
gh pr create --base main --head dev --title "Deploy: [Brief description of fixes]" --body "$(cat <<'EOF'
## Summary
Deploying verified fixes from development branch to production.

## Fixes Included
- Fixed [problem 1]
- Fixed [problem 2]
- Fixed [problem 3]

## Testing Complete
- [x] Fixes verified on development branch
- [x] Manual testing completed
- [x] No breaking changes detected

## Deployment Notes
These fixes address user-reported issues from the Features audit system.

🤖 Generated with [Claude Code](https://claude.ai/code)
EOF
)"
```

### 5.3 Production Deployment Checklist
Before merging to main:
- [ ] All fixes tested and working on dev branch
- [ ] No breaking changes introduced
- [ ] Team approval received
- [ ] Ready for production deployment

---

## Step 6: Emergency Rollback (If Needed)

If something goes wrong after deployment, here's how to quickly rollback:

### 6.1 Rollback on Dev Branch
```bash
# Switch to dev branch
git checkout dev

# Revert the merge commit (find the merge commit hash first)
git log --oneline --grep="Merge pull request"

# Revert the merge (replace MERGE_COMMIT_HASH)
git revert -m 1 MERGE_COMMIT_HASH

# Push the rollback
git push origin dev
```

### 6.2 Rollback on Main Branch
```bash
# Switch to main branch  
git checkout main

# Same process as dev branch
git revert -m 1 MERGE_COMMIT_HASH
git push origin main
```

---

## Quick Reference Commands

### Essential Git Commands
```bash
# See current branch and status
git branch
git status

# Switch branches
git checkout branch-name

# Create and push new branch
git checkout -b new-branch-name
git push -u origin new-branch-name

# View recent commits
git log --oneline -10

# Revert a commit
git revert COMMIT_HASH
```

### Essential GitHub CLI Commands
```bash
# Create PR to dev
gh pr create --base dev --title "Fix: description"

# Create PR to main
gh pr create --base main --head dev --title "Deploy: description"

# Merge PR
gh pr merge --merge

# View PR status
gh pr status
```

### Development Server
```bash
# Start development server
pnpm dev

# Stop development server
Ctrl+C (or Cmd+C on Mac)
```

---

## Troubleshooting

### Problem: "git command not found"
**Solution:** Install Git for your operating system

### Problem: "gh command not found" 
**Solution:** Install GitHub CLI: https://cli.github.com/

### Problem: "pnpm command not found"
**Solution:** Install pnpm: `npm install -g pnpm`

### Problem: PR creation fails
**Solution:** 
1. Make sure you're authenticated: `gh auth login`
2. Make sure your branch is pushed: `git push -u origin branch-name`
3. Check you're in the correct repository directory

### Problem: Development server won't start
**Solution:**
1. Make sure you're in the web directory: `cd web`
2. Install dependencies: `pnpm install`  
3. Try starting again: `pnpm dev`

---

**Remember:** Always test your fixes locally before creating PRs, and never merge directly to main without going through the dev branch first!