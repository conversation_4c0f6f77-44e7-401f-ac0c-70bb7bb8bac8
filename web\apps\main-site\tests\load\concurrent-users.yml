config:
  target: 'http://192.168.1.87:3000'
  phases:
    # Gradual ramp up
    - duration: 30
      arrivalRate: 10
    # Peak load - simulating your target of 30-40 concurrent users  
    - duration: 120
      arrivalRate: 35
    # Burst test - simulating peak of 100 users
    - duration: 60
      arrivalRate: 100
    # Cool down
    - duration: 30
      arrivalRate: 5

scenarios:
  - name: "Realistic User Journey"
    weight: 40
    flow:
      # Landing and browsing
      - get:
          url: "/"
      - think: 1
      - get:
          url: "/news"
      - think: 1
      - get:
          url: "/events"
      - think: 1
      - get:
          url: "/volunteer"
      - think: 1 
      
      # Login attempt with real test users
      - post:
          url: "/api/auth/login"
          json:
            email: "testuser{{ $randomInt(1, 10) }}@example.com"
            password: "TestPassword123!"
          capture:
            - json: "$.token"
              as: "token"
      - think: 1
      
      # Banking operations
      - get:
          url: "/bank"
          headers:
            Authorization: "Bearer {{ token }}"
      - get:
          url: "/bank/dashboard/pay-code"
          headers:
            Authorization: "Bearer {{ token }}"
      - think: 1

  - name: "Heavy Shopping Load"
    weight: 30
    flow:
      - get:
          url: "/shop"
      - think: 1
      - get:
          url: "/shop/products/d85017a3-d79a-4d55-9b94-ff434399df4f"
          headers:
            Authorization: "Bearer {{ token }}"
      - think: 1
      

