/**
 * Utility functions for client-side image processing
 * These functions run in the browser and help with image preview and display
 */

/**
 * Resize an image file for preview display
 * @param file The original image file
 * @param maxWidth Maximum width for the resized image
 * @param maxHeight Maximum height for the resized image
 * @returns Promise resolving to a data URL of the resized image
 */
export async function resizeImageForPreview(
  file: File,
  maxWidth = 400,
  maxHeight = 300,
): Promise<string> {
  return new Promise((resolve, reject) => {
    // Create a FileReader to read the file
    const reader = new FileReader();

    // Set up the FileReader onload event
    reader.onload = (event) => {
      if (!event.target?.result) {
        reject(new Error("Failed to read file"));
        return;
      }

      // Create an image element to load the file data
      const img = new Image();

      // Set up the image onload event
      img.onload = () => {
        // Calculate new dimensions while maintaining aspect ratio
        let width = img.width;
        let height = img.height;

        // Scale down if the image exceeds maximum dimensions
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width = Math.floor(width * ratio);
          height = Math.floor(height * ratio);
        }

        // Create a canvas element for drawing the resized image
        const canvas = document.createElement("canvas");
        canvas.width = width;
        canvas.height = height;

        // Get the canvas context and draw the resized image
        const ctx = canvas.getContext("2d");
        if (!ctx) {
          reject(new Error("Failed to get canvas context"));
          return;
        }

        // Draw the image on the canvas
        ctx.drawImage(img, 0, 0, width, height);

        // Convert the canvas to a data URL and resolve the promise
        const dataUrl = canvas.toDataURL(file.type);
        resolve(dataUrl);
      };

      // Set up error handling for the image
      img.onerror = () => {
        reject(new Error("Failed to load image"));
      };

      // Set the image source to the file data
      img.src = event.target.result as string;
    };

    // Set up error handling for the FileReader
    reader.onerror = () => {
      reject(new Error("Failed to read file"));
    };

    // Read the file as a data URL
    reader.readAsDataURL(file);
  });
}
