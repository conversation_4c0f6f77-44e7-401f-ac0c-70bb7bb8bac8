"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useSSE } from "@/hooks/useSSE";
import { <PERSON><PERSON>, <PERSON>, Spinner } from "@bank-of-styx/ui";

interface PerformanceMetric {
  name: string;
  developmentTime?: number;
  productionTime?: number;
  improvement?: number;
}

interface ApiTestResult {
  endpoint: string;
  responseTime: number;
  status: number;
  success: boolean;
}

const PerformanceTestPage = () => {
  const { isAuthenticated } = useAuth();
  const [pageLoadTime, setPageLoadTime] = useState<number | null>(null);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [apiResults, setApiResults] = useState<ApiTestResult[]>([]);
  const [sseConnectionTime, setSseConnectionTime] = useState<number | null>(
    null,
  );
  const [sseMessageTime, setSseMessageTime] = useState<number | null>(null);
  const [environment, setEnvironment] = useState<string>("Unknown");

  // SSE connection for testing
  const {
    isConnected: sseConnected,
    connect: connectSSE,
    disconnect: disconnectSSE,
  } = useSSE({
    onConnect: () => {
      const endTime = performance.now();
      setSseConnectionTime(endTime - sseStartTime);
    },
    onMessage: (data) => {
      if (data.type === "test_message") {
        const endTime = performance.now();
        setSseMessageTime(endTime - data.sentTime);
      }
    },
    enabled: false,
  });

  const [sseStartTime, setSseStartTime] = useState<number>(0);

  // Determine environment
  useEffect(() => {
    setEnvironment(
      process.env.NODE_ENV === "production" ? "Production" : "Development",
    );
  }, []);

  // Measure page load time
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Use the Navigation Timing API to get accurate page load time
      const pageLoadTime =
        window.performance.timing.domContentLoadedEventEnd -
        window.performance.timing.navigationStart;
      setPageLoadTime(pageLoadTime);
    }
  }, []);

  // Test API endpoints
  const testApiEndpoint = async (endpoint: string): Promise<ApiTestResult> => {
    try {
      const startTime = performance.now();
      const response = await fetch(endpoint);
      const endTime = performance.now();
      const responseTime = endTime - startTime;

      return {
        endpoint,
        responseTime,
        status: response.status,
        success: response.ok,
      };
    } catch (error) {
      return {
        endpoint,
        responseTime: 0,
        status: 0,
        success: false,
      };
    }
  };

  // Run all API tests
  const runApiTests = async () => {
    setIsRunningTests(true);
    const endpoints = [
      "/api/news/public",
      "/api/bank/statistics",
      "/api/users/profile",
    ];

    const results = [];
    for (const endpoint of endpoints) {
      const result = await testApiEndpoint(endpoint);
      results.push(result);
    }

    setApiResults(results);
    setIsRunningTests(false);
  };

  // Test SSE connection
  const testSseConnection = () => {
    disconnectSSE();
    setSseStartTime(performance.now());
    connectSSE();
  };

  // Test SSE message delivery
  const testSseMessageDelivery = async () => {
    if (!sseConnected) {
      alert("SSE connection is not established. Please connect first.");
      return;
    }

    try {
      const timestamp = performance.now();
      const response = await fetch("/api/test/sse-performance", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ timestamp }),
      });

      if (!response.ok) {
        throw new Error("Failed to send test message");
      }

      const data = await response.json();
      console.log("Test message sent:", data);
    } catch (error) {
      console.error("Error sending test message:", error);
      alert("Failed to send test message");
    }
  };

  // Format time in ms
  const formatTime = (time: number | null | undefined): string => {
    if (time === null || time === undefined) return "N/A";
    return `${time.toFixed(2)} ms`;
  };

  // Calculate improvement percentage
  const calculateImprovement = (
    dev: number | null | undefined,
    prod: number | null | undefined,
  ): string => {
    if (!dev || !prod) return "N/A";
    const improvement = ((dev - prod) / dev) * 100;
    return improvement > 0
      ? `${improvement.toFixed(2)}% faster`
      : `${Math.abs(improvement).toFixed(2)}% slower`;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Performance Testing</h1>
      <p className="mb-4">
        Current Environment: <span className="font-bold">{environment}</span>
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card title="Page Load Performance" headerAction={null} className="p-4">
          <p className="mb-2">
            Page Load Time:{" "}
            <span className="font-mono">{formatTime(pageLoadTime)}</span>
          </p>
          <p className="text-sm text-gray-500 mt-2">
            Note: Page load times in development mode are significantly slower
            than production due to development server overhead.
          </p>
        </Card>

        <Card title="SSE Performance" headerAction={null} className="p-4">
          <p className="mb-2">
            Connection Time:{" "}
            <span className="font-mono">{formatTime(sseConnectionTime)}</span>
          </p>
          <p className="mb-2">
            Message Delivery Time:{" "}
            <span className="font-mono">{formatTime(sseMessageTime)}</span>
          </p>
          <div className="mt-4">
            <Button
              onClick={testSseConnection}
              disabled={!isAuthenticated}
              className="mr-2"
            >
              Test SSE Connection
            </Button>
            <Button
              onClick={testSseMessageDelivery}
              disabled={!isAuthenticated || !sseConnected}
              className="mr-2"
            >
              Test Message Delivery
            </Button>
            {!isAuthenticated && (
              <p className="text-sm text-red-500 mt-2">
                You must be logged in to test SSE connections
              </p>
            )}
          </div>
        </Card>
      </div>

      <Card title="API Response Times" headerAction={null} className="p-4 mb-8">
        <Button
          onClick={runApiTests}
          disabled={isRunningTests}
          className="mb-4"
        >
          {isRunningTests ? (
            <>
              <Spinner size="sm" className="mr-2" /> Running Tests...
            </>
          ) : (
            "Run API Tests"
          )}
        </Button>

        {apiResults.length > 0 && (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-transparent">
              <thead>
                <tr>
                  <th className="px-4 py-2 text-left">Endpoint</th>
                  <th className="px-4 py-2 text-left">Response Time</th>
                  <th className="px-4 py-2 text-left">Status</th>
                  <th className="px-4 py-2 text-left">Success</th>
                </tr>
              </thead>
              <tbody>
                {apiResults.map((result, index) => (
                  <tr key={index}>
                    <td className="px-4 py-2 font-mono text-sm">
                      {result.endpoint}
                    </td>
                    <td className="px-4 py-2 font-mono">
                      {formatTime(result.responseTime)}
                    </td>
                    <td className="px-4 py-2">{result.status}</td>
                    <td className="px-4 py-2">
                      {result.success ? (
                        <span className="text-green-500">✓</span>
                      ) : (
                        <span className="text-red-500">✗</span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </Card>

      <Card title="Performance Comparison" headerAction={null} className="p-4">
        <p className="mb-4">
          Use this section to manually record and compare metrics between
          development and production environments.
        </p>

        <div className="overflow-x-auto">
          <table className="min-w-full bg-transparent">
            <thead>
              <tr>
                <th className="px-4 py-2 text-left">Metric</th>
                <th className="px-4 py-2 text-left">Development</th>
                <th className="px-4 py-2 text-left">Production</th>
                <th className="px-4 py-2 text-left">Improvement</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="px-4 py-2">Page Load Time</td>
                <td className="px-4 py-2">
                  <input
                    type="number"
                    className="w-24 px-2 py-1 border rounded"
                    placeholder="ms"
                  />
                </td>
                <td className="px-4 py-2">
                  <input
                    type="number"
                    className="w-24 px-2 py-1 border rounded"
                    placeholder="ms"
                  />
                </td>
                <td className="px-4 py-2">Calculate</td>
              </tr>
              <tr>
                <td className="px-4 py-2">SSE Connection Time</td>
                <td className="px-4 py-2">
                  <input
                    type="number"
                    className="w-24 px-2 py-1 border rounded"
                    placeholder="ms"
                  />
                </td>
                <td className="px-4 py-2">
                  <input
                    type="number"
                    className="w-24 px-2 py-1 border rounded"
                    placeholder="ms"
                  />
                </td>
                <td className="px-4 py-2">Calculate</td>
              </tr>
              <tr>
                <td className="px-4 py-2">API Response Time (avg)</td>
                <td className="px-4 py-2">
                  <input
                    type="number"
                    className="w-24 px-2 py-1 border rounded"
                    placeholder="ms"
                  />
                </td>
                <td className="px-4 py-2">
                  <input
                    type="number"
                    className="w-24 px-2 py-1 border rounded"
                    placeholder="ms"
                  />
                </td>
                <td className="px-4 py-2">Calculate</td>
              </tr>
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default PerformanceTestPage;
