import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import jwt from "jsonwebtoken";

const JWT_SECRET = process.env.JWT_SECRET || "your-development-secret-key";

// Helper function to verify token
function verifyToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

// Handle OPTIONS request for CORS preflight
export async function OPTIONS(request) {
  const response = new NextResponse(null, { status: 204 });

  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set(
    "Access-Control-Allow-Methods",
    "GET, PATCH, OPTIONS"
  );
  response.headers.set(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization"
  );

  return response;
}

// GET /api/user/state - Get user state
export async function GET(request) {
  try {
    // Get authorization header
    const authHeader = request.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json(
        { error: "Invalid token" },
        { status: 401 }
      );
    }

    const userId = decoded.id;

    // Get user state or create if it doesn't exist
    let userState = await prisma.userState.findUnique({
      where: { userId },
    });

    // If user state doesn't exist, create it
    if (!userState) {
      userState = await prisma.userState.create({
        data: {
          userId,
          lastActive: new Date(),
          deviceInfo: request.headers.get("user-agent") || null,
        },
      });
    }

    // Update last active time
    await prisma.userState.update({
      where: { userId },
      data: { lastActive: new Date() },
    });

    return NextResponse.json(userState);
  } catch (error) {
    console.error("Error getting user state:", error);
    return NextResponse.json(
      { error: "Failed to get user state" },
      { status: 500 }
    );
  }
}

// PATCH /api/user/state - Update user state
export async function PATCH(request) {
  try {
    // Get authorization header
    const authHeader = request.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Extract token
    const token = authHeader.split(" ")[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded || typeof decoded !== "object" || !decoded.id) {
      return NextResponse.json(
        { error: "Invalid token" },
        { status: 401 }
      );
    }

    const userId = decoded.id;
    const data = await request.json();

    // Validate data
    const allowedFields = [
      "lastNewsSync",
      "lastCategorySync",
      "lastNotificationSync",
      "betaFeaturesEnabled",
      "darkModeEnabled",
      "compactViewEnabled",
      "globalCacheVersion",
      "lastActive",
      "deviceInfo",
      "pageViews",
      "articleReads",
      "customState",
      "hasAccessToAuctions",
      "hasAccessToMerchants",
      "hasAccessToChat",
    ];

    // Filter out disallowed fields
    const filteredData = Object.keys(data).reduce((acc, key) => {
      if (allowedFields.includes(key)) {
        acc[key] = data[key];
      }
      return acc;
    }, {});

    // Update user state
    const userState = await prisma.userState.upsert({
      where: { userId },
      update: filteredData,
      create: {
        userId,
        ...filteredData,
        lastActive: new Date(),
        deviceInfo: request.headers.get("user-agent") || null,
      },
    });

    return NextResponse.json(userState);
  } catch (error) {
    console.error("Error updating user state:", error);
    return NextResponse.json(
      { error: "Failed to update user state" },
      { status: 500 }
    );
  }
}
