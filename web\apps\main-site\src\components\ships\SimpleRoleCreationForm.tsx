"use client";

import { useState } from 'react';
import { Button } from '@bank-of-styx/ui';

interface RoleCreationFormProps {
  onCreateRole: (name: string, description?: string) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

export default function RoleCreationForm({
  onCreateRole,
  onCancel,
  loading = false
}: RoleCreationFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [actionLoading, setActionLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) return;

    setActionLoading(true);
    try {
      await onCreateRole(formData.name.trim(), formData.description.trim() || undefined);
      setFormData({ name: '', description: '' });
    } catch (error) {
      console.error('Error creating role:', error);
    } finally {
      setActionLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Role Name *
        </label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          className="w-full bg-input border border-secondary-dark rounded-md px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary"
          placeholder="e.g., First Mate, Navigator, Cook"
          maxLength={50}
          required
          disabled={loading || actionLoading}
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Description (Optional)
        </label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          className="w-full bg-input border border-secondary-dark rounded-md px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary"
          placeholder="Describe the role's responsibilities..."
          rows={3}
          maxLength={200}
          disabled={loading || actionLoading}
        />
        <div className="text-xs text-gray-400 mt-1">
          {formData.description.length}/200 characters
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading || actionLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={loading || actionLoading || !formData.name.trim()}
        >
          {actionLoading ? 'Creating...' : 'Create Role'}
        </Button>
      </div>
    </form>
  );
}