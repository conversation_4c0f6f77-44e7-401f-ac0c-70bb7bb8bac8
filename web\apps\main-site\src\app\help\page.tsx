"use client";

import React, { useState } from "react";
import { Card, Button, Modal, Input } from "@bank-of-styx/ui";
import toast from "react-hot-toast";
import { submitContactForm } from "@/services/ticketService";

export default function HelpPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [name, setName] = useState("");
  const [showSuccess, setShowSuccess] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  const handleOpenModal = () => {
    setIsModalOpen(true);
    setError("");
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setMessage("");
    setEmail("");
    setPhone("");
    setName("");
    setError("");
  };

  const handleSubmit = async () => {
    if (!message.trim()) {
      setError("Please enter a message");
      return;
    }

    if (!email.trim() || !/^\S+@\S+\.\S+$/.test(email)) {
      setError("Please enter a valid email address");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      // Use the ticket service to create a support ticket
      const result = await submitContactForm({
        name,
        email,
        phone,
        message,
      });

      if (!result.success) {
        throw new Error("Failed to create support ticket");
      }

      // Success handling
      setIsModalOpen(false);
      setShowSuccess(true);
      setMessage("");
      setEmail("");
      setPhone("");
      setName("");

      // Show toast notification
      toast.success(
        "Your support ticket has been created! An administrator will contact you at the email you provided.",
      );

      // Hide success message after 5 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 5000);
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "Failed to create support ticket. Please try again.",
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-4 md:py-6 lg:py-8">
      <h1 className="text-3xl font-bold mb-8 text-white">Help & Support</h1>

      {showSuccess && (
        <div className="mb-6 p-4 rounded-lg bg-success bg-opacity-20 text-success border border-success flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
              clipRule="evenodd"
            />
          </svg>
          <span>
            Your support ticket has been successfully created! An administrator
            will respond to your inquiry soon.
          </span>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        <Card title="Bank Administrators" className="mb-6" headerAction={<></>}>
          <div className="space-y-4 text-white">
            <p className="mb-4">
              Our Bank Administrators are available to assist with any
              banking-related issues:
            </p>

            <div className="flex items-center space-x-3 p-3 bg-secondary rounded-lg">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center">
                  <span className="text-white font-bold">SM</span>
                </div>
              </div>
              <div>
                <h3 className="font-bold">@Satchel McDab II</h3>
                <p className="text-gray-400 text-sm">Head Administrator</p>
              </div>
            </div>

            <div className="flex items-center space-x-3 p-3 bg-secondary rounded-lg">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center">
                  <span className="text-white font-bold">PM</span>
                </div>
              </div>
              <div>
                <h3 className="font-bold">@Profit McDab</h3>
                <p className="text-gray-400 text-sm">Bank Administrator</p>
              </div>
            </div>
          </div>
        </Card>

        <Card title="Content Editors" className="mb-6" headerAction={<></>}>
          <div className="space-y-4 text-white">
            <p className="mb-4">
              Our Content Editors manage news articles and featured content
              across the site:
            </p>

            <div className="flex items-center space-x-3 p-3 bg-secondary rounded-lg">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 rounded-full bg-accent flex items-center justify-center">
                  <span className="text-white font-bold">LD</span>
                </div>
              </div>
              <div>
                <h3 className="font-bold">@La Maga Demonio</h3>
                <p className="text-gray-400 text-sm">Lead Editor</p>
              </div>
            </div>

            <div className="flex items-center space-x-3 p-3 bg-secondary rounded-lg">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 rounded-full bg-accent flex items-center justify-center">
                  <span className="text-white font-bold">CP</span>
                </div>
              </div>
              <div>
                <h3 className="font-bold">@Captain Parrot</h3>
                <p className="text-gray-400 text-sm">News Editor</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      <Card
        title="Community Support Resources"
        className="mb-6"
        headerAction={<></>}
      >
        <div className="space-y-4 text-white">
          <p>
            The Bank of Styx is part of the larger Pirate Rinfair community. We
            have several resources available to help you:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div className="bg-secondary p-4 rounded-lg">
              <h3 className="font-bold text-lg mb-2">Discord Channels</h3>
              <p className="text-gray-400">
                Join our Discord server and check the #rules-help and #ꓭ∀ꓤ_ǝ𝗵ꓕ
                channels for assistance.
              </p>
            </div>

            <div className="bg-secondary p-4 rounded-lg">
              <h3 className="font-bold text-lg mb-2">Community Forums</h3>
              <p className="text-gray-400">
                Browse our community forums for guides, FAQs, and discussions
                about the Bank of Styx.
              </p>
            </div>

            <div className="bg-secondary p-4 rounded-lg">
              <h3 className="font-bold text-lg mb-2">Support Tickets</h3>
              <p className="text-gray-400">
                For more complex issues, submit a support ticket using the
                Contact Administrators button.
              </p>
            </div>
          </div>
        </div>
      </Card>

      <Card title="Need Help?" className="mb-6" headerAction={<></>}>
        <div className="space-y-6 text-white">
          <p className="mb-4">
            If you have any questions or require assistance, you can:
          </p>

          <ul className="list-disc pl-6 space-y-2">
            <li>Check the Rules & Help channel for guides.</li>
            <li>Use the button below to submit a ticket for help.</li>
            <li>Ask for help in #ꓭ∀ꓤ_ǝ𝗵ꓕ channel.</li>
          </ul>

          <div className="mt-6">
            <Button variant="primary" onClick={handleOpenModal}>
              Contact Administrators
            </Button>
          </div>
        </div>
      </Card>

      {/* Contact Admin Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        title="Contact Administrators"
        size="md"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleCloseModal}>
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleSubmit}
              disabled={!message.trim() || !email.trim() || isSubmitting}
            >
              {isSubmitting ? "Submitting..." : "Submit"}
            </Button>
          </div>
        }
      >
        <div className="space-y-4">
          <p className="text-white">
            Please provide your contact information and describe your issue in
            detail. Our administrators will respond as soon as possible.
          </p>

          {error && (
            <div className="p-3 rounded-md bg-error bg-opacity-20 text-error border border-error text-sm">
              {error}
            </div>
          )}

          <div className="space-y-4">
            <Input
              label="Name (Optional)"
              id="name"
              value={name}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setName(e.target.value)
              }
              placeholder="Your name"
              fullWidth
            />

            <Input
              label="Email Address"
              id="email"
              type="email"
              value={email}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setEmail(e.target.value)
              }
              placeholder="<EMAIL>"
              fullWidth
              required
            />

            <Input
              label="Phone Number (Optional)"
              id="phone"
              type="tel"
              value={phone}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setPhone(e.target.value)
              }
              placeholder="Your phone number"
              fullWidth
            />

            <div>
              <label
                htmlFor="message"
                className="block text-sm font-medium text-white mb-1"
              >
                Your Message
              </label>
              <textarea
                id="message"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Type your message here..."
                rows={5}
                className="w-full min-h-[120px] resize-y px-4 py-2 rounded-md shadow-sm text-white
                  border-gray-600 focus:border-primary focus:ring-primary
                  focus:outline-none focus:ring-2 focus:ring-offset-0
                  disabled:bg-secondary-dark disabled:text-disabled disabled:cursor-not-allowed
                  bg-[#2C2F33]"
                required
              />
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
}
