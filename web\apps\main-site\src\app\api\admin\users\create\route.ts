import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";
import bcrypt from "bcryptjs";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export async function POST(request: Request) {
  try {
    // Check if user is authenticated and has admin role
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const isAdmin = await userHasRole(request, "admin");
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Admin privileges required" },
        { status: 403 },
      );
    }

    // Get request body
    const { username, displayName, email, password, roles, status } =
      await request.json();

    // Validate required fields
    if (!username || !displayName || !email || !password) {
      return NextResponse.json(
        { error: "Username, display name, email, and password are required" },
        { status: 400 },
      );
    }

    // Validate email format
    if (!email.includes("@")) {
      return NextResponse.json(
        { error: "Valid email is required" },
        { status: 400 },
      );
    }

    // Check if username or email already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [{ username }, { email }],
      },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "Username or email already exists" },
        { status: 409 },
      );
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create user
    const user = await prisma.user.create({
      data: {
        username,
        displayName,
        email,
        passwordHash: hashedPassword,
        isAdmin: roles?.admin || false,
        isEditor: roles?.editor || false,
        isBanker: roles?.banker || false,
        isChatModerator: roles?.chatModerator || false,
        isVolunteerCoordinator: roles?.volunteerCoordinator || false,
        isLeadManager: roles?.leadManager || false,
        isSalesManager: roles?.salesManager || false,
        isLandSteward: roles?.landSteward || false,
        isEmailVerified: status === "active", // Auto-verify if status is active
      },
      select: {
        id: true,
        username: true,
        displayName: true,
        avatar: true,
        email: true,
        isEmailVerified: true,
        isAdmin: true,
        isEditor: true,
        isBanker: true,
        isChatModerator: true,
        isVolunteerCoordinator: true,
        isLeadManager: true,
        isSalesManager: true,
        isLandSteward: true,
        createdAt: true,
      },
    });

    // Transform user to match the AdminUser interface
    const transformedUser = {
      id: user.id,
      username: user.username,
      displayName: user.displayName,
      avatar: user.avatar,
      email: user.email,
      isEmailVerified: user.isEmailVerified,
      status: status || "active", // Use the status from the request
      roles: {
        admin: user.isAdmin,
        editor: user.isEditor,
        banker: user.isBanker,
        chatModerator: user.isChatModerator,
        volunteerCoordinator: user.isVolunteerCoordinator,
        leadManager: user.isLeadManager,
        salesManager: user.isSalesManager,
        landSteward: user.isLandSteward,
      },
      createdAt: user.createdAt.toISOString(),
    };

    return NextResponse.json(transformedUser);
  } catch (error) {
    console.error("Error creating user:", error);
    return NextResponse.json(
      { error: "Failed to create user" },
      { status: 500 },
    );
  }
}
