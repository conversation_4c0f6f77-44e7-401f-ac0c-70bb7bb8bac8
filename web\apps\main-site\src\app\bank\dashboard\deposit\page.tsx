"use client";
import Link from "next/link";

import React, { useState } from "react";
import { DashboardLayout } from "../../../../components/bank";
import { TransactionItem } from "../../../../components/bank";
import {
  useBankUser,
  useDeposits,
  useCreateDeposit,
} from "../../../../hooks/useBank";
import { toast } from "react-hot-toast";
import { openImageInNewWindow } from "../../../../lib/imageDisplayUtils";
import DepositReceiptUploader from "../../../../components/upload/DepositReceiptUploader";
import { UploadResponse } from "../../../../types/upload";

export default function DepositPage() {
  const [amount, setAmount] = useState("");
  const [note, setNote] = useState("");
  const [uploadedReceipt, setUploadedReceipt] = useState<any>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUploadingReceipt, setIsUploadingReceipt] = useState(false);

  // Fetch user data and deposits
  const { data: bankUser, isLoading: isLoadingUser } = useBankUser();
  const { data: deposits = [], isLoading: isLoadingDeposits } = useDeposits(
    20,
    true,
  );
  const createDeposit = useCreateDeposit();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate amount
    if (!amount || parseFloat(amount) <= 0) {
      toast.error("Please enter a valid amount");
      return;
    }

    // Validate receipt upload
    if (!uploadedReceipt) {
      toast.error("Please upload a receipt");
      return;
    }

    setShowConfirmation(true);
  };

  const handleConfirm = async () => {
    if (!uploadedReceipt) return;

    try {
      setIsSubmitting(true);

      // Create form data using the uploaded receipt information
      const formData = new FormData();
      formData.append("amount", amount);

      // If we have the file ID, use it; otherwise use the URL for backwards compatibility
      if (uploadedReceipt.id) {
        formData.append("receiptId", uploadedReceipt.id);
      } else {
        // Fallback for existing API compatibility
        formData.append(
          "receiptUrl",
          uploadedReceipt.url || uploadedReceipt.file?.url,
        );
      }

      if (note) formData.append("note", note);

      // Submit deposit request
      await createDeposit.mutateAsync(formData);

      // Show success message
      toast.success("Deposit request submitted successfully");

      // Reset form
      setAmount("");
      setNote("");
      setUploadedReceipt(null);
      setShowConfirmation(false);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "An error occurred";
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setShowConfirmation(false);
  };

  // Handler for the new DepositReceiptUploader
  const handleReceiptUploadComplete = (response: UploadResponse) => {
    if (response.success) {
      setUploadedReceipt(response.file || response);
      toast.success("Receipt uploaded successfully");
    } else {
      toast.error(response.message || "Upload failed");
    }
  };

  const handleReceiptUploadStart = () => {
    setIsUploadingReceipt(true);
  };

  const handleReceiptProcessed = (data: any) => {
    console.log("Receipt processed:", data);
    // You can use this data to auto-fill form fields if needed
    setIsUploadingReceipt(false);
  };

  return (
    <DashboardLayout>
      <div className="mb-3">
        <Link
          href="/bank/dashboard"
          className="text-primary hover:text-primary-light"
        >
          &larr; Back to Dashboard
        </Link>
      </div>
      <div className="bg-secondary-light rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-2xl font-bold text-white mb-6">Request Deposit</h2>

        <div className="mb-6">
          <p className="text-gray-400">
            Current Balance:{" "}
            <span className="font-bold text-success">
              {isLoadingUser
                ? "Loading..."
                : `NS ${bankUser?.balance.toFixed(0)}`}
            </span>
          </p>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label
                htmlFor="amount"
                className="block text-white font-medium mb-2"
              >
                Amount
              </label>
              <div className="relative flex items-center">
                <span className="absolute left-3 text-gray-400 select-none pointer-events-none">
                  NS
                </span>
                <input
                  type="number"
                  id="amount"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="0"
                  min="1"
                  step="1"
                  required
                />
              </div>
            </div>

            {/* New Universal Receipt Uploader */}
            <div>
              <DepositReceiptUploader
                depositId={`temp-${Date.now()}`} // Temporary ID until deposit is created
                onUploadComplete={handleReceiptUploadComplete}
                onUploadStart={handleReceiptUploadStart}
                onReceiptProcessed={handleReceiptProcessed}
                allowPDF={true}
                className="mb-4"
              />
            </div>

            <div>
              <label
                htmlFor="note"
                className="block text-white font-medium mb-2"
              >
                Note <span className="text-gray-400 text-sm">(Optional)</span>
              </label>
              <input
                type="text"
                id="note"
                value={note}
                onChange={(e) => setNote(e.target.value)}
                className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Additional information about this deposit"
              />
            </div>

            <div className="flex justify-end space-x-4 pt-4">
              <button
                type="button"
                onClick={() => {
                  setAmount("");
                  setNote("");
                  setUploadedReceipt(null);
                }}
                className="px-4 py-2 border border-gray-600 rounded-md text-white hover:bg-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Submitting..." : "Submit Deposit Request"}
              </button>
            </div>
          </div>
        </form>
      </div>

      <div className="bg-secondary-light rounded-lg shadow-md p-6">
        <h2 className="text-lg font-semibold text-white mb-4">Deposits</h2>

        {isLoadingDeposits ? (
          <div className="space-y-4">
            <div className="animate-pulse h-16 bg-gray-600 rounded w-full"></div>
            <div className="animate-pulse h-16 bg-gray-600 rounded w-full"></div>
            <div className="animate-pulse h-16 bg-gray-600 rounded w-full"></div>
          </div>
        ) : deposits.length > 0 ? (
          <div className="divide-y divide-gray-600">
            {deposits.map((deposit) => (
              <div
                key={deposit.id}
                className="py-3 border-b border-gray-600 last:border-0"
              >
                <div className="grid grid-cols-4 md:grid-cols-2 items-center">
                  {/* Left group: Date and Status */}
                  <div className="col-span-2 md:col-span-1 flex items-center space-x-4">
                    <div className="text-sm text-gray-400">
                      {new Date(deposit.createdAt).toLocaleDateString()}
                    </div>

                    <div
                      className={`text-sm ${
                        deposit.status === "pending"
                          ? "text-warning"
                          : deposit.status === "completed"
                          ? "text-success"
                          : "text-error"
                      }`}
                    >
                      {deposit.status.charAt(0).toUpperCase() +
                        deposit.status.slice(1)}
                    </div>
                  </div>

                  {/* Right group: Amount */}
                  <div className="col-span-2 md:col-span-1 flex items-center justify-end">
                    <div className="font-bold text-primary">
                      NS {Math.abs(deposit.amount).toFixed(0)}
                    </div>
                  </div>
                </div>

                {/* Additional details */}
                <div className="mt-2 text-sm text-gray-400">
                  <div>Description: {deposit.description}</div>
                  {deposit.note && <div>Note: {deposit.note}</div>}
                  {deposit.receiptImage && (
                    <div className="mt-1">
                      <a
                        href={deposit.receiptImage || "#"}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-accent hover:underline"
                        onClick={(e) => {
                          // Use our utility function to handle all receipt image formats
                          e.preventDefault();
                          openImageInNewWindow(deposit.receiptImage);
                        }}
                      >
                        View Receipt
                      </a>
                    </div>
                  )}
                  {deposit.processedAt && (
                    <div>
                      Processed:{" "}
                      {new Date(deposit.processedAt).toLocaleString()}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-6 text-gray-400">
            No deposits found.
          </div>
        )}
      </div>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-secondary-light rounded-lg p-6 max-w-md w-full">
            <h3 className="text-xl font-bold text-white mb-4">
              Confirm Deposit Request
            </h3>
            <p className="mb-4 text-white">
              You are about to request a deposit of{" "}
              <span className="font-bold">NS {amount}</span>.
            </p>
            {uploadedReceipt && (
              <div className="mb-4">
                <p className="text-white mb-2">
                  Receipt: {uploadedReceipt.filename || "Receipt uploaded"}
                </p>
                {uploadedReceipt.url &&
                  uploadedReceipt.mimeType?.startsWith("image/") && (
                    <div className="mt-2 border border-gray-600 rounded-md overflow-hidden max-w-xs">
                      <img
                        src={uploadedReceipt.url}
                        alt="Receipt preview"
                        className="w-full h-auto object-contain"
                        style={{ maxHeight: "300px" }}
                        onError={(e) => {
                          console.error("Failed to load image preview");
                        }}
                      />
                    </div>
                  )}
              </div>
            )}
            {note && <p className="mb-4 text-white">Note: {note}</p>}
            <div className="flex justify-end space-x-4">
              <button
                onClick={handleCancel}
                className="px-4 py-2 border border-gray-600 rounded-md text-white hover:bg-secondary"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirm}
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Processing..." : "Confirm"}
              </button>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
}
