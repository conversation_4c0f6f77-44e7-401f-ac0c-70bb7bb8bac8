-- AlterTable
ALTER TABLE `notification` ADD COLUMN `category` VARCHAR(191) NOT NULL DEFAULT 'system',
    ADD COLUMN `title` VARCHAR(255) NOT NULL DEFAULT 'Notification',
    ADD COLUMN `link` VARCHAR(191) NULL,
    ADD COLUMN `icon` VARCHAR(191) NULL,
    ADD COLUMN `priority` VARCHAR(191) NOT NULL DEFAULT 'medium',
    ADD COLUMN `userId` VARCHAR(191) NOT NULL DEFAULT '';

-- AlterTable
ALTER TABLE `user` ADD COLUMN `notifyAuctions` BOOLEAN NOT NULL DEFAULT TRUE,
    ADD COLUMN `notifyChat` BOOLEAN NOT NULL DEFAULT TRUE,
    ADD COLUMN `notifyAdmin` BOOLEAN NOT NULL DEFAULT TRUE;

-- CreateIndex
CREATE INDEX `notification_userId_idx` ON `notification`(`userId`);

-- CreateIndex
CREATE INDEX `notification_category_idx` ON `notification`(`category`);

-- CreateIndex
CREATE INDEX `notification_read_idx` ON `notification`(`read`);

-- AddForeignKey
ALTER TABLE `notification` ADD CONSTRAINT `notification_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- Update existing notifications to have a userId (assign to first admin user)
UPDATE `notification` SET `userId` = (SELECT `id` FROM `user` WHERE `isAdmin` = TRUE LIMIT 1) WHERE `userId` = '';
