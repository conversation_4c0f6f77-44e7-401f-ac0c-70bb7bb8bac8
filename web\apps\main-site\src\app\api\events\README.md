# Events API Routes

Event management system providing calendar functionality and event operations.

## Endpoints

- **route.ts** - Main events operations (list, create, update events)
- **[id]/** - Individual event operations (view, edit, delete specific events)

## Event Features

These endpoints support:

- Event creation and management
- Calendar integration and display
- Event categorization and filtering
- Featured event highlighting
- Event registration and attendance tracking

The event system integrates with the volunteer management system for events requiring volunteer coordination.
