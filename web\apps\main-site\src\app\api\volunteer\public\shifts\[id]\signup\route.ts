import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// POST /api/volunteer/public/shifts/[id]/signup - Sign up for a shift
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id: shiftId } = params;
    const formData = await req.json();

    // Check if user is authenticated
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    // Get the shift to check if it exists, has vacancies, and if the event is published
    const shift = await prisma.volunteerShift.findUnique({
      where: { id: shiftId },
      select: {
        id: true,
        title: true,
        startTime: true,
        endTime: true,
        maxVolunteers: true,
        categoryId: true,
        eventId: true,
        // Include event to check if it's published
        event: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
        // Include assignments to check if user is already signed up and to calculate vacancies
        assignments: {
          select: {
            id: true,
            userId: true,
            status: true,
          },
          where: {
            status: {
              notIn: ["cancelled", "no_show"],
            },
          },
        },
      },
    });

    if (!shift) {
      return NextResponse.json({ error: "Shift not found" }, { status: 404 });
    }

    // Check if event is published
    if (shift.event.status !== "published") {
      return NextResponse.json(
        { error: "Shift not available" },
        { status: 403 },
      );
    }

    // Check if shift has already started
    const now = new Date();
    if (new Date(shift.startTime) < now) {
      return NextResponse.json(
        { error: "Shift has already started" },
        { status: 400 },
      );
    }

    // Check if user is already signed up for this shift
    const existingAssignment = shift.assignments.find(
      (assignment) => assignment.userId === user.id,
    );

    if (existingAssignment) {
      return NextResponse.json(
        { error: "You are already signed up for this shift" },
        { status: 400 },
      );
    }

    // Check if shift has vacancies
    const vacancies = shift.maxVolunteers - shift.assignments.length;
    if (vacancies <= 0) {
      return NextResponse.json({ error: "Shift is full" }, { status: 400 });
    }

    // Create volunteer assignment
    const assignment = await prisma.volunteerAssignment.create({
      data: {
        userId: user.id,
        shiftId: shift.id,
        status: "pending",
        // Store additional form data in metadata
        metadata: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          pirateName: formData.pirateName,
          stayingWith: formData.stayingWith,
          stayingWithShipId: formData.stayingWithShipId,
          isDock: formData.isDock,
          isLandGrant: formData.isLandGrant,
          landGrantCredit: formData.landGrantCredit,
          landGrantCreditShipId: formData.landGrantCreditShipId,
          pronouns: formData.pronouns,
          addToDeedsLottery: formData.addToDeedsLottery,
        },
        // Create notification preferences
        notificationPreferences: {
          create: {
            emailNotification: formData.emailNotification,
            websiteNotification: formData.websiteNotification,
          },
        },
      },
      include: {
        shift: {
          select: {
            id: true,
            title: true,
            startTime: true,
            endTime: true,
            categoryId: true,
            eventId: true,
            category: {
              select: {
                name: true,
                payRate: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({
      assignment,
      message: "Successfully signed up for shift",
    });
  } catch (error) {
    console.error("Error signing up for shift:", error);
    return NextResponse.json(
      { error: "Failed to sign up for shift" },
      { status: 500 },
    );
  }
}
