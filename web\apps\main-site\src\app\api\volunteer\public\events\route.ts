import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/volunteer/public/events - Get events with volunteer opportunities
export async function GET(req: NextRequest) {
  try {
    // Check if user is authenticated
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    // Get current date
    const now = new Date();

    // Get events with volunteer opportunities
    const events = await prisma.event.findMany({
      where: {
        status: "published",
        endDate: {
          gte: now,
        },
        // Only include events that have volunteer categories
        volunteerCategories: {
          some: {},
        },
      },
      orderBy: {
        startDate: "asc",
      },
      select: {
        id: true,
        name: true,
        description: true,
        shortDescription: true,
        startDate: true,
        endDate: true,
        location: true,
        isVirtual: true,
        image: true,
        // Include count of volunteer categories and shifts
        _count: {
          select: {
            volunteerCategories: true,
          },
        },
      },
    });

    return NextResponse.json({ events });
  } catch (error) {
    console.error("Error fetching volunteer events:", error);
    return NextResponse.json(
      { error: "Failed to fetch volunteer events" },
      { status: 500 },
    );
  }
}
