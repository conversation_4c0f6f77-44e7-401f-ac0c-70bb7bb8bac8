"use client";

import React from "react";
import { usePublicArticle } from "../../../hooks/usePublicNews";
import { notFound, useRouter } from "next/navigation";
import Link from "next/link";
import {
  PublicArticle,
  RelatedArticle,
} from "../../../services/publicNewsService";
import ArticleViewTracker from "../../../components/news/ArticleViewTracker";

interface NewsArticlePageProps {
  params: {
    slug: string;
  };
}

export default function NewsArticlePage({ params }: NewsArticlePageProps) {
  const { slug } = params;
  const router = useRouter();

  const { data: article, isLoading, error } = usePublicArticle(slug);

  // Functions to handle social media sharing
  const handleShare = (platform: string) => {
    if (!article) return;

    const articleUrl =
      typeof window !== "undefined"
        ? `${window.location.origin}/news/${article.slug}`
        : `/news/${article.slug}`;

    const articleTitle = article.title;
    let shareUrl = "";

    switch (platform) {
      case "reddit":
        shareUrl = `https://www.reddit.com/submit?url=${encodeURIComponent(
          articleUrl,
        )}&title=${encodeURIComponent(articleTitle)}`;
        break;
      case "bluesky":
        // Bluesky doesn't have a direct sharing URL yet, so we'll use a text copy approach
        navigator.clipboard.writeText(`${articleTitle} ${articleUrl}`);
        alert(
          "Article link copied to clipboard. You can now paste it in Bluesky.",
        );
        return;
      default:
        return;
    }

    // Open the share URL in a new window
    if (shareUrl) {
      window.open(shareUrl, "_blank", "noopener,noreferrer");
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto px-2 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-600 rounded w-1/4 mb-8"></div>
          <div className="h-64 bg-gray-600 rounded mb-8"></div>
          <div className="h-4 bg-gray-600 rounded w-3/4 mb-4"></div>
          <div className="h-4 bg-gray-600 rounded w-1/2 mb-8"></div>
          <div className="h-12 bg-gray-600 rounded mb-8"></div>
        </div>
      </div>
    );
  }

  // Error or article not found
  if (error || !article) {
    return notFound();
  }

  const formattedDate = new Date(article.publishedAt).toLocaleDateString(
    "en-US",
    {
      year: "numeric",
      month: "long",
      day: "numeric",
    },
  );

  return (
    <div className="container mx-auto px-2 py-8">
      {/* Track article view */}
      {article && <ArticleViewTracker articleId={article.id} />}

      <div className="mb-4">
        <Link href="/news" className="text-primary hover:text-primary-light">
          ← Back to News
        </Link>
      </div>

      <div className="max-w-4xl mx-auto">
        <article
          className="bg-secondary-light rounded-lg shadow-md overflow-hidden"
          data-url={
            typeof window !== "undefined"
              ? `${window.location.origin}/news/${article.slug}`
              : `/news/${article.slug}`
          }
        >
          {article.image && (
            <div className="relative w-full overflow-hidden">
              <img
                src={article.image}
                alt={article.title}
                className="w-full h-full object-contain"
                onError={(e) => {
                  // Fallback if image fails to load
                  const target = e.target as HTMLImageElement;
                  target.onerror = null; // Prevent infinite error loop
                  target.src = "/images/placeholder-news.jpg"; // Fallback image
                }}
              />
              <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-t from-black/60 to-transparent"></div>
              <div className="absolute bottom-0 left-0 w-full p-6 article-header">
                <span className="inline-block bg-primary text-white text-xs font-bold px-3 py-1 rounded-full mb-3">
                  {article.category.name}
                </span>
                <h1 className="text-3xl md:text-4xl font-bold text-white mb-2">
                  {article.title}
                </h1>
                <div className="flex items-center text-white/80">
                  <span className="mr-4">{formattedDate}</span>
                  <span>By {article.author.displayName}</span>
                </div>
              </div>
            </div>
          )}
          <div className="p-6 md:p-8">
            {!article.image && (
              <>
                <span className="inline-block bg-primary text-white text-xs font-bold px-3 py-1 rounded-full mb-3">
                  {article.category.name}
                </span>
                <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  {article.title}
                </h1>
                <div className="flex items-center text-gray-400 mb-6">
                  <span className="mr-4">{formattedDate}</span>
                  <span>By {article.author.displayName}</span>
                </div>
              </>
            )}
            <div className="prose max-w-none text-gray-400">
              <div
                dangerouslySetInnerHTML={{ __html: article.content || "" }}
                className="ql-content"
              />
            </div>

            <div className="mt-8 pt-6 border-t border-gray-600">
              <div className="flex items-center justify-between">
                <div className="flex items-center share-buttons">
                  <span className="text-gray-400 mr-2">Share:</span>
                  <div className="flex space-x-2">
                    {/* Reddit */}
                    <button
                      className="p-2 rounded-full bg-secondary hover:bg-hover"
                      onClick={() => handleShare("reddit")}
                      aria-label="Share on Reddit"
                    >
                      <img
                        src="/images/icons/reddit.png"
                        alt="Reddit"
                        className="w-5 h-5"
                      />
                    </button>

                    {/* Bluesky */}
                    <button
                      className="p-2 rounded-full bg-secondary hover:bg-hover"
                      onClick={() => handleShare("bluesky")}
                      aria-label="Share on Bluesky"
                    >
                      <img
                        src="/images/icons/bluesky.png"
                        alt="Bluesky"
                        className="w-5 h-5"
                      />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </article>

        {/* Related Articles */}
        {article.relatedArticles && article.relatedArticles.length > 0 && (
          <div className="mt-12">
            <h2 className="text-xl md:text-2xl font-bold mb-6">
              Related Articles
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {article.relatedArticles.map((relatedArticle) => (
                <div
                  key={relatedArticle.id}
                  className="cursor-pointer"
                  onClick={() => router.push(`/news/${relatedArticle.slug}`)}
                >
                  <div className="bg-secondary-light rounded-lg shadow-md overflow-hidden border border-gray-600 h-full flex flex-col">
                    {relatedArticle.image && (
                      <div className="relative h-80 w-full overflow-hidden">
                        <img
                          src={relatedArticle.image}
                          alt={relatedArticle.title}
                          className="w-full h-full object-contain"
                          onError={(e) => {
                            // Fallback if image fails to load
                            const target = e.target as HTMLImageElement;
                            target.onerror = null; // Prevent infinite error loop
                            target.src = "/images/placeholder-news.jpg"; // Fallback image
                          }}
                        />
                      </div>
                    )}
                    <div className="p-4 flex-grow">
                      <h3 className="text-lg font-bold mb-2 text-white">
                        {relatedArticle.title}
                      </h3>
                      <p className="text-gray-400 text-sm line-clamp-2">
                        {relatedArticle.excerpt}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
