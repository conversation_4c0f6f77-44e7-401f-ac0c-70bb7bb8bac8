/*
  Warnings:

  - A unique constraint covering the columns `[leadManagerCategoryId]` on the table `user` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[leadManagerId]` on the table `volunteer_categories` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE `user` ADD COLUMN `isLeadManager` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `isVolunteerCoordinator` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `leadManagerCategoryId` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `volunteer_categories` ADD COLUMN `leadManagerId` VARCHAR(191) NULL;

-- CreateIndex
CREATE UNIQUE INDEX `user_leadManagerCategoryId_key` ON `user`(`leadManagerCategoryId`);

-- CreateIndex
CREATE UNIQUE INDEX `volunteer_categories_leadManagerId_key` ON `volunteer_categories`(`leadManagerId`);

-- Add<PERSON><PERSON><PERSON>Key
ALTER TABLE `user` ADD CONSTRAINT `user_leadManagerCategoryId_fkey` FOREIGN KEY (`leadManagerCategoryId`) REFERENCES `volunteer_categories`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
