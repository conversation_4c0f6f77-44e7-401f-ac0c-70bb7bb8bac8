import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../lib/prisma";
import { getCurrentUser, userHasRole } from "../../../../lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// POST endpoint to create a notification
export async function POST(req: NextRequest) {
  try {
    // Get current user
    const currentUser = await getCurrentUser(req);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Check if user has admin role (only admins or system can create notifications for others)
    const isAdmin = await userHasRole(req, "admin");

    // Parse request body
    const body = await req.json();
    const {
      userId,
      category,
      type,
      title,
      message,
      link,
      icon,
      priority = "medium",
      transactionId,
    } = body;

    // Validate required fields
    if (!category || !type || !title || !message) {
      return NextResponse.json(
        { error: "Category, type, title, and message are required" },
        { status: 400 },
      );
    }

    // If creating notification for another user, check admin privileges
    if (userId && userId !== currentUser.id && !isAdmin) {
      return NextResponse.json(
        {
          error:
            "Admin privileges required to create notifications for other users",
        },
        { status: 403 },
      );
    }

    // Create the notification
    const notification = await prisma.notification.create({
      data: {
        userId: userId || currentUser.id,
        category,
        type,
        title,
        message,
        link,
        icon,
        priority,
        transactionId,
      },
    });

    return NextResponse.json({
      notification: {
        ...notification,
        createdAt: notification.createdAt.toISOString(),
        updatedAt: notification.updatedAt.toISOString(),
      },
      serverTimestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error creating notification:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
