import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

// This function handles middleware for the main site
export function middleware(request: NextRequest) {
  // For API requests, make sure CORS headers are applied
  if (request.nextUrl.pathname.startsWith("/api/")) {
    // Add CORS headers to all responses
    const response = NextResponse.next();

    response.headers.set("Access-Control-Allow-Origin", "*");
    response.headers.set(
      "Access-Control-Allow-Methods",
      "GET, POST, PUT, DELETE, OPTIONS",
    );
    response.headers.set(
      "Access-Control-Allow-Headers",
      "Content-Type, Authorization",
    );

    return response;
  }

  return NextResponse.next();
}

// Only match API routes
export const config = {
  matcher: "/api/:path*",
};
