import { NextRequest, NextResponse } from "next/server";
import { saveImage } from "@/services/imageUploadService";

export async function POST(request: NextRequest) {
  try {
    // Check if request is multipart/form-data
    const contentType = request.headers.get("content-type");
    if (!contentType || !contentType.includes("multipart/form-data")) {
      return NextResponse.json(
        {
          error: "Invalid content type, must be multipart/form-data",
          details: `Received content-type: ${contentType || "none"}`,
        },
        { status: 400 },
      );
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const userIdValue = formData.get("userId");
    const userId = userIdValue ? String(userIdValue) : undefined;

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        {
          error:
            "File type not supported. Please upload an image (JPEG, PNG, GIF, WEBP)",
        },
        { status: 400 },
      );
    }

    // Validate file size (2MB max for avatars)
    const maxSize = 2 * 1024 * 1024; // 2MB in bytes
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: "File size exceeds 2MB limit" },
        { status: 400 },
      );
    }

    // Use the image upload service for avatars too
    const result = await saveImage(file, {
      entityType: "avatar",
      entityId: userId,
      directory: "avatars",
    });

    return NextResponse.json(
      {
        success: true,
        file: {
          url: result.url,
          name: result.filename,
          originalName: file.name,
          size: file.size,
          type: file.type,
        },
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("Error uploading avatar:", error);

    // Provide more detailed error message
    const errorMessage =
      error instanceof Error
        ? `Failed to upload avatar: ${error.message}`
        : "Failed to upload avatar";

    return NextResponse.json(
      {
        error: errorMessage,
        details: error instanceof Error ? error.stack : undefined,
      },
      { status: 500 },
    );
  }
}
