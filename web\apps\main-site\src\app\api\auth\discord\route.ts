import { NextRequest, NextResponse } from "next/server";
import { getDiscordAuthUrl } from "../../../../lib/discord";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export const OPTIONS = async (req: NextRequest) => {
  // Handle OPTIONS request for CORS preflight
  const response = new NextResponse(null, { status: 204 });

  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set("Access-Control-Allow-Methods", "GET, OPTIONS");
  response.headers.set(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization",
  );

  return response;
};

export const GET = async (req: NextRequest) => {
  try {
    // Get query parameters
    const url = new URL(req.url);
    const connect = url.searchParams.get("connect") === "true";
    const returnUrl = url.searchParams.get("returnUrl") || "";

    // Generate state parameter to include connection info
    const state = Buffer.from(
      JSON.stringify({
        connect,
        returnUrl,
      }),
    ).toString("base64");

    // Get Discord auth URL with state parameter
    const authUrl = getDiscordAuthUrl(state);
    return NextResponse.redirect(authUrl);
  } catch (error) {
    console.error("Discord auth error:", error);
    return NextResponse.json(
      { error: "Failed to initiate Discord authentication" },
      { status: 500 },
    );
  }
};
