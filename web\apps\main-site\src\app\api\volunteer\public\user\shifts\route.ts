import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";
import { Prisma } from "@prisma/client";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/volunteer/public/user/shifts - Get user's shifts
export async function GET(req: NextRequest) {
  try {
    // Check if user is authenticated
    const user = await getCurrentUser(req);
    if (!user) {
      console.log("Authentication failed: No user found");
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    console.log(`User authenticated: ${user.id}`);

    // Check if the required tables exist
    try {
      // Try to count records to verify table exists
      await prisma.volunteerAssignment.count();
    } catch (schemaError: any) {
      console.error(
        "Schema error - volunteerAssignment table may not exist:",
        schemaError,
      );
      return NextResponse.json(
        {
          error: "Database schema error",
          details:
            "The volunteer assignment table may not exist or is not accessible",
          code: schemaError.code || "SCHEMA_ERROR",
        },
        { status: 500 },
      );
    }

    // Get current date
    const now = new Date();

    try {
      // Get user's upcoming shifts
      const assignments = await prisma.volunteerAssignment.findMany({
        where: {
          userId: user.id,
          status: {
            in: ["pending", "confirmed"],
          },
          shift: {
            startTime: {
              gte: now,
            },
          },
        },
        orderBy: {
          shift: {
            startTime: "asc",
          },
        },
        select: {
          id: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          shift: {
            select: {
              id: true,
              title: true,
              description: true,
              startTime: true,
              endTime: true,
              location: true,
              categoryId: true,
              eventId: true,
              category: {
                select: {
                  name: true,
                  payRate: true,
                },
              },
              event: {
                select: {
                  name: true,
                },
              },
            },
          },
          notificationPreferences: {
            select: {
              emailNotification: true,
              websiteNotification: true,
            },
          },
        },
      });

      console.log(
        `Found ${assignments.length} assignments for user ${user.id}`,
      );

      // Format assignments to include notification preferences
      const formattedAssignments = assignments.map((assignment) => {
        const { notificationPreferences, ...assignmentData } = assignment;

        return {
          ...assignmentData,
          emailNotification:
            notificationPreferences?.emailNotification || false,
          websiteNotification:
            notificationPreferences?.websiteNotification || false,
        };
      });

      return NextResponse.json({ assignments: formattedAssignments });
    } catch (dbError: any) {
      console.error("Database error fetching user shifts:", dbError);
      return NextResponse.json(
        {
          error: "Database error fetching user shifts",
          details: dbError.message,
          code: dbError.code,
        },
        { status: 500 },
      );
    }
  } catch (error: any) {
    console.error("Error fetching user shifts:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch user shifts",
        details: error.message || "Unknown error",
      },
      { status: 500 },
    );
  }
}
