# Event and Volunteer Management System

## Overview
A comprehensive system for managing events, volunteer categories, shifts, assignments, and payments with role-based access control and automated workflows.

## Core Components

### Event Management
Events are the top-level container for volunteer activities with comprehensive metadata:

#### Event Creation
```typescript
// Admin-only event creation
const event = await prisma.event.create({
  data: {
    name,
    description,
    shortDescription,
    startDate: startDateTime,
    endDate: endDateTime,
    location,
    address,
    virtualLink,
    isVirtual: isVirtual || false,
    image,
    status: status || "draft", // draft, published, cancelled
    capacity: capacity ? parseInt(capacity) : null,
    categoryId,
    createdById: currentUser.id,
  },
});
```

#### Event Status Workflow
- **Draft**: Being prepared, not visible to volunteers
- **Published**: Active, volunteers can sign up
- **Cancelled**: Event cancelled, no new signups

### Volunteer Category System
Categories organize volunteer work with specific pay rates and lead managers:

#### Category Features
- **Pay Rate**: Hourly compensation amount
- **Lead Manager**: User assigned to manage category (role-based)
- **Event Association**: Categories belong to specific events
- **Shift Organization**: Contains multiple volunteer shifts

#### Lead Manager Role
```typescript
// Lead managers have category-specific permissions
const hasLeadRole = await userHasRole(req, "leadManager");
const leadManagerCategoryId = user.leadManagerCategoryId;

// Can only manage shifts in their assigned category
if (assignment.shift.category.id !== leadManagerCategoryId) {
  return NextResponse.json({ error: "This assignment is not in your category" });
}
```

### Volunteer Shift System
Shifts are specific time slots within categories that volunteers can sign up for:

#### Shift Creation (Batch Support)
```typescript
// Supports both single and batch shift creation
const isBatchRequest = Array.isArray(requestData);
const shiftsToCreate = isBatchRequest ? requestData : [requestData];

// Auto-generate volunteer slots for each shift
if (shiftData.maxVolunteers > 0) {
  const slots = Array(shiftData.maxVolunteers)
    .fill(null)
    .map(() => ({
      shiftId: shift.id,
      status: TicketStatus.AVAILABLE,
    }));

  await tx.volunteerSlot.createMany({ data: slots });
}
```

#### Shift Features
- **Time Management**: Start/end times with validation
- **Capacity Control**: Maximum volunteers per shift
- **Location Tracking**: Physical or virtual locations
- **Automated Slots**: Auto-generates volunteer slots
- **Status Tracking**: Available, assigned, completed

### Volunteer Assignment System
Manages volunteer signups and participation tracking:

#### Signup Process
```typescript
// Public signup with comprehensive validation
const assignment = await prisma.volunteerAssignment.create({
  data: {
    userId: user.id,
    shiftId: shift.id,
    status: "pending",
    // Store detailed volunteer information
    metadata: {
      firstName: formData.firstName,
      lastName: formData.lastName,
      email: formData.email,
      pirateName: formData.pirateName,
      stayingWith: formData.stayingWith,
      isDock: formData.isDock,
      isLandGrant: formData.isLandGrant,
      landGrantCredit: formData.landGrantCredit,
      pronouns: formData.pronouns,
      addToDeedsLottery: formData.addToDeedsLottery,
    },
    // Notification preferences
    notificationPreferences: {
      create: {
        emailNotification: formData.emailNotification,
        websiteNotification: formData.websiteNotification,
      },
    },
  },
});
```

#### Assignment Validation
- **Event Status**: Must be published
- **Timing**: Cannot sign up for past shifts
- **Capacity**: Must have available slots
- **Duplicate Check**: Prevents double-signup
- **User Authentication**: Requires logged-in user

#### Assignment Status Flow
1. **Pending**: Initial signup state
2. **Confirmed**: Shift confirmed by coordinator
3. **Completed**: Shift successfully completed
4. **Cancelled**: Volunteer cancelled
5. **No Show**: Volunteer didn't attend

## Payment System
Sophisticated payment processing for volunteer work:

### Payment Initiation
```typescript
// Lead managers initiate payments for completed shifts
const paymentAmount = payRate * hoursWorked * payMultiplier;

const hours = await prisma.volunteerHours.upsert({
  where: { assignmentId },
  update: {
    hoursWorked,
    paymentAmount,
    verifiedById: user.id,
    verifiedAt: new Date(),
    paymentStatus: "pending",
  },
  create: {
    assignmentId,
    userId: assignment.userId,
    hoursWorked,
    paymentAmount,
    verifiedById: user.id,
    verifiedAt: new Date(),
    paymentStatus: "pending",
  },
});
```

### Payment Calculation
- **Hours Worked**: Calculated from shift start/end times
- **Pay Rate**: From volunteer category settings
- **Pay Multiplier**: Optional multiplier for special circumstances
- **Verification**: Lead manager approval required

### Payment Status Workflow
1. **Pending**: Payment initiated, awaiting processing
2. **Processed**: Payment completed
3. **Cancelled**: Payment cancelled

## Role-based Access Control

### User Roles
- **Volunteer Coordinator**: Can create events, categories, and shifts
- **Lead Manager**: Can manage specific category assignments and payments
- **Admin**: Full system access
- **User**: Can sign up for shifts

### Permission Patterns
```typescript
// Coordinator role for category/shift management
const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");

// Lead manager for payment processing
const hasLeadRole = await userHasRole(req, "leadManager");

// Category-specific permissions
if (assignment.shift.category.id !== leadManagerCategoryId) {
  return NextResponse.json({ error: "Unauthorized for this category" });
}
```

## Data Collection System
Comprehensive volunteer information collection:

### Volunteer Metadata
- **Personal**: Name, email, pronouns
- **Pirate Identity**: Pirate name (theme-specific)
- **Accommodation**: Where staying, dock access
- **Land Grant**: Special status tracking
- **Lottery Participation**: Deeds lottery enrollment

### Notification Preferences
- **Email Notifications**: Volunteer prefers email updates
- **Website Notifications**: In-app notification preference
- **Assignment-specific**: Stored per volunteer assignment

## Statistics and Reporting

### Assignment Statistics
```typescript
// Calculate completion rates and participation
const shiftsWithStats = shifts.map((shift) => {
  const totalAssignments = shift._count.assignments;
  const completedAssignments = shift.assignments.filter(
    (assignment) => assignment.status === "completed",
  ).length;

  return {
    ...shift,
    stats: { totalAssignments, completedAssignments },
  };
});
```

### Dashboard Metrics
- **Total Assignments**: All volunteer signups
- **Completed Assignments**: Successfully finished shifts
- **Payment Statistics**: Hours worked, amounts paid
- **Category Performance**: Stats by volunteer category

## API Architecture

### Public Endpoints (Volunteer Access)
- `GET /api/volunteer/public/events` - Available events
- `GET /api/volunteer/public/categories/[id]/shifts` - Available shifts
- `POST /api/volunteer/public/shifts/[id]/signup` - Sign up for shift
- `GET /api/volunteer/public/user/shifts` - User's assignments

### Management Endpoints (Coordinator/Lead)
- `POST /api/volunteer/events/[id]/categories/[categoryId]/shifts` - Create shifts
- `GET /api/volunteer/lead/shifts` - Lead manager's category shifts
- `POST /api/volunteer/lead/payments/initiate` - Process payments
- `PUT /api/volunteer/lead/shifts/[id]/attendance` - Mark attendance

### Admin Endpoints
- `POST /api/admin/events` - Create events
- `GET /api/admin/event-categories` - Manage categories
- `GET /api/volunteer/management/payments/history` - Payment reports

## Integration Features

### Event-Product Integration
Events can have associated products (tickets) for purchase alongside volunteer opportunities.

### Banking Integration
Volunteer payments integrate with the banking system for automated compensation.

### Notification System
Real-time notifications for assignment updates, payment processing, and shift reminders.

## Important Files
- `src/app/api/volunteer/public/shifts/[id]/signup/route.ts` - Volunteer signup
- `src/app/api/volunteer/events/[id]/categories/[categoryId]/shifts/route.ts` - Shift creation
- `src/app/api/volunteer/lead/payments/initiate/route.ts` - Payment processing
- `src/app/api/admin/events/route.ts` - Event management
- `prisma/schema.prisma` - Database models for events, categories, shifts, assignments