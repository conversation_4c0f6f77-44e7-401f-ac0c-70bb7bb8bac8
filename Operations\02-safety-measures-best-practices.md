# Safety Measures and Best Practices

## Overview

This document outlines critical safety measures and best practices for maintaining the Bank of Styx website. Following these procedures will help prevent data loss, security breaches, and system downtime.

## 🚨 CRITICAL SAFETY PRINCIPLES

### The Golden Rules
1. **NEVER work directly on production** - Always use development/staging first
2. **ALWAYS backup before changes** - No exceptions, ever
3. **TEST everything thoroughly** - If it's not tested, it's broken
4. **DOCUMENT everything** - Future you will thank present you
5. **HAVE a rollback plan** - Know how to undo what you're doing

## Version Control Workflows

### Git Branching Strategy

#### Main Branches
- **`main`** - Production-ready code (NEVER commit directly here)
- **`develop`** - Integration branch for features
- **`staging`** - Pre-production testing branch

#### Feature Branches
- **`feature/feature-name`** - New features
- **`hotfix/issue-name`** - Critical production fixes
- **`bugfix/bug-name`** - Non-critical bug fixes

### Safe Git Workflow

#### Starting New Work
```bash
# 1. Always start from main
git checkout main
git pull origin main

# 2. Create feature branch
git checkout -b feature/your-feature-name

# 3. Verify you're on the right branch
git branch
```

#### Making Changes
```bash
# 1. Make small, focused commits
git add specific-files
git commit -m "Clear description of what changed"

# 2. Push regularly to backup your work
git push origin feature/your-feature-name

# 3. Never commit sensitive data
# Check .gitignore includes:
# - .env files
# - Database credentials
# - API keys
# - User uploads
```

#### Merging Changes
```bash
# 1. Update your branch with latest main
git checkout main
git pull origin main
git checkout feature/your-feature-name
git merge main

# 2. Resolve any conflicts carefully
# 3. Test everything still works
# 4. Create pull request for review
```

### Emergency Rollback Procedures

#### If You Need to Undo Recent Changes
```bash
# Undo last commit (keeps changes in working directory)
git reset --soft HEAD~1

# Undo last commit and discard changes (DANGEROUS)
git reset --hard HEAD~1

# Undo specific file changes
git checkout HEAD -- filename.js
```

#### If Production is Broken
```bash
# 1. Immediately revert to last known good state
git checkout main
git reset --hard [last-good-commit-hash]

# 2. Deploy the rollback
./deploy-production.sh

# 3. Investigate the issue in development
```

## Backup and Recovery Procedures

### Daily Automated Backups

#### Database Backups
```bash
# Location: /backups/database/
# Schedule: Every 6 hours
# Retention: 30 days

# Manual backup command:
mysqldump -u root -p bank_of_styx > backup_$(date +%Y%m%d_%H%M%S).sql
```

#### File System Backups
```bash
# Location: /backups/files/
# Schedule: Daily at 2 AM
# Retention: 7 days

# Manual backup command:
tar -czf backup_files_$(date +%Y%m%d).tar.gz /path/to/project
```

#### Code Repository Backups
- **Primary**: GitHub repository
- **Secondary**: Local Git mirrors
- **Frequency**: Real-time (with every push)

### Recovery Procedures

#### Database Recovery
```bash
# 1. Stop the application
pm2 stop bank-of-styx

# 2. Restore database
mysql -u root -p bank_of_styx < backup_file.sql

# 3. Restart application
pm2 start bank-of-styx

# 4. Verify functionality
```

#### File Recovery
```bash
# 1. Stop application
pm2 stop bank-of-styx

# 2. Extract backup
tar -xzf backup_files_YYYYMMDD.tar.gz

# 3. Copy files to correct location
cp -r backup_files/* /path/to/project/

# 4. Set correct permissions
chown -R www-data:www-data /path/to/project

# 5. Restart application
pm2 start bank-of-styx
```

## Security Considerations

### Environment Isolation

#### Development Environment
- **Purpose**: Safe testing and development
- **Database**: Separate development database
- **Domain**: localhost:3000 or dev.bankofstyx.com
- **Data**: Test data only, no real user information

#### Staging Environment
- **Purpose**: Final testing before production
- **Database**: Copy of production data (anonymized)
- **Domain**: staging.bankofstyx.com
- **Access**: Restricted to team members only

#### Production Environment
- **Purpose**: Live website for users
- **Database**: Live user data
- **Domain**: bankofstyx.com
- **Access**: Highly restricted, monitored

### Security Best Practices

#### Environment Variables
```bash
# NEVER commit these to Git:
DATABASE_URL=mysql://user:password@localhost:3306/db
JWT_SECRET=your-secret-key
STRIPE_SECRET_KEY=sk_live_...
EMAIL_PASSWORD=your-email-password

# Always use .env files and add to .gitignore
echo ".env*" >> .gitignore
```

#### Access Control
1. **Database Access**
   - Use dedicated database user (not root)
   - Limit permissions to necessary operations only
   - Regular password rotation

2. **Server Access**
   - SSH key authentication only
   - No password authentication
   - Regular security updates

3. **Application Access**
   - Strong admin passwords
   - Two-factor authentication where possible
   - Regular access review

#### Code Security
```javascript
// GOOD: Parameterized queries
const user = await prisma.user.findUnique({
  where: { id: userId }
});

// BAD: String concatenation (SQL injection risk)
const query = `SELECT * FROM users WHERE id = ${userId}`;
```

### Security Monitoring

#### What to Monitor
- Failed login attempts
- Unusual database activity
- File system changes
- Network traffic anomalies
- Error rate spikes

#### Alert Thresholds
- More than 5 failed logins in 10 minutes
- Database queries taking longer than 5 seconds
- Error rate above 1%
- Disk space above 80%

## Risk Assessment Guidelines

### Change Risk Levels

#### Low Risk (Green Light)
- Content updates (text, images)
- CSS styling changes
- Static page additions
- Documentation updates

**Approval Required**: Team lead
**Testing Required**: Basic functionality test
**Backup Required**: Standard daily backup

#### Medium Risk (Yellow Light)
- New features
- Database schema changes
- API modifications
- Third-party integrations

**Approval Required**: Technical lead + stakeholder
**Testing Required**: Full regression testing
**Backup Required**: Pre-change backup + staging deployment

#### High Risk (Red Light)
- Core system changes
- Security modifications
- Payment system changes
- Major database migrations

**Approval Required**: All stakeholders + external review
**Testing Required**: Comprehensive testing + load testing
**Backup Required**: Multiple backups + detailed rollback plan

### Risk Mitigation Strategies

#### For All Changes
1. **Backup everything** before starting
2. **Test in development** first
3. **Document the process** step by step
4. **Have a rollback plan** ready
5. **Monitor after deployment**

#### For High-Risk Changes
1. **Schedule during low-traffic periods**
2. **Have technical support on standby**
3. **Prepare user communication**
4. **Plan for extended downtime**
5. **Consider phased rollout**

## Emergency Response Procedures

### Website Down
1. **Check server status**
   ```bash
   pm2 status
   systemctl status nginx
   ```

2. **Check logs**
   ```bash
   pm2 logs bank-of-styx
   tail -f /var/log/nginx/error.log
   ```

3. **Restart services**
   ```bash
   pm2 restart bank-of-styx
   systemctl restart nginx
   ```

4. **If still down, rollback**
   ```bash
   git checkout main
   git reset --hard [last-good-commit]
   pm2 restart bank-of-styx
   ```

### Database Issues
1. **Check database connectivity**
   ```bash
   mysql -u root -p -e "SELECT 1"
   ```

2. **Check disk space**
   ```bash
   df -h
   ```

3. **Restart database if needed**
   ```bash
   systemctl restart mysql
   ```

4. **Restore from backup if corrupted**

### Security Breach
1. **Immediately change all passwords**
2. **Review access logs**
3. **Notify users if data compromised**
4. **Contact security expert**
5. **Document incident**

## Maintenance Schedules

### Daily Tasks
- [ ] Check website accessibility
- [ ] Review error logs
- [ ] Verify backup completion
- [ ] Monitor disk space

### Weekly Tasks
- [ ] Review security logs
- [ ] Update system packages
- [ ] Check SSL certificate status
- [ ] Performance monitoring review

### Monthly Tasks
- [ ] Full system backup verification
- [ ] Security audit
- [ ] Performance optimization review
- [ ] Documentation updates

### Quarterly Tasks
- [ ] Disaster recovery test
- [ ] Security penetration test
- [ ] Full system health check
- [ ] Team training updates

## Advanced Backup Strategies

### Automated Backup System

#### Database Backup Script
```bash
#!/bin/bash
# backup-database.sh - Automated database backup

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/database"
DB_NAME="bank_of_styx"
RETENTION_DAYS=30

# Create backup directory
mkdir -p $BACKUP_DIR

# Create database backup
mysqldump -u root -p$DB_PASSWORD $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/db_backup_$DATE.sql

# Remove old backups
find $BACKUP_DIR -name "*.sql.gz" -mtime +$RETENTION_DAYS -delete

# Verify backup integrity
gunzip -t $BACKUP_DIR/db_backup_$DATE.sql.gz

if [ $? -eq 0 ]; then
    echo "Backup completed successfully: db_backup_$DATE.sql.gz"
else
    echo "Backup verification failed!" >&2
    exit 1
fi
```

#### Application Backup Script
```bash
#!/bin/bash
# backup-application.sh - Full application backup

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/application"
APP_DIR="/path/to/bank-of-styx"
RETENTION_DAYS=7

# Create backup directory
mkdir -p $BACKUP_DIR

# Stop application for consistent backup
pm2 stop bank-of-styx

# Create application backup (excluding node_modules and .next)
tar --exclude='node_modules' \
    --exclude='.next' \
    --exclude='*.log' \
    -czf $BACKUP_DIR/app_backup_$DATE.tar.gz \
    -C $APP_DIR .

# Restart application
pm2 start bank-of-styx

# Remove old backups
find $BACKUP_DIR -name "*.tar.gz" -mtime +$RETENTION_DAYS -delete

echo "Application backup completed: app_backup_$DATE.tar.gz"
```

### Disaster Recovery Procedures

#### Complete System Recovery Plan

##### Phase 1: Assessment (15 minutes)
1. **Determine scope of disaster**
   - Hardware failure?
   - Data corruption?
   - Security breach?
   - Natural disaster?

2. **Assess available resources**
   - Backup integrity
   - Alternative hardware
   - Network connectivity
   - Team availability

##### Phase 2: Emergency Response (30 minutes)
1. **Secure the environment**
   ```bash
   # If security breach suspected
   # Change all passwords immediately
   # Disconnect from network if necessary
   # Document everything
   ```

2. **Notify stakeholders**
   - Technical team
   - Community leadership
   - Users (if necessary)
   - Hosting provider

##### Phase 3: System Recovery (2-4 hours)
1. **Prepare new environment**
   ```bash
   # Set up new server if needed
   # Install required software
   # Configure network settings
   # Set up security measures
   ```

2. **Restore from backups**
   ```bash
   # Restore database
   mysql -u root -p bank_of_styx < latest_backup.sql

   # Restore application files
   tar -xzf latest_app_backup.tar.gz

   # Restore environment configuration
   cp .env.production .env.local
   ```

3. **Verify system integrity**
   ```bash
   # Test database connectivity
   # Verify application starts
   # Check all major functions
   # Run security scan
   ```

##### Phase 4: Service Restoration (1 hour)
1. **Gradual service restoration**
   - Start with read-only mode
   - Enable basic functionality
   - Restore full functionality
   - Monitor for issues

2. **User communication**
   - Announce service restoration
   - Explain any data loss
   - Provide support contact
   - Thank users for patience

### Backup Verification Procedures

#### Daily Backup Verification
```bash
#!/bin/bash
# verify-backups.sh - Daily backup verification

BACKUP_DIR="/backups"
LOG_FILE="/var/log/backup-verification.log"

echo "$(date): Starting backup verification" >> $LOG_FILE

# Verify database backup
LATEST_DB_BACKUP=$(ls -t $BACKUP_DIR/database/*.sql.gz | head -1)
if [ -f "$LATEST_DB_BACKUP" ]; then
    gunzip -t "$LATEST_DB_BACKUP"
    if [ $? -eq 0 ]; then
        echo "$(date): Database backup verified: $LATEST_DB_BACKUP" >> $LOG_FILE
    else
        echo "$(date): ERROR: Database backup corrupted: $LATEST_DB_BACKUP" >> $LOG_FILE
        # Send alert
        curl -X POST "https://discord.com/api/webhooks/YOUR_WEBHOOK" \
          -H "Content-Type: application/json" \
          -d '{"content": "ALERT: Database backup verification failed!"}'
    fi
else
    echo "$(date): ERROR: No database backup found!" >> $LOG_FILE
fi

# Verify application backup
LATEST_APP_BACKUP=$(ls -t $BACKUP_DIR/application/*.tar.gz | head -1)
if [ -f "$LATEST_APP_BACKUP" ]; then
    tar -tzf "$LATEST_APP_BACKUP" > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "$(date): Application backup verified: $LATEST_APP_BACKUP" >> $LOG_FILE
    else
        echo "$(date): ERROR: Application backup corrupted: $LATEST_APP_BACKUP" >> $LOG_FILE
    fi
else
    echo "$(date): ERROR: No application backup found!" >> $LOG_FILE
fi
```

#### Weekly Recovery Testing
```bash
#!/bin/bash
# test-recovery.sh - Weekly recovery test

TEST_DIR="/tmp/recovery-test"
BACKUP_DIR="/backups"

echo "Starting weekly recovery test..."

# Create test environment
mkdir -p $TEST_DIR
cd $TEST_DIR

# Test database recovery
echo "Testing database recovery..."
LATEST_DB_BACKUP=$(ls -t $BACKUP_DIR/database/*.sql.gz | head -1)
gunzip -c "$LATEST_DB_BACKUP" > test_restore.sql

# Create test database
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS test_recovery;"
mysql -u root -p test_recovery < test_restore.sql

# Verify data integrity
RECORD_COUNT=$(mysql -u root -p test_recovery -e "SELECT COUNT(*) FROM User;" | tail -1)
if [ "$RECORD_COUNT" -gt 0 ]; then
    echo "Database recovery test: PASSED ($RECORD_COUNT users found)"
else
    echo "Database recovery test: FAILED (No users found)"
fi

# Clean up test database
mysql -u root -p -e "DROP DATABASE test_recovery;"

# Test application recovery
echo "Testing application recovery..."
LATEST_APP_BACKUP=$(ls -t $BACKUP_DIR/application/*.tar.gz | head -1)
tar -xzf "$LATEST_APP_BACKUP"

# Verify critical files exist
if [ -f "package.json" ] && [ -f "web/apps/main-site/package.json" ]; then
    echo "Application recovery test: PASSED"
else
    echo "Application recovery test: FAILED"
fi

# Clean up
cd /
rm -rf $TEST_DIR

echo "Weekly recovery test completed."
```

## Security Hardening

### Server Security Configuration

#### Firewall Configuration
```bash
# Configure UFW firewall
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 3000/tcp  # Application port
ufw enable

# Check firewall status
ufw status verbose
```

#### SSH Security
```bash
# Edit SSH configuration
sudo nano /etc/ssh/sshd_config

# Recommended settings:
# Port 2222  # Change from default 22
# PermitRootLogin no
# PasswordAuthentication no
# PubkeyAuthentication yes
# MaxAuthTries 3
# ClientAliveInterval 300
# ClientAliveCountMax 2

# Restart SSH service
sudo systemctl restart sshd
```

#### Database Security
```sql
-- Create dedicated database user
CREATE USER 'bankofstyx_app'@'localhost' IDENTIFIED BY 'strong_password_here';

-- Grant minimal required permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON bank_of_styx.* TO 'bankofstyx_app'@'localhost';

-- Remove unnecessary permissions
REVOKE ALL PRIVILEGES ON *.* FROM 'bankofstyx_app'@'localhost';

-- Flush privileges
FLUSH PRIVILEGES;
```

### Application Security

#### Environment Variable Security
```bash
# Secure environment file permissions
chmod 600 .env.production
chown www-data:www-data .env.production

# Use strong secrets
JWT_SECRET=$(openssl rand -base64 32)
CRON_SECRET_KEY=$(openssl rand -base64 32)
```

#### File Upload Security
```javascript
// Secure file upload configuration
const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
const maxFileSize = 10 * 1024 * 1024; // 10MB

// Validate file type and size
function validateUpload(file) {
  if (!allowedTypes.includes(file.mimetype)) {
    throw new Error('Invalid file type');
  }
  if (file.size > maxFileSize) {
    throw new Error('File too large');
  }
  return true;
}
```

### Security Monitoring

#### Log Analysis
```bash
# Monitor failed login attempts
grep "Failed login" /var/log/auth.log | tail -20

# Monitor application errors
pm2 logs bank-of-styx | grep -i error

# Monitor database access
tail -f /var/log/mysql/mysql.log | grep -i "access denied"
```

#### Intrusion Detection
```bash
# Install fail2ban
sudo apt install fail2ban

# Configure fail2ban for SSH
sudo nano /etc/fail2ban/jail.local

# Add configuration:
[sshd]
enabled = true
port = 2222
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600
```

---

*Remember: Security is everyone's responsibility. When in doubt, choose the safer option.*
