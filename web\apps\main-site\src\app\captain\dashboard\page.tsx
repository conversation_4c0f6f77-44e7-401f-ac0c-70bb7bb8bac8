"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { CaptainDashboardLayout } from "@/components/captain";
import { VolunteerHoursStatus } from "@/components/captain";
import { useCaptainShip } from "@/hooks/captain/useCaptainShip";
import { useVolunteerRequirements } from "@/hooks/captain/useVolunteerRequirements";
import { Button, Card } from "@bank-of-styx/ui";
import Link from "next/link";

export default function CaptainDashboard() {
  const { user, isLoading: authLoading, openAuthModal } = useAuth();
  const router = useRouter();
  
  const { ship, statistics, recentActivity, isLoading, error } = useCaptainShip();
  const { requirements, isLoading: requirementsLoading } = useVolunteerRequirements();

  // Authentication and authorization checks
  React.useEffect(() => {
    if (!authLoading && !user) {
      openAuthModal();
      router.replace("/");
      return;
    }
  }, [user, authLoading, router, openAuthModal]);

  // Loading state
  if (authLoading || isLoading) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-400">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!authLoading && !user) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-400">Redirecting...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-secondary-dark">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-secondary-light rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Captain Dashboard Unavailable</h3>
            <p className="text-gray-400 mb-4">{error.message}</p>
            <Link href="/ships/apply">
              <Button variant="primary">Apply to be a Captain</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!ship) {
    return null;
  }

  return (
    <CaptainDashboardLayout ship={ship}>
      <div className="space-y-6">
        {/* Welcome Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between bg-secondary-light rounded-lg border border-gray-600 p-4">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-white mb-1">
              Welcome back, Captain
            </h1>
            <p className="text-sm sm:text-base text-gray-400">
              Here's what's happening with {ship.name}
            </p>
          </div>
          <div className="mt-3 sm:mt-0">
            <Link href={`/ships/${ship.id}`}>
              <Button variant="outline" size="sm">
                View Ship Page
              </Button>
            </Link>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-secondary-light rounded-lg border border-gray-600 p-4 sm:p-6 m-2">
          <h2 className="text-lg font-semibold text-white mb-4">Quick Actions</h2>
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
            <Link href="/captain/dashboard/members">
              <Button variant="outline" size="sm" className="w-full justify-start">
                <span className="hidden sm:inline">Manage Members</span>
                <span className="sm:hidden">Members</span>
              </Button>
            </Link>
            
            <Link href="/captain/dashboard/invite">
              <Button variant="outline" size="sm" className="w-full justify-start">
                <span className="hidden sm:inline">Invite Users</span>
                <span className="sm:hidden">Invite</span>
              </Button>
            </Link>
            
            <Link href="/captain/dashboard/forms">
              <Button variant="outline" size="sm" className="w-full justify-start">
                <span className="hidden sm:inline">View Forms</span>
                <span className="sm:hidden">Forms</span>
              </Button>
            </Link>
            
            <Link href="/captain/dashboard/settings">
              <Button variant="outline" size="sm" className="w-full justify-start">
                <span className="hidden sm:inline">Ship Settings</span>
                <span className="sm:hidden">Settings</span>
              </Button>
            </Link>
          </div>
        </div>

        {/* Volunteer Hours Status */}
        <div className="bg-secondary-light rounded-lg border border-gray-600 p-4">
          <VolunteerHoursStatus 
            requirements={requirements}
            loading={requirementsLoading}
          />
        </div>

        {/* Statistics Grid */}
        <div className="bg-secondary-light rounded-lg border border-gray-600 p-4">
          <h2 className="text-lg font-semibold text-white mb-4">Statistics</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="bg-secondary-dark rounded-lg border border-gray-600 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Total Members</p>
                  <p className="text-2xl font-bold text-white">
                    {statistics?.totalMembers || 0}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-secondary-dark rounded-lg border border-gray-600 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Join Requests</p>
                  <p className="text-2xl font-bold text-white">
                    {statistics?.pendingRequests || 0}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-secondary-dark rounded-lg border border-gray-600 p-4 sm:col-span-2 lg:col-span-1">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Recent Joins</p>
                  <p className="text-2xl font-bold text-white">
                    {statistics?.recentJoins || 0}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        {recentActivity && recentActivity.length > 0 && (
          <div className="bg-secondary-light rounded-lg border border-gray-600 p-4 sm:p-6">
            <h2 className="text-lg font-semibold text-white mb-4">Recent Activity</h2>
            <div className="space-y-3">
              {recentActivity.slice(0, 5).map((activity, index) => (
                <div key={index} className="flex items-center gap-3 p-2 rounded bg-secondary-dark/50 border border-gray-600">
                  <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center flex-shrink-0">
                    <span className="text-xs">
                      {activity.type === 'join' ? '👋' : 
                       activity.type === 'leave' ? '👋' : '📝'}
                    </span>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm text-gray-300">
                      <span className="font-medium text-white">
                        {activity.user.displayName || activity.user.username}
                      </span>
                      {' '}
                      {activity.type === 'join' && 'joined the ship'}
                      {activity.type === 'leave' && 'left the ship'}
                      {activity.type === 'form' && 'submitted a form'}
                    </p>
                    <p className="text-xs text-gray-500">
                      {new Date(activity.timestamp).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </CaptainDashboardLayout>
  );
}