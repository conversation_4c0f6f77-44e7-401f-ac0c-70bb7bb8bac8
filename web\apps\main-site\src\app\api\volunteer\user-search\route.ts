import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/volunteer/user-search - Search for users to assign as volunteers
export async function GET(req: NextRequest) {
  try {
    // Check if user is authenticated and has volunteer coordinator role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");
    if (!hasCoordinatorRole) {
      return NextResponse.json(
        { error: "Unauthorized - Coordinator role required" },
        { status: 403 },
      );
    }

    // Get query parameter
    const url = new URL(req.url);
    const query = url.searchParams.get("query");

    if (!query || query.length < 2) {
      return NextResponse.json([]);
    }

    console.log(
      `Volunteer user search: query="${query}", length=${query.length}`,
    );

    // Search for users without using mode: "insensitive" which might not be supported
    // by all database configurations
    const users = await prisma.user.findMany({
      where: {
        OR: [
          {
            username: {
              contains: query,
              // Remove mode: "insensitive" to avoid potential database compatibility issues
            },
          },
          {
            displayName: {
              contains: query,
              // Remove mode: "insensitive" to avoid potential database compatibility issues
            },
          },
          {
            email: {
              contains: query,
              // Remove mode: "insensitive" to avoid potential database compatibility issues
            },
          },
        ],
      },
      select: {
        id: true,
        username: true,
        displayName: true,
        avatar: true,
        email: true,
      },
      take: 20, // Fetch more results to allow for manual filtering
    });

    // Manually filter for case-insensitive matching
    const lowercaseQuery = query.toLowerCase();
    const filteredUsers = users
      .filter(
        (user) =>
          (user.username &&
            user.username.toLowerCase().includes(lowercaseQuery)) ||
          (user.displayName &&
            user.displayName.toLowerCase().includes(lowercaseQuery)) ||
          (user.email && user.email.toLowerCase().includes(lowercaseQuery)),
      )
      .slice(0, 10); // Limit to 10 results after filtering

    console.log(
      `Volunteer user search results: found ${filteredUsers.length} users`,
    );
    return NextResponse.json(filteredUsers);
  } catch (error) {
    console.error("Error searching for users:", error);
    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorStack = error instanceof Error ? error.stack : undefined;
    console.error("Detailed error:", errorMessage);
    if (errorStack) console.error("Stack trace:", errorStack);

    return NextResponse.json(
      {
        error: "Failed to search for users",
        details: errorMessage,
      },
      { status: 500 },
    );
  }
}
