import React, { useState, useEffect } from "react";

// We'll use the RichTextEditor as a simple wrapper that the consuming app needs to extend
// This avoids dependency issues in the UI package

// Define default toolbar options that consuming apps can use
export const defaultToolbarOptions = [
  [{ header: [1, 2, 3, 4, 5, 6, false] }],
  ["bold", "italic", "underline", "strike"],
  [{ color: [] }, { background: [] }],
  [{ list: "ordered" }, { list: "bullet" }],
  [{ align: [] }],
  ["blockquote", "code-block"],
  ["link", "image"],
  ["clean"],
];

export interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  readOnly?: boolean;
  minHeight?: string;
  className?: string;
  error?: boolean;
  errorMessage?: string;
}

// This is a minimal implementation that the consuming app needs to extend
// It avoids trying to import React Quill directly in the UI package
export const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = "Start typing...",
  readOnly = false,
  minHeight = "300px",
  className = "",
  error = false,
  errorMessage = "This field is required",
}) => {
  const [isMounted, setIsMounted] = useState(false);

  // Handle SSR
  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <div className={`w-full ${className}`}>
      {isMounted ? (
        <div className={`${error ? "border border-red-500 rounded-md" : ""}`}>
          <div className="bg-gray-100 p-4 text-center rounded">
            <p>Rich Text Editor would render here in the consuming app.</p>
            <p>
              Please implement the actual editor in your app using this
              component as a wrapper.
            </p>
          </div>
        </div>
      ) : (
        <div className="h-60 w-full bg-gray-100 animate-pulse rounded-md"></div>
      )}
      {error && errorMessage && (
        <p className="mt-1 text-sm text-red-500">{errorMessage}</p>
      )}
    </div>
  );
};
