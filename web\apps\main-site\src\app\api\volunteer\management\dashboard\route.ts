import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// GET /api/volunteer/management/dashboard - Get dashboard data for a specific category (coordinator view)
export async function GET(req: NextRequest) {
  try {
    // Check if user is authenticated and has coordinator role
    const user = await getCurrentUser(req);
    
    // Temporarily disable authentication for testing
    // if (!user) {
    //   return NextResponse.json(
    //     { error: "Unauthorized - Authentication required" },
    //     { status: 401 },
    //   );
    // }

    // const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");
    // if (!hasCoordinatorRole) {
    //   return NextResponse.json(
    //     { error: "Unauthorized - Volunteer Coordinator role required" },
    //     { status: 403 },
    //   );
    // }

    // Get categoryId from query parameters
    const { searchParams } = new URL(req.url);
    const categoryId = searchParams.get("categoryId");

    if (!categoryId) {
      return NextResponse.json(
        { error: "Category ID is required" },
        { status: 400 },
      );
    }

    // Get current date for upcoming shifts calculation
    const now = new Date();

    // Get the category with event details
    const category = await prisma.volunteerCategory.findUnique({
      where: { id: categoryId },
      include: {
        event: {
          select: {
            id: true,
            name: true,
            startDate: true,
            endDate: true,
            status: true,
          },
        },
      },
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 },
      );
    }

    // Get shifts for the category
    const shifts = await prisma.volunteerShift.count({
      where: {
        categoryId: categoryId,
      },
    });

    // Get upcoming shifts count
    const upcomingShifts = await prisma.volunteerShift.count({
      where: {
        categoryId: categoryId,
        startTime: {
          gte: now,
        },
      },
    });

    // Get total volunteers assigned to this category's shifts
    const volunteerAssignments = await prisma.volunteerAssignment.findMany({
      where: {
        shift: {
          categoryId: categoryId,
        },
      },
      distinct: ["userId"],
      select: {
        userId: true,
      },
    });
    const volunteers = volunteerAssignments.length;

    // Get completed shifts count
    const completedShifts = await prisma.volunteerAssignment.count({
      where: {
        shift: {
          categoryId: categoryId,
        },
        status: "completed",
      },
    });

    // Get pending payments count
    const pendingPayments = await prisma.volunteerHours.count({
      where: {
        assignment: {
          shift: {
            categoryId: categoryId,
          },
        },
        paymentStatus: "pending",
      },
    });

    return NextResponse.json({
      category,
      stats: {
        totalShifts: shifts,
        upcomingShifts,
        totalVolunteers: volunteers,
        completedShifts,
        pendingPayments,
      },
    });
  } catch (error) {
    console.error("Error fetching management dashboard data:", error);

    // Provide more detailed error information for debugging
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorDetails =
      error instanceof Error && (error as any).details
        ? (error as any).details
        : {};

    return NextResponse.json(
      {
        error: "Failed to fetch dashboard data",
        message: errorMessage,
        details: errorDetails,
      },
      { status: 500 },
    );
  }
}