# Bank of Styx Main Site

The primary Next.js application containing the complete banking platform interface, user dashboard, and all core functionality.

## Key Directories

- **src/** - Source code including pages, components, hooks, and utilities
- **prisma/** - Database schema, migrations, and seeding scripts
- **public/** - Static assets including images, icons, and user uploads
- **scripts/** - Utility scripts for development and maintenance
- **.next/** - Next.js build output and cache

## Configuration Files

- **next.config.js** - Next.js configuration
- **tailwind.config.js** - TailwindCSS styling configuration
- **tsconfig.json** - TypeScript configuration
- **middleware.ts** - Next.js middleware for authentication and routing
- **.env.local** - Local environment variables (not committed)

This is the main user-facing application that provides the complete Bank of Styx banking experience.
