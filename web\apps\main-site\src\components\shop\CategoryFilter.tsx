"use client";

import React from "react";
import { ProductCategory } from "@/services/productService";

interface CategoryFilterProps {
  categories: ProductCategory[];
  selectedCategory: string;
  onChange: (categoryId: string) => void;
}

export const CategoryFilter: React.FC<CategoryFilterProps> = ({
  categories,
  selectedCategory,
  onChange,
}) => {
  return (
    <div className="space-y-2">
      <div
        className={`px-3 py-2 rounded-md cursor-pointer transition-colors ${
          !selectedCategory
            ? "bg-primary text-white font-medium"
            : "hover:bg-secondary hover:text-text-primary"
        }`}
        onClick={() => onChange("")}
      >
        All Products
      </div>

      {categories.map((category) => (
        <div
          key={category.id}
          className={`px-3 py-2 rounded-md cursor-pointer transition-colors ${
            selectedCategory === category.id
              ? "bg-primary text-white font-medium"
              : "hover:bg-secondary hover:text-text-primary"
          }`}
          onClick={() => onChange(category.id)}
        >
          {category.name}
        </div>
      ))}
    </div>
  );
};
