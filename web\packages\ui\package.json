{"name": "@bank-of-styx/ui", "version": "0.1.0", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "sideEffects": false, "license": "MIT", "files": ["dist/**"], "scripts": {"build": "tsup src/index.tsx --format esm,cjs --dts --external react", "dev": "tsup src/index.tsx --format esm,cjs --dts --watch --external react", "lint": "eslint \"src/**/*.ts*\"", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "devDependencies": {"@types/react": "18.2.0", "@types/react-dom": "18.2.0", "eslint": "^8.40.0", "@bank-of-styx/config": "workspace:*", "react": "18.2.0", "tsup": "^6.7.0", "typescript": "^5.0.4"}, "peerDependencies": {"react": "18.2.0"}}