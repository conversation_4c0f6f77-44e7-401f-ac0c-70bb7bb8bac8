# Authentication Components

Components for user authentication, registration, and account verification.

## Components

- **AuthModal.tsx** - Main authentication modal with login and registration forms
- **EmailVerificationForm.tsx** - Email verification form for new account activation
- **PasswordCreationModal.tsx** - Modal for setting passwords during registration
- **GeneratedPasswordDisplay.tsx** - Component for displaying generated passwords securely
- **AuthModal.css** - Styling for authentication components
- **index.ts** - Component exports

These components handle the complete user authentication flow including login, registration, email verification, and password management with Discord OAuth integration.
