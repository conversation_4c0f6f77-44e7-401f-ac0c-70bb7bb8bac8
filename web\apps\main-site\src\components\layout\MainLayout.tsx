"use client";

import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import { ScrollToTop } from "@bank-of-styx/ui";
import { Toaster } from "react-hot-toast";
import Header from "./Header";
import Navigation from "./Navigation";
import Footer from "./Footer";
import Breadcrumbs from "./Breadcrumbs";
import NotificationPanel from "../notifications/NotificationPanel";
import PageViewTracker from "../common/PageViewTracker";

interface MainLayoutProps {
  children: React.ReactNode;
}

export default function MainLayout({ children }: MainLayoutProps) {
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);
  const pathname = usePathname();

  // Close mobile navigation when pathname changes (page navigation)
  useEffect(() => {
    if (isMobileNavOpen) {
      setIsMobileNavOpen(false);
    }
  }, [pathname]);

  // No longer need to calculate footer height as the navigation extends to bottom of viewport

  const toggleMobileNav = () => {
    setIsMobileNavOpen(!isMobileNavOpen);
  };

  const closeMobileNav = () => {
    setIsMobileNavOpen(false);
  };

  return (
    <div className="flex flex-col min-h-screen">
      {/* Toast notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          style: {
            background: "var(--color-secondary)",
            color: "var(--color-text-primary)",
            border: "1px solid var(--color-border-dark)",
            boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
          },
          success: {
            style: {
              background: "var(--color-success)",
              color: "white",
              fontWeight: "500",
            },
            icon: "✓",
          },
          error: {
            style: {
              background: "var(--color-error)",
              color: "white",
              fontWeight: "500",
            },
            icon: "✕",
          },
        }}
      />

      {/* Sticky header */}
      <div className="sticky top-0 z-40">
        <Header toggleMobileNav={toggleMobileNav} />
      </div>

      <div className="flex flex-1 relative">
        {/* Background extension for navigation - extends to bottom of viewport */}
        <div className="hidden md:block w-40 bg-secondary fixed top-12 bottom-0 left-0 z-10"></div>

        {/* Navigation - sticky on desktop, fixed on mobile when open */}
        <Navigation
          isMobileNavOpen={isMobileNavOpen}
          closeMobileNav={closeMobileNav}
        />

        <main className="flex-1">
          <div className="px-2 py-4">
            <Breadcrumbs
              className="mb-2"
              toggleMobileNav={toggleMobileNav}
              excludePaths={["/auth/discord", "/api/auth/discord"]}
            />
            <div className="content-container">{children}</div>
          </div>
        </main>
      </div>

      {/* Footer positioned at the bottom, on top of everything */}
      <Footer />

      {/* Scroll to top button */}
      <ScrollToTop bottomPadding={30} rightPadding={30} scrollThreshold={300} />

      {/* Notification panel */}
      <NotificationPanel />

      {/* Page view tracker */}
      <PageViewTracker />
    </div>
  );
}
