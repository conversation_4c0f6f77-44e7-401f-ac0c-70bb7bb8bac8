"use client";

import { useEffect, useRef } from "react";
import { usePathname } from "next/navigation";
import { useUserState } from "@/contexts/UserStateContext";
import { useAuth } from "@/contexts/AuthContext";

/**
 * Component that tracks page views for authenticated users
 * Increments the pageViews counter in the user state
 */
export default function PageViewTracker() {
  const pathname = usePathname();
  const { incrementPageViews } = useUserState();
  const { isAuthenticated } = useAuth();
  const lastPathRef = useRef(pathname);
  const trackingTimeoutRef = useRef(null);

  useEffect(() => {
    // Only track page views for authenticated users and when the path changes
    if (isAuthenticated && pathname !== lastPathRef.current) {
      // Update the last path
      lastPathRef.current = pathname;

      // Clear any existing timeout
      if (trackingTimeoutRef.current) {
        clearTimeout(trackingTimeoutRef.current);
      }

      // Use a small timeout to avoid tracking during redirects
      trackingTimeoutRef.current = setTimeout(() => {
        incrementPageViews();
        console.log("Page view tracked:", pathname);
        trackingTimeoutRef.current = null;
      }, 1500);
    }

    return () => {
      // Clean up timeout on unmount or when dependencies change
      if (trackingTimeoutRef.current) {
        clearTimeout(trackingTimeoutRef.current);
      }
    };
  }, [pathname, isAuthenticated, incrementPageViews]);

  // This component doesn't render anything
  return null;
}
