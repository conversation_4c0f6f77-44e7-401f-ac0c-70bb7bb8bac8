# News API Routes

Content management system for news articles, categories, and analytics.

## Content Management

- **articles/** - News article CRUD operations (create, read, update, delete)
- **categories/** - News category management and organization
- **public/** - Public-facing news content and article display

## Analytics & Tracking

- **analytics/** - News article analytics, view tracking, and engagement metrics

## Features

The news system provides:

- Rich text article creation and editing
- Category-based organization
- Featured article highlighting
- View tracking and analytics
- Public and administrative interfaces
- Image upload and media management

These endpoints support a comprehensive content management system for platform news and announcements.
