// /api/uploads/v2/route.ts - New unified endpoint
import { NextRequest, NextResponse } from "next/server";
import { uploadConfig } from "@/lib/uploadConfig";
import { saveImageV2 } from "@/services/uploadServiceV2";

export async function POST(req: NextRequest) {
  try {
    // Parse form data
    const formData = await req.formData();
    const file = formData.get("file") as File;
    const type = formData.get("type") as string;
    const entityId = formData.get("entityId") as string;
    const options = formData.get("options")
      ? JSON.parse(formData.get("options") as string)
      : {};

    // Validate required fields
    if (!file || !type) {
      return NextResponse.json(
        { error: "Missing file or type" },
        { status: 400 },
      );
    }

    // Validate upload type
    const validTypes = Object.keys(uploadConfig);
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        {
          error: `Invalid upload type. Allowed types: ${validTypes.join(", ")}`,
        },
        { status: 400 },
      );
    }

    // Get configuration for this upload type
    const config = uploadConfig[type as keyof typeof uploadConfig];

    // Validate file type
    if (!config.allowedTypes.includes(file.type)) {
      return NextResponse.json(
        {
          error: `Invalid file type for ${type}. Allowed types: ${config.allowedTypes.join(
            ", ",
          )}`,
        },
        { status: 400 },
      );
    }

    // Validate file size
    if (file.size > config.maxSize) {
      const maxSizeMB = config.maxSize / (1024 * 1024);
      return NextResponse.json(
        { error: `File size exceeds ${maxSizeMB}MB limit for ${type}` },
        { status: 400 },
      );
    }

    // Process the upload with type-based routing
    const result = await saveImageV2(file, {
      uploadType: type,
      entityId,
      options: { ...config, ...options },
    });

    return NextResponse.json(
      {
        success: true,
        file: result,
        message: `File uploaded successfully for type: ${type}`,
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("Error in v2 upload endpoint:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Upload failed" },
      { status: 500 },
    );
  }
}
