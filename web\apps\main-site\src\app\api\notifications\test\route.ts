import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../lib/prisma";
import { getCurrentUser } from "../../../../lib/auth";
import { connectionStore } from "../../../../lib/connectionStore";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// POST endpoint to create a test notification
export async function POST(req: NextRequest) {
  try {
    // Get current user
    const currentUser = await getCurrentUser(req);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Create a test notification
    const notification = await prisma.notification.create({
      data: {
        userId: currentUser.id,
        category: "test",
        type: "test",
        title: "Test Notification",
        message:
          "This is a test notification to verify the notification system is working correctly. You can manage your notification settings in the Settings > Notifications section.",
        priority: "medium",
        icon: null,
        // No link property - clicking will just mark as read
      },
    });

    // Format notification for response and SSE
    const formattedNotification = {
      ...notification,
      createdAt: notification.createdAt.toISOString(),
      updatedAt: notification.updatedAt.toISOString(),
    };

    // Broadcast notification via SSE if user has active connections
    const connectionCount = connectionStore.getUserConnectionCount(
      currentUser.id,
    );
    if (connectionCount > 0) {
      console.log(
        `[Test Notification] Broadcasting to user ${currentUser.id} via SSE (${connectionCount} connections)`,
      );
      await connectionStore.sendToUser(currentUser.id, {
        type: "notification",
        notification: formattedNotification,
      });
    } else {
      console.log(
        `[Test Notification] User ${currentUser.id} has no active SSE connections, skipping broadcast`,
      );
    }

    return NextResponse.json({
      success: true,
      notification: formattedNotification,
      broadcastedViaSSE: connectionCount > 0,
      activeConnections: connectionCount,
    });
  } catch (error) {
    console.error("Error creating test notification:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
