# Cashier Components

Staff-facing components for the cashier portal enabling transaction processing and member management.

## Layout & Navigation

- **CashierDashboardLayout.tsx** - Main layout component for cashier dashboard
- **CashierQuickActions.tsx** - Quick action buttons for common cashier operations

## Transaction Management

- **TransactionsManager.tsx** - Main transaction management interface
- **TransactionCard.tsx** - Individual transaction display card
- **TransactionHistoryCard.tsx** - Transaction history display component
- **DepositCard.tsx** - Deposit transaction card with approval controls
- **DepositsManager.tsx** - Deposit management and processing interface
- **WithdrawalCard.tsx** - Withdrawal transaction card with processing controls
- **WithdrawalsManager.tsx** - Withdrawal management and processing interface

## Member Management

- **MemberSearch.tsx** - Search functionality for finding members
- **MemberCard.tsx** - Member account summary card
- **MemberProfile.tsx** - Detailed member profile view
- **MemberTransactionHistory.tsx** - Transaction history for specific members
- **MemberTransactionStats.tsx** - Transaction statistics for members

## Reporting & Analytics

- **LedgerDisplay.tsx** - Financial ledger and transaction log display
- **StatisticsDisplay.tsx** - Cashier portal statistics and metrics
- **index.ts** - Component exports

These components provide cashiers with comprehensive tools for processing transactions, managing member accounts, and accessing financial reports and statistics.
