import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";

interface Params {
  params: {
    id: string;
  };
}

export async function GET(request: Request, { params }: Params) {
  const { id } = params;

  try {
    const category = await prisma.newsCategory.findUnique({
      where: { id },
      include: {
        articles: {
          orderBy: { createdAt: "desc" },
        },
        _count: {
          select: { articles: true },
        },
      },
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 },
      );
    }

    const formattedCategory = {
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      articlesCount: category._count.articles,
      articles: category.articles,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
    };

    return NextResponse.json(formattedCategory);
  } catch (error) {
    console.error("Error fetching category:", error);
    return NextResponse.json(
      { error: "Failed to fetch category" },
      { status: 500 },
    );
  }
}

export async function PUT(request: Request, { params }: Params) {
  const { id } = params;

  try {
    const { name, description } = await request.json();

    if (!name || name.trim() === "") {
      return NextResponse.json(
        { error: "Category name is required" },
        { status: 400 },
      );
    }

    // Generate a slug from the name
    const slug = name
      .toLowerCase()
      .replace(/\s+/g, "-")
      .replace(/[^\w-]+/g, "");

    // Check if another category with the same name or slug exists (excluding this one)
    const existingCategory = await prisma.newsCategory.findFirst({
      where: {
        OR: [{ name }, { slug }],
        NOT: {
          id,
        },
      },
    });

    if (existingCategory) {
      return NextResponse.json(
        {
          error: "Another category with this name already exists",
        },
        { status: 409 },
      );
    }

    // Update the category
    const updatedCategory = await prisma.newsCategory.update({
      where: { id },
      data: {
        name,
        slug,
        description,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json(updatedCategory);
  } catch (error) {
    console.error("Error updating category:", error);
    return NextResponse.json(
      { error: "Failed to update category" },
      { status: 500 },
    );
  }
}

export async function DELETE(request: Request, { params }: Params) {
  const { id } = params;

  try {
    // Check if category has associated articles
    const category = await prisma.newsCategory.findUnique({
      where: { id },
      include: {
        _count: {
          select: { articles: true },
        },
      },
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 },
      );
    }

    if (category._count.articles > 0) {
      return NextResponse.json(
        {
          error: `Cannot delete category with ${category._count.articles} articles. Please reassign or delete the articles first.`,
        },
        { status: 400 },
      );
    }

    // Delete the category
    await prisma.newsCategory.delete({
      where: { id },
    });

    return NextResponse.json({ message: "Category deleted successfully" });
  } catch (error) {
    console.error("Error deleting category:", error);
    return NextResponse.json(
      { error: "Failed to delete category" },
      { status: 500 },
    );
  }
}
