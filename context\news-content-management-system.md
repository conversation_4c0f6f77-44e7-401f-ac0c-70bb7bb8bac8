# News and Content Management System

## Overview
A comprehensive content management system with article creation, category organization, rich text editing, dual publication workflows (admin/public), and analytics tracking.

## Core Components

### Article Management System
A dual-interface system supporting both administrative content management and public content consumption:

#### Article Status Workflow
```typescript
// Article lifecycle with publication control
const articleStates = {
  draft: "Article being created/edited, not visible to public",
  published: "Live article visible to public, sets publishedAt timestamp", 
  paused: "Temporarily hidden from public, keeps publishedAt"
};

// Status transition rules
const statusTransitions = {
  draft: ["published"],              // Can only publish from draft
  published: ["paused"],             // Can pause published articles
  paused: ["published"]              // Can republish paused articles
};
```

#### Featured Content System
```typescript
// Featured articles get prominence in public displays
const featuredSystem = {
  designation: "Boolean flag for featured status",
  visibility: "Featured articles appear in special sections",
  filtering: "Public API supports featured-only queries",
  management: "Admins/editors can toggle featured status"
};
```

### Database Schema

#### NewsArticle Model
```sql
NewsArticle {
  id: String (UUID)
  title: String (required, max 255 chars)
  content: String (LongText, rich HTML content)
  excerpt: String (Text, auto-generated from title if not provided)
  image: String? (featured image URL/path)
  slug: String (unique, auto-generated from title)
  
  // Publication Control
  status: String (default: "draft") 
  featured: Boolean (default: false)
  publishedAt: DateTime? (set when first published)
  views: Int (default: 0, incremented on each view)
  
  // Relationships
  authorId: String (required)
  author: User (relationship)
  categoryId: String (required) 
  category: NewsCategory (relationship)
  
  // Timestamps
  createdAt: DateTime
  updatedAt: DateTime
}
```

#### NewsCategory Model  
```sql
NewsCategory {
  id: String (UUID)
  name: String (unique, max 255 chars)
  slug: String (unique, auto-generated from name)
  description: String? (Text, optional category description)
  
  // Relationships
  articles: NewsArticle[] (one-to-many)
  
  // Timestamps
  createdAt: DateTime
  updatedAt: DateTime
}
```

### Role-based Access Control

#### Permission Matrix
```typescript
const newsPermissions = {
  // Public Users (Unauthenticated)
  public: {
    read: "published-only",     // Can only view published articles
    create: false,              // Cannot create articles
    update: false,              // Cannot edit articles
    delete: false,              // Cannot delete articles
    categories: "read-only"     // Can view categories
  },
  
  // Authenticated Users (Regular)
  user: {
    read: "published-only",     // Can only view published articles
    create: false,              // Cannot create articles  
    update: false,              // Cannot edit articles
    delete: false,              // Cannot delete articles
    categories: "read-only"     // Can view categories
  },
  
  // Editor Role
  editor: {
    read: "all",                // Can view all articles (draft/published/paused)
    create: true,               // Can create articles
    update: "own",              // Can edit their own articles
    delete: "own",              // Can delete their own articles  
    categories: "read-only",    // Can view categories
    publish: true,              // Can publish articles
    feature: true               // Can set featured status
  },
  
  // Admin Role
  admin: {
    read: "all",                // Can view all articles
    create: true,               // Can create articles
    update: "all",              // Can edit any article
    delete: "all",              // Can delete any article
    categories: "full",         // Full category management
    publish: true,              // Can publish articles
    feature: true,              // Can set featured status
    analytics: true             // Can view analytics
  }
};
```

#### Authorization Validation
```typescript
// Role verification for content operations
const requireNewsPermissions = async (request: NextRequest) => {
  const user = await getCurrentUser(request);
  
  if (!user) {
    throw new Error("Authentication required");
  }
  
  if (!user.isAdmin && !user.isEditor) {
    throw new Error("Editor or Admin privileges required");
  }
  
  return user;
};
```

## API Architecture

### Administrative Endpoints

#### GET /api/news/articles
**Editor/Admin endpoint** with comprehensive filtering:

```typescript
interface ArticleFilters {
  page?: number;               // Pagination (default: 1) 
  limit?: number;              // Items per page (default: 10)
  status?: "draft" | "published" | "paused";
  featured?: boolean;          // Filter by featured status
  category?: string;           // Filter by category slug
  search?: string;             // Search title, excerpt, content
  sortBy?: string;             // Sort field (default: "createdAt")
  order?: "asc" | "desc";      // Sort direction (default: "desc")
}

// Response includes full article details + relationships
interface ArticleResponse {
  data: Article[];             // Full article objects with author/category
  meta: PaginationMeta;        // Total count, pages, navigation
}
```

#### POST /api/news/articles
**Content creation** with automatic slug generation:

```typescript
const createArticle = async (data: {
  title: string;               // Required
  content: string;             // Required, rich HTML
  excerpt?: string;            // Optional, defaults to title
  categoryId: string;          // Required, must exist
  image?: string;              // Optional, featured image
  status?: "draft" | "published"; // Default: "draft"
  featured?: boolean;          // Default: false
}) => {
  // Auto-generates unique slug from title
  // Sets publishedAt if status = "published"
  // Associates with authenticated user as author
};
```

#### PUT /api/news/articles/[id]
**Content updates** with slug regeneration and publication control:

```typescript
const updateArticle = async (id: string, updates: {
  title?: string;              // Regenerates slug if changed
  content?: string;            // Rich HTML content
  excerpt?: string;            // Article summary
  categoryId?: string;         // Change category association
  image?: string;              // Update featured image
  status?: "draft" | "published" | "paused";
  featured?: boolean;          // Toggle featured status
}) => {
  // Only sets publishedAt when first published (draft → published)
  // Preserves publishedAt when pausing/republishing
  // Updates slug if title changes (with collision avoidance)
};
```

### Public Endpoints

#### GET /api/news/public
**Public-facing article feed** with published content only:

```typescript
const getPublicArticles = async (filters: {
  page?: number;
  limit?: number;
  category?: string;           // Filter by category slug
  search?: string;             // Search published content
  featured?: boolean;          // Show only featured articles
  sortBy?: string;             // Default: "publishedAt"
  order?: "asc" | "desc";      // Default: "desc"
}) => {
  // Automatically filters to status: "published"
  // Returns limited fields (no sensitive admin data)
  // Includes view count and public author info
};
```

#### GET /api/news/public/[slug]
**Individual article access** with view tracking:

```typescript
const getPublicArticle = async (slug: string) => {
  // Finds article by slug
  // Increments view count automatically
  // Returns full content with author/category info
  // Only accessible if status = "published"
};
```

### Category Management

#### GET /api/news/categories
**Category listing** with article counts:

```typescript
const getCategories = async () => {
  return prisma.newsCategory.findMany({
    include: {
      _count: { select: { articles: true } }  // Article count per category
    },
    orderBy: { name: "asc" }
  });
};
```

#### POST /api/news/categories
**Category creation** with auto-slug generation:

```typescript
const createCategory = async (data: {
  name: string;                // Required, must be unique
  description?: string;        // Optional category description
}) => {
  // Auto-generates slug from name
  // Validates name/slug uniqueness
  // Normalizes slug format (lowercase, hyphenated)
};
```

## Rich Text Editor Integration

### ReactQuill Implementation
The system uses ReactQuill for rich text editing with custom configuration:

```typescript
// Dynamic import to prevent SSR issues
const ReactQuill = dynamic(() => import("react-quill"), {
  ssr: false,
  loading: () => <EditorSkeleton />
});

// Toolbar configuration optimized for news content
const editorModules = {
  toolbar: [
    [{ header: [1, 2, 3, false] }],      // Header levels
    ["bold", "italic", "underline", "strike"],
    [{ list: "ordered" }, { list: "bullet" }],
    [{ indent: "-1" }, { indent: "+1" }],
    ["link"],                             // Link support
    ["clean"]                             // Remove formatting
  ]
};

// Supported formats for content
const editorFormats = [
  "header", "bold", "italic", "underline", "strike",
  "list", "bullet", "indent", "link"
];
```

### Editor Features
- **SSR Compatibility**: Dynamic loading prevents server-side rendering issues
- **Theme Integration**: Custom CSS variables for dark theme consistency
- **Mobile Responsive**: Toolbar adjustments for mobile devices
- **Error Handling**: Validation and error state display
- **Hydration Safety**: Mounted state prevents hydration mismatches

### Content Storage
```typescript
// Rich HTML content stored directly in database
const contentStorage = {
  format: "HTML",                       // ReactQuill outputs HTML
  storage: "LongText field",           // Database stores full HTML
  validation: "Client-side only",      // No server-side HTML sanitization
  display: "Direct HTML rendering"     // Frontend renders stored HTML
};
```

## Analytics and Monitoring

### Analytics Dashboard
The system provides comprehensive content analytics:

```typescript
// Analytics endpoint: GET /api/news/analytics
interface NewsAnalytics {
  counts: {
    total: number;                     // All articles
    published: number;                 // Live articles
    draft: number;                     // Unpublished articles
    paused: number;                    // Temporarily hidden
    featured: number;                  // Featured articles
  };
  topViewed: Article[];                // Most popular articles (top 5)
  byCategory: CategoryStats[];         // Article count per category
  recent: Article[];                   // Recently updated articles (last 5)
}
```

### View Tracking
```typescript
// Automatic view counting on article access
const trackArticleView = async (articleId: string) => {
  await prisma.newsArticle.update({
    where: { id: articleId },
    data: { views: { increment: 1 } }
  });
};

// View tracking triggers on:
// - Individual article GET requests
// - Public article access via slug
// - Both admin and public interfaces
```

### Performance Metrics
- **Content Performance**: Most viewed articles tracking
- **Category Distribution**: Articles per category analysis  
- **Publication Status**: Draft vs published ratio
- **Recent Activity**: Latest content updates

## Slug System

### Automatic Slug Generation
```typescript
// Slug generation from article titles
const generateSlug = (title: string) => {
  const baseSlug = title
    .toLowerCase()                     // Convert to lowercase
    .replace(/[^\w\s-]/g, "")         // Remove special characters
    .replace(/\s+/g, "-")             // Replace spaces with hyphens
    .replace(/-+/g, "-");             // Collapse multiple hyphens
    
  return baseSlug;
};

// Collision avoidance for duplicate slugs
const ensureUniqueSlug = async (baseSlug: string, excludeId?: string) => {
  const existing = await prisma.newsArticle.findFirst({
    where: { 
      slug: baseSlug,
      ...(excludeId && { id: { not: excludeId } })
    }
  });
  
  // Append timestamp suffix if collision detected
  return existing 
    ? `${baseSlug}-${Date.now().toString().slice(-6)}`
    : baseSlug;
};
```

### Slug Usage Patterns
- **URL Structure**: `/news/article/[slug]` for SEO-friendly URLs
- **Uniqueness**: Database constraint ensures no duplicate slugs
- **Persistence**: Slugs preserved across title changes when possible
- **API Access**: Public endpoints use slug-based lookups

## Frontend Service Integration

### TypeScript Service Layer
```typescript
// Comprehensive type definitions
export interface Article {
  id: string;
  title: string;
  content: string;                     // Rich HTML content
  excerpt: string;
  image: string;
  slug: string;
  status: "draft" | "published" | "paused";
  featured: boolean;
  views: number;
  publishedAt: string | null;
  author: AuthorInfo;
  category: CategoryInfo;
}

// Service functions with proper error handling
export const newsService = {
  // Article management
  getArticles: (filters?: ArticleFilters) => Promise<ArticleResponse>,
  getArticleById: (id: string) => Promise<Article>,
  createArticle: (data: CreateArticleData) => Promise<Article>,
  updateArticle: (id: string, data: UpdateArticleData) => Promise<Article>,
  deleteArticle: (id: string) => Promise<void>,
  
  // Status management
  toggleArticleStatus: (id: string) => Promise<Article>,
  toggleArticleFeatured: (id: string) => Promise<Article>,
  
  // Category management
  getCategories: () => Promise<Category[]>,
  createCategory: (data: CategoryData) => Promise<Category>
};
```

### Image Upload Integration
```typescript
// Integration with V2 upload system for featured images
const uploadFeaturedImage = async (file: File) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', 'news');      // Uses 'news' upload type
  
  const result = await uploadServiceV2.uploadFile(formData);
  return result.url;                    // Returns public URL for article.image
};
```

## Content Workflow Patterns

### Editorial Workflow
```typescript
// Typical article lifecycle
const editorialWorkflow = {
  creation: {
    status: "draft",                   // Always start as draft
    author: "currentUser",             // Auto-assigned to creator
    slug: "auto-generated",            // From title
    publishedAt: null                  // Not set until published
  },
  
  editing: {
    updates: "real-time",              // Immediate saves
    preview: "available",              // Can preview before publish
    slug: "preserved-when-possible"    // Only changes if title changes
  },
  
  publication: {
    trigger: 'status: "published"',    // Sets publishedAt timestamp
    visibility: "immediate",           // Appears in public API
    search: "indexed",                 // Searchable in public endpoints
    views: "tracked"                   // View counting begins
  },
  
  management: {
    pause: 'status: "paused"',         // Temporarily hide
    republish: 're-enable',            // Restore to published
    featured: "toggleable",            // Can feature/unfeature anytime
    analytics: "continuous"            // View tracking throughout
  }
};
```

### Publication States
```typescript
// State transitions with business logic
const publicationStates = {
  draft: {
    visibility: "admin/editor only",
    publishedAt: null,
    searchable: false,
    viewTracking: false
  },
  
  published: {
    visibility: "public",
    publishedAt: "set on first publish",
    searchable: true,
    viewTracking: true
  },
  
  paused: {
    visibility: "admin/editor only", 
    publishedAt: "preserved",          // Keeps original publish date
    searchable: false,
    viewTracking: false
  }
};
```

## Security Features

### Content Validation
```typescript
// Input validation for article creation/updates
const validateArticleData = (data: ArticleData) => {
  // Required field validation
  if (!data.title?.trim()) throw new Error("Title is required");
  if (!data.content?.trim()) throw new Error("Content is required");
  if (!data.categoryId) throw new Error("Category is required");
  
  // Length validation
  if (data.title.length > 255) throw new Error("Title too long");
  if (data.excerpt && data.excerpt.length > 1000) throw new Error("Excerpt too long");
  
  // Category existence validation
  const categoryExists = await prisma.newsCategory.findUnique({
    where: { id: data.categoryId }
  });
  if (!categoryExists) throw new Error("Invalid category");
};
```

### Access Control Enforcement
```typescript
// Endpoint protection patterns
const protectNewsEndpoint = async (request: NextRequest) => {
  const user = await getCurrentUser(request);
  
  // Require authentication
  if (!user) {
    return NextResponse.json({ error: "Authentication required" }, { status: 401 });
  }
  
  // Require editor/admin role
  if (!user.isEditor && !user.isAdmin) {
    return NextResponse.json({ error: "Editor privileges required" }, { status: 403 });
  }
  
  return user;
};
```

### XSS Prevention
```typescript
// Content sanitization considerations
const contentSecurity = {
  storage: "Raw HTML stored (no server sanitization)",
  display: "Client-side rendering of stored HTML",
  editor: "ReactQuill provides limited format control",
  risk: "Trusted editors only - admin/editor role required",
  mitigation: "Role-based access prevents untrusted content"
};
```

## Performance Optimizations

### Database Queries
```typescript
// Optimized queries with selective field loading
const optimizedQueries = {
  // Public API - limited fields for performance
  publicArticles: {
    select: {
      id: true, title: true, excerpt: true, image: true,
      slug: true, publishedAt: true, views: true, featured: true,
      author: { select: { displayName: true, avatar: true } },
      category: { select: { name: true, slug: true } }
    }
  },
  
  // Admin API - full data including sensitive fields
  adminArticles: "full include with author/category relationships",
  
  // Analytics - aggregated counts only
  analytics: "count queries and top-N selections"
};
```

### Caching Strategy
```typescript
// Cacheable endpoints and strategies
const cachingStrategy = {
  categories: "High cache (rarely change)",
  publicArticles: "Medium cache (10-15 minutes)",
  analytics: "Low cache (real-time preferred)",
  individualArticles: "High cache with view tracking"
};
```

## Integration Features

### Featured Content Integration
- **Homepage**: Featured articles display prominently
- **Category Pages**: Featured filtering available
- **RSS Feeds**: Featured articles in special feeds
- **Admin Dashboard**: Featured status management

### Search Integration
- **Multi-field Search**: Title, excerpt, and content searching
- **Category Filtering**: Search within specific categories
- **Status Filtering**: Admin can search drafts, public searches published only
- **Performance**: Database-level text search using LIKE queries

### User Integration
- **Author Association**: Articles linked to user accounts
- **Role-based Access**: Editor/admin permissions required
- **Activity Tracking**: User actions tracked for auditing

## Important Files
- `src/app/api/news/articles/route.ts` - Main article CRUD operations
- `src/app/api/news/public/route.ts` - Public article access
- `src/app/api/news/categories/route.ts` - Category management
- `src/app/api/news/analytics/route.ts` - Content analytics
- `src/services/newsService.ts` - Frontend service layer
- `src/components/news/NewsEditor.tsx` - Rich text editor component
- `prisma/schema.prisma` - NewsArticle and NewsCategory models