import React, { useState } from "react";
import { Button } from "@bank-of-styx/ui";
import { VolunteerPayment } from "@/hooks/useVolunteerPayments";
import { formatDate, formatCurrency } from "@/lib/utils";

interface PaymentListProps {
  payments: VolunteerPayment[];
  isLoading: boolean;
  onProcessPayment: (paymentId: string) => void;
  onBulkProcess: (paymentIds: string[]) => void;
}

export default function PaymentList({
  payments,
  isLoading,
  onProcessPayment,
  onBulkProcess,
}: PaymentListProps) {
  const [selectedPayments, setSelectedPayments] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Handle select all checkbox
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedPayments([]);
    } else {
      setSelectedPayments(payments.map((payment) => payment.id));
    }
    setSelectAll(!selectAll);
  };

  // Handle individual payment selection
  const handleSelectPayment = (paymentId: string) => {
    if (selectedPayments.includes(paymentId)) {
      setSelectedPayments(selectedPayments.filter((id) => id !== paymentId));
      setSelectAll(false);
    } else {
      setSelectedPayments([...selectedPayments, paymentId]);
      if (selectedPayments.length + 1 === payments.length) {
        setSelectAll(true);
      }
    }
  };

  // Handle bulk processing
  const handleBulkProcess = () => {
    if (selectedPayments.length > 0) {
      onBulkProcess(selectedPayments);
    }
  };

  // Calculate total amount for selected payments
  const calculateTotalSelected = () => {
    return payments
      .filter((payment) => selectedPayments.includes(payment.id))
      .reduce((total, payment) => total + payment.paymentAmount, 0);
  };

  if (isLoading) {
    return (
      <div className="bg-secondary-light rounded-lg shadow-md p-4 border border-gray-600">
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          <p className="ml-2 text-white">Loading payments...</p>
        </div>
      </div>
    );
  }

  if (payments.length === 0) {
    return (
      <div className="bg-secondary-light rounded-lg shadow-md p-4 border border-gray-600">
        <p className="text-white text-center py-8">
          No payments found matching the current filters.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-secondary-light rounded-lg shadow-md border border-gray-600">
      {/* Bulk Actions */}
      {selectedPayments.length > 0 && (
        <div className="p-4 border-b border-gray-600 bg-secondary flex flex-wrap items-center justify-between">
          <div className="text-white">
            <span className="font-semibold">{selectedPayments.length}</span>{" "}
            payments selected ({formatCurrency(calculateTotalSelected())})
          </div>
          <Button
            variant="primary"
            onClick={handleBulkProcess}
            className="mt-2 sm:mt-0"
          >
            Process Selected Payments
          </Button>
        </div>
      )}

      {/* Table Header */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-secondary-dark">
            <tr>
              <th className="p-3 text-left">
                <input
                  type="checkbox"
                  checked={selectAll}
                  onChange={handleSelectAll}
                  className="rounded bg-secondary-dark border-gray-600"
                />
              </th>
              <th className="p-3 text-left text-white">Volunteer</th>
              <th className="p-3 text-left text-white">Event / Category</th>
              <th className="p-3 text-left text-white">Shift Date</th>
              <th className="p-3 text-left text-white">Hours</th>
              <th className="p-3 text-right text-white">Amount</th>
              <th className="p-3 text-center text-white">Status</th>
              <th className="p-3 text-right text-white">Actions</th>
            </tr>
          </thead>
          <tbody>
            {payments.map((payment) => (
              <tr
                key={payment.id}
                className="border-t border-gray-600 hover:bg-secondary-dark/50"
              >
                <td className="p-3">
                  <input
                    type="checkbox"
                    checked={selectedPayments.includes(payment.id)}
                    onChange={() => handleSelectPayment(payment.id)}
                    className="rounded bg-secondary-dark border-gray-600"
                  />
                </td>
                <td className="p-3">
                  <div className="flex items-center">
                    {payment.user.avatar && (
                      <img
                        src={payment.user.avatar}
                        alt={payment.user.displayName}
                        className="w-8 h-8 rounded-full mr-2"
                      />
                    )}
                    <div>
                      <div className="text-white">
                        {payment.user.displayName}
                      </div>
                      <div className="text-gray-400 text-sm">
                        @{payment.user.username}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="p-3">
                  <div className="text-white">
                    {payment.assignment.shift.event.name}
                  </div>
                  <div className="text-gray-400 text-sm">
                    {payment.assignment.shift.category.name}
                  </div>
                </td>
                <td className="p-3">
                  <div className="text-white">
                    {formatDate(payment.assignment.shift.startTime)}
                  </div>
                  <div className="text-gray-400 text-sm">
                    {payment.assignment.shift.name}
                  </div>
                </td>
                <td className="p-3 text-white">{payment.hoursWorked}</td>
                <td className="p-3 text-white text-right">
                  {formatCurrency(payment.paymentAmount)}
                </td>
                <td className="p-3">
                  <span
                    className={`inline-block px-2 py-1 rounded text-xs ${
                      payment.paymentStatus === "pending"
                        ? "bg-yellow-800 text-yellow-200"
                        : payment.paymentStatus === "processing"
                        ? "bg-blue-800 text-blue-200"
                        : payment.paymentStatus === "paid"
                        ? "bg-green-800 text-green-200"
                        : "bg-red-800 text-red-200"
                    }`}
                  >
                    {payment.paymentStatus.charAt(0).toUpperCase() +
                      payment.paymentStatus.slice(1)}
                  </span>
                </td>
                <td className="p-3 text-right">
                  {payment.paymentStatus === "pending" && (
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => onProcessPayment(payment.id)}
                    >
                      Process
                    </Button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
