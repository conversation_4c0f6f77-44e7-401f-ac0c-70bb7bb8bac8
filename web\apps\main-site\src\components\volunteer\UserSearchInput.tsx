"use client";

import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import fetchClient from "@/lib/fetchClient";

interface ApiUser {
  id: string;
  name?: string;
  displayName?: string;
  email: string;
  image?: string | null;
  avatar?: string | null;
}

// Normalized user interface with consistent property names
interface User {
  id: string;
  name: string;
  email: string;
  image: string | null;
}

interface UserSearchInputProps {
  selectedUserId: string | null;
  onUserSelect: (user: User | null) => void;
  label?: string;
  placeholder?: string;
  error?: boolean;
  errorMessage?: string;
  className?: string;
  disabled?: boolean;
}

export const UserSearchInput: React.FC<UserSearchInputProps> = ({
  selectedUserId,
  onUserSelect,
  label = "Lead Manager",
  placeholder = "Search for a user...",
  error = false,
  errorMessage = "",
  className = "",
  disabled = false,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const searchRef = useRef<HTMLDivElement>(null);

  // Fetch selected user details when selectedUserId changes
  useEffect(() => {
    const fetchSelectedUser = async () => {
      if (!selectedUserId) {
        setSelectedUser(null);
        return;
      }

      try {
        const user = await fetchClient.get<ApiUser>(
          `/api/users/${selectedUserId}`,
        );

        // Normalize the user data to ensure consistent property names
        const normalizedUser: User = {
          id: user.id,
          name: user.name || user.displayName || "",
          email: user.email,
          image: user.image || user.avatar || null,
        };

        setSelectedUser(normalizedUser);
      } catch (error) {
        console.error("Error fetching selected user:", error);
        setSelectedUser(null);
      }
    };

    fetchSelectedUser();
  }, [selectedUserId]);

  // Handle search input change
  const handleSearchChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);

    if (value.trim().length < 2) {
      setSearchResults([]);
      return;
    }

    setIsLoading(true);
    try {
      const data = await fetchClient.get<{ users: ApiUser[] }>(
        `/api/users/search`,
        {
          params: {
            q: value,
            volunteer: "true",
          },
        },
      );

      // Map the response to ensure consistent property names
      const normalizedUsers: User[] = (data.users || []).map((user) => ({
        id: user.id,
        name: user.name || user.displayName || "",
        email: user.email,
        image: user.image || user.avatar || null,
      }));

      setSearchResults(normalizedUsers);
    } catch (error) {
      console.error("Error searching users:", error);
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle user selection
  const handleUserSelect = (user: User) => {
    // Ensure user data is properly normalized before setting
    const normalizedUser: User = {
      id: user.id,
      name: user.name || "",
      email: user.email,
      image: user.image || null,
    };

    setSelectedUser(normalizedUser);
    setSearchTerm("");
    setShowResults(false);
    onUserSelect(normalizedUser);
  };

  // Handle clear selection
  const handleClearSelection = () => {
    setSelectedUser(null);
    setSearchTerm("");
    onUserSelect(null);
  };

  // Handle click outside to close results
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchRef.current &&
        !searchRef.current.contains(event.target as Node)
      ) {
        setShowResults(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className={`relative ${className}`} ref={searchRef}>
      {label && (
        <label className="block text-sm font-medium mb-1 text-white">
          {label}
        </label>
      )}

      {selectedUser ? (
        // Display selected user
        <div className="flex items-center justify-between p-2 bg-secondary border border-gray-600 rounded-md">
          <div className="flex items-center">
            {selectedUser.image ? (
              <div className="w-8 h-8 rounded-full overflow-hidden mr-2">
                <Image
                  src={selectedUser.image}
                  alt={selectedUser.name || "User"}
                  width={32}
                  height={32}
                  className="object-cover"
                />
              </div>
            ) : (
              <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center mr-2">
                <span className="text-white text-sm">
                  {selectedUser.name && selectedUser.name.length > 0
                    ? selectedUser.name.charAt(0).toUpperCase()
                    : "?"}
                </span>
              </div>
            )}
            <div>
              <div className="text-white font-medium">{selectedUser.name}</div>
              <div className="text-gray-400 text-sm">{selectedUser.email}</div>
            </div>
          </div>
          <button
            type="button"
            onClick={handleClearSelection}
            className="text-gray-400 hover:text-white"
            disabled={disabled}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
      ) : (
        // Search input
        <div className="relative">
          <input
            type="text"
            placeholder={placeholder}
            className={`w-full px-4 py-2 bg-secondary border ${
              error ? "border-accent" : "border-gray-600"
            } rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary`}
            value={searchTerm}
            onChange={handleSearchChange}
            onFocus={() => setShowResults(true)}
            disabled={disabled}
          />
          {isLoading && (
            <div className="absolute right-3 top-2">
              <svg
                className="animate-spin h-5 w-5 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            </div>
          )}
        </div>
      )}

      {/* Search results dropdown */}
      {showResults && searchResults.length > 0 && (
        <div className="absolute z-10 mt-1 w-full bg-secondary-dark border border-gray-600 rounded-md shadow-lg max-h-60 overflow-auto">
          {searchResults.map((user) => (
            <div
              key={user.id}
              className="flex items-center p-2 hover:bg-secondary cursor-pointer"
              onClick={() => handleUserSelect(user)}
            >
              {user.image ? (
                <div className="w-8 h-8 rounded-full overflow-hidden mr-2">
                  <Image
                    src={user.image}
                    alt={user.name || "User"}
                    width={32}
                    height={32}
                    className="object-cover"
                  />
                </div>
              ) : (
                <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center mr-2">
                  <span className="text-white text-sm">
                    {user.name && user.name.length > 0
                      ? user.name.charAt(0).toUpperCase()
                      : "?"}
                  </span>
                </div>
              )}
              <div>
                <div className="text-white font-medium">{user.name}</div>
                <div className="text-gray-400 text-sm">{user.email}</div>
              </div>
            </div>
          ))}
        </div>
      )}

      {error && errorMessage && (
        <p className="mt-1 text-sm text-accent">{errorMessage}</p>
      )}
    </div>
  );
};
