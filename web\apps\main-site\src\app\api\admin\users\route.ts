import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export async function GET(request: Request) {
  try {
    // Check if user is authenticated and has admin role
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const isAdmin = await userHasRole(request, "admin");
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Admin privileges required" },
        { status: 403 },
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    let limit = parseInt(url.searchParams.get("limit") || "10");
    const search = url.searchParams.get("search") || "";
    const role = url.searchParams.get("role");
    const status = url.searchParams.get("status");

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build where conditions
    const whereConditions: any = {};

    // Add search condition with manual case-insensitive filtering
    if (search) {
      const lowercaseSearch = search.toLowerCase();
      whereConditions.OR = [
        { username: { contains: lowercaseSearch } },
        { displayName: { contains: lowercaseSearch } },
        { email: { contains: lowercaseSearch } },
      ];
      // We'll fetch extra results for manual filtering
      limit *= 2;
    }

    // Add role filter
    if (role) {
      switch (role) {
        case "admin":
          whereConditions.isAdmin = true;
          break;
        case "editor":
          whereConditions.isEditor = true;
          break;
        case "banker":
          whereConditions.isBanker = true;
          break;
        case "chatModerator":
          whereConditions.isChatModerator = true;
          break;
        case "volunteerCoordinator":
          whereConditions.isVolunteerCoordinator = true;
          break;
        case "leadManager":
          whereConditions.isLeadManager = true;
          break;
        case "salesManager":
          whereConditions.isSalesManager = true;
          break;
        case "landSteward":
          whereConditions.isLandSteward = true;
          break;
      }
    }

    // Add status filter
    if (status) {
      whereConditions.status = status;
    }

    // Get users with pagination
    const users = await prisma.user.findMany({
      where: whereConditions,
      select: {
        id: true,
        username: true,
        displayName: true,
        avatar: true,
        email: true,
        isEmailVerified: true,
        status: true,
        isAdmin: true,
        isEditor: true,
        isBanker: true,
        isChatModerator: true,
        isVolunteerCoordinator: true,
        isLeadManager: true,
        isSalesManager: true,
        isLandSteward: true,
        createdAt: true,
      },
      skip,
      take: limit,
      orderBy: {
        createdAt: "desc",
      },
    });

    // Apply manual case-insensitive filtering when search is present
    let filteredUsers = users;
    if (search) {
      const lowercaseSearch = search.toLowerCase();
      filteredUsers = users.filter(
        (user) =>
          user.username.toLowerCase().includes(lowercaseSearch) ||
          (user.displayName &&
            user.displayName.toLowerCase().includes(lowercaseSearch)) ||
          (user.email && user.email.toLowerCase().includes(lowercaseSearch)),
      );
      // Take only the requested number of results after filtering
      filteredUsers = filteredUsers.slice(0, limit / 2);
    }

    // Transform users to match the AdminUser interface
    const transformedUsers = filteredUsers.map((user) => ({
      id: user.id,
      username: user.username,
      displayName: user.displayName,
      avatar: user.avatar,
      email: user.email,
      isEmailVerified: user.isEmailVerified,
      status: user.status,
      roles: {
        admin: user.isAdmin,
        editor: user.isEditor,
        banker: user.isBanker,
        chatModerator: user.isChatModerator,
        volunteerCoordinator: user.isVolunteerCoordinator,
        leadManager: user.isLeadManager,
        salesManager: user.isSalesManager,
        landSteward: user.isLandSteward,
      },
      createdAt: user.createdAt.toISOString(),
    }));

    // Recalculate total based on filtered results when searching
    const total = search
      ? filteredUsers.length
      : await prisma.user.count({
          where: whereConditions,
        });

    return NextResponse.json({
      users: transformedUsers,
      total,
      pages: Math.ceil(total / (limit / (search ? 2 : 1))),
    });
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { error: "Failed to fetch users" },
      { status: 500 },
    );
  }
}
