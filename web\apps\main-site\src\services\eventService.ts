/**
 * Event API Service
 * Provides functions for interacting with the Event API endpoints
 */
import fetchClient from "@/lib/fetchClient";

// Types
export interface Event {
  id: string;
  name: string;
  description: string | null;
  shortDescription: string | null;
  startDate: string;
  endDate: string;
  location: string;
  address: string | null;
  virtualLink: string | null;
  isVirtual: boolean;
  image: string | null;
  status: string;
  capacity: number | null;
  capacityUsed: number | null;
  categoryId: string;
  createdById: string;
  createdAt: string;
  updatedAt: string;
  category?: {
    id: string;
    name: string;
  };
}

export interface EventsResponse {
  events: Event[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

// Get all published events
export const getEvents = async (filters?: {
  page?: number;
  limit?: number;
  categoryId?: string;
  search?: string;
  upcoming?: boolean;
  featured?: boolean;
}): Promise<Event[]> => {
  const params = new URLSearchParams();

  if (filters?.page) params.append("page", filters.page.toString());
  if (filters?.limit) params.append("limit", filters.limit.toString());
  if (filters?.categoryId) params.append("categoryId", filters.categoryId);
  if (filters?.search) params.append("search", filters.search);
  if (filters?.upcoming) params.append("upcoming", "true");
  if (filters?.featured) params.append("featured", "true");

  const response = await fetchClient.get<EventsResponse>(
    `/api/events?${params.toString()}`,
  );
  return response.events;
};

// Get a specific event by ID
export const getEvent = async (id: string): Promise<Event> => {
  return fetchClient.get<Event>(`/api/events/${id}`);
};
