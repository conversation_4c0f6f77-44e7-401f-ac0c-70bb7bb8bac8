"use client";

import React from "react";

export interface HeroProps {
  title: string;
  subtitle?: string;
  backgroundImage?: string;
  overlayColor?: string;
  textColor?: string;
  height?: string;
  className?: string;
  children?: React.ReactNode;
}

export const Hero: React.FC<HeroProps> = ({
  title,
  subtitle,
  backgroundImage,
  overlayColor = "rgba(0, 0, 0, 0.5)",
  textColor = "white",
  height = "500px",
  className = "",
  children,
}) => {
  return (
    <div
      className={`relative flex items-center justify-center ${className}`}
      style={{ height }}
    >
      {backgroundImage && (
        <div
          className="absolute inset-0 bg-cover bg-center z-0"
          style={{ backgroundImage: `url(${backgroundImage})` }}
        />
      )}
      <div
        className="absolute inset-0 z-10"
        style={{ backgroundColor: overlayColor }}
      />
      <div className="relative z-20 text-center px-4 sm:px-6 lg:px-8 max-w-5xl">
        <h1
          className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight"
          style={{ color: textColor }}
        >
          {title}
        </h1>
        {subtitle && (
          <p
            className="mt-6 text-xl sm:text-2xl max-w-3xl mx-auto"
            style={{ color: textColor }}
          >
            {subtitle}
          </p>
        )}
        {children && <div className="mt-8">{children}</div>}
      </div>
    </div>
  );
};
