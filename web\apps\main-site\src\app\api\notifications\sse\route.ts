/**
 * Server-Sent Events (SSE) API Endpoint
 *
 * This endpoint establishes a persistent connection for real-time notifications
 * using Server-Sent Events (SSE). It authenticates the user via JWT token and
 * maintains the connection in the connection store.
 */

import { NextRequest } from "next/server";
import { verifyToken } from "../../../../lib/auth";
import { connectionStore } from "../../../../lib/connectionStore";
// Import the heartbeat service and initialize it
import { safeInitializeHeartbeatService } from "../../../../lib/heartbeatService";
import crypto from "crypto";



// Initialize the heartbeat service when this module is loaded
// This ensures the service is started before any SSE connections are established
if (process.env.NODE_ENV === "development") {
  // Use the safe initialization function to prevent duplicate services
  safeInitializeHeartbeatService();
}

// Force dynamic rendering for this route
export const dynamic = "force-dynamic";

// Helper function for timestamped logging
//function logWithTimestamp(message: string, ...args: any[]) {
  //const timestamp = new Date().toISOString().replace("T", " ").replace("Z", "");
//  console.log(`[${timestamp}] ${message}`, ...args);
//}

export async function GET(req: NextRequest) {
  try {
  
    // Get token from query parameter or authorization header
    let token: string | null = null;

    // First try to get token from query parameter (for EventSource which doesn't support custom headers)
    const url = new URL(req.url);
    token = url.searchParams.get("auth_token");
  
    // If no token in query, try authorization header
    if (!token) {
      const authHeader = req.headers.get("authorization");
      if (authHeader && authHeader.startsWith("Bearer ")) {
        token = authHeader.split(" ")[1];
      }
    }

    // Check if we have a token
    if (!token) {
      return new Response("Unauthorized", { status: 401 });
    }

    // Verify token
    const user = await verifyToken(token);
    if (!user || !user.id) {
      return new Response("Invalid token", { status: 401 });
    }

  
    // Set up SSE headers
    const responseHeaders = new Headers({
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache, no-transform",
      Connection: "keep-alive",
      "X-Accel-Buffering": "no", // Disable buffering for Nginx
    });

    // Create a transform stream
    const stream = new TransformStream();
    const writer = stream.writable.getWriter();

    // Generate a unique connection ID
    const connectionId = crypto.randomUUID();
  
    // Store the connection
    await connectionStore.addConnection(connectionId, user.id, writer);

    // Note: The heartbeat service is now initialized at server startup
    // We don't need to start it here anymore

    // Send initial connection message
    const encoder = new TextEncoder();
    const initialMessage = {
      type: "connected",
      message: "SSE connection established",
      connectionId,
      timestamp: new Date().toISOString(),
    };
  
    writer.write(encoder.encode(`data: ${JSON.stringify(initialMessage)}\n\n`));

    // Send multiple comment lines immediately after the initial message to ensure the connection stays open
    // This helps with some proxies and browsers that might close the connection if no data is received initially
    writer.write(encoder.encode(": keepalive comment\n\n"));

    // Send a few more comments with a slight delay to help establish the connection
    setTimeout(() => {
      try {
        writer.write(encoder.encode(": connection keepalive 1\n\n"));
      } catch (error) {
        console.error(
          `[${new Date()
            .toISOString()
            .replace("T", " ")
            .replace("Z", "")}] [SSE API] Error sending keepalive 1:`,
          error,
        );
      }
    }, 500);

    setTimeout(() => {
      try {
        writer.write(encoder.encode(": connection keepalive 2\n\n"));
      } catch (error) {
        console.error(
          `[${new Date()
            .toISOString()
            .replace("T", " ")
            .replace("Z", "")}] [SSE API] Error sending keepalive 2:`,
          error,
        );
      }
    }, 1000);

    // Set up connection cleanup on close
    req.signal.addEventListener("abort", () => {
      connectionStore.removeConnection(connectionId);
    });

    return new Response(stream.readable, { headers: responseHeaders });
  } catch (error) {
    const timestamp = new Date()
      .toISOString()
      .replace("T", " ")
      .replace("Z", "");
    console.error(`[${timestamp}] [SSE] Error establishing connection:`, error);
    return new Response("Internal Server Error", { status: 500 });
  }
}
