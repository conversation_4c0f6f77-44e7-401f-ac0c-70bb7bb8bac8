/**
 * Test script for Discord avatar download and storage
 *
 * This is a manual test script that can be run to verify the Discord avatar functionality.
 * It's not meant to be run automatically as part of a test suite.
 *
 * To use:
 * 1. Set the DISCORD_ID and AVATAR_HASH variables to valid values
 * 2. Set the USER_ID to a valid user ID in your database
 * 3. Run the script with ts-node or similar
 */

import { downloadAndSaveDiscordAvatar } from "../lib/discordAvatarUtils";

// Test configuration - replace with valid values
const DISCORD_ID = "261755488738213898"; // Example Discord ID
const AVATAR_HASH = "26572cbd0fd01f8f94ae119ca9f18388"; // Example avatar hash
const USER_ID = "user_id_from_your_database"; // Replace with a valid user ID

async function runTest() {
  console.log("Starting Discord avatar download test...");

  try {
    console.log(`Downloading avatar for Discord ID: ${DISCORD_ID}`);
    console.log(`Avatar hash: ${AVATAR_HASH}`);
    console.log(`User ID: ${USER_ID}`);

    const avatarUrl = await downloadAndSaveDiscordAvatar(
      DISCORD_ID,
      AVATAR_HASH,
      USER_ID,
    );

    console.log("Avatar download successful!");
    console.log(`Saved avatar URL: ${avatarUrl}`);

    return {
      success: true,
      avatarUrl,
    };
  } catch (error) {
    console.error("Test failed:", error);
    return {
      success: false,
      error,
    };
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  runTest()
    .then((result) => {
      console.log("Test completed:", result);
      process.exit(result.success ? 0 : 1);
    })
    .catch((error) => {
      console.error("Unhandled error:", error);
      process.exit(1);
    });
}

export { runTest };
