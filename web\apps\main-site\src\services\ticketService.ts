/**
 * Ticket Service
 * Provides functions for interacting with the Support Ticket API endpoints
 */
import fetchClient from "@/lib/fetchClient";

// Using the centralized fetch client for all API calls

// Types
export interface SupportTicket {
  id: string;
  subject: string;
  message: string;
  status: "open" | "in_progress" | "resolved" | "closed";
  priority: "low" | "medium" | "high" | "urgent";
  category: string;
  name: string | null;
  email: string;
  phone: string | null;
  userId: string | null;
  assignedToId: string | null;
  assignedAt: string | null;
  resolvedById: string | null;
  resolvedAt: string | null;
  resolution: string | null;
  createdAt: string;
  updatedAt: string;
  user?: {
    id: string;
    username: string;
    displayName: string;
    avatar: string;
  };
  assignedTo?: {
    id: string;
    username: string;
    displayName: string;
    avatar: string;
  };
  resolvedBy?: {
    id: string;
    username: string;
    displayName: string;
    avatar: string;
  };
  notes?: TicketNote[];
}

export interface TicketNote {
  id: string;
  content: string;
  isInternal: boolean;
  ticketId: string;
  authorId: string;
  createdAt: string;
  updatedAt: string;
  author?: {
    id: string;
    username: string;
    displayName: string;
    avatar: string;
  };
}

export interface TicketListResponse {
  tickets: SupportTicket[];
  pagination: {
    total: number;
    pages: number;
    page: number;
    limit: number;
  };
}

// Get all tickets (with pagination and filtering)
export const getTickets = async (params?: {
  page?: number;
  limit?: number;
  status?: string;
  priority?: string;
  category?: string;
  search?: string;
  assignedToMe?: boolean;
}): Promise<TicketListResponse> => {
  const queryParams: Record<string, string> = {};

  if (params?.page) queryParams.page = params.page.toString();
  if (params?.limit) queryParams.limit = params.limit.toString();
  if (params?.status) queryParams.status = params.status;
  if (params?.priority) queryParams.priority = params.priority;
  if (params?.category) queryParams.category = params.category;
  if (params?.search) queryParams.search = params.search;
  if (params?.assignedToMe) queryParams.assignedToMe = "true";

  return fetchClient.get<TicketListResponse>("/api/support/tickets", {
    params: queryParams,
  });
};

// Get a single ticket by ID
export const getTicketById = async (id: string): Promise<SupportTicket> => {
  return fetchClient.get<SupportTicket>(`/api/support/tickets/${id}`);
};

// Create a new ticket
export const createTicket = async (data: {
  subject: string;
  message: string;
  name?: string;
  email: string;
  phone?: string;
  category?: string;
}): Promise<{ success: boolean; ticket: Partial<SupportTicket> }> => {
  return fetchClient.post<{ success: boolean; ticket: Partial<SupportTicket> }>(
    "/api/support/tickets",
    data,
  );
};

// Update a ticket
export const updateTicket = async (
  id: string,
  data: {
    status?: "open" | "in_progress" | "resolved" | "closed";
    priority?: "low" | "medium" | "high" | "urgent";
    category?: string;
    assignedToId?: string;
    resolution?: string;
  },
): Promise<SupportTicket> => {
  return fetchClient.put<SupportTicket>(`/api/support/tickets/${id}`, data);
};

// Get notes for a ticket
export const getTicketNotes = async (id: string): Promise<TicketNote[]> => {
  return fetchClient.get<TicketNote[]>(`/api/support/tickets/${id}/notes`);
};

// Add a note to a ticket
export const addTicketNote = async (
  id: string,
  data: {
    content: string;
    isInternal?: boolean;
  },
): Promise<TicketNote> => {
  return fetchClient.post<TicketNote>(`/api/support/tickets/${id}/notes`, data);
};

// Update the contact form to create a ticket
export const submitContactForm = async (data: {
  name?: string;
  email: string;
  phone?: string;
  message: string;
}): Promise<{ success: boolean; ticket?: Partial<SupportTicket> }> => {
  return fetchClient.post<{
    success: boolean;
    ticket?: Partial<SupportTicket>;
  }>("/api/support/tickets", {
    subject: "Contact Form Submission",
    message: data.message,
    name: data.name,
    email: data.email,
    phone: data.phone,
    category: "general",
  });
};
