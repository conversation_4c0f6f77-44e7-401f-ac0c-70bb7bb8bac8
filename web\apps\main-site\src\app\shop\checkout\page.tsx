"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useCart } from "@/hooks/useCart";
import { useAuth } from "@/contexts/AuthContext";
import { <PERSON><PERSON>, Card, Spinner } from "@bank-of-styx/ui";
import { Elements } from "@stripe/react-stripe-js";
import { getStripeClient } from "@/lib/stripe";
import { CheckoutForm } from "@/components/shop/CheckoutForm";
import Link from "next/link";
import fetchClient from "@/lib/fetchClient";

export default function CheckoutPage() {
  const { user, isAuthenticated, openAuthModal } = useAuth();
  const { data: cartData, isLoading: isLoadingCart } = useCart();
  const [clientSecret, setClientSecret] = useState("");
  const [orderId, setOrderId] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    // Only create a payment intent when the cart is loaded and not empty
    if (
      isAuthenticated &&
      cartData?.cart &&
      cartData.cart.items.length > 0 &&
      !isProcessing &&
      !clientSecret
    ) {
      createPaymentIntent();
    }
  }, [isAuthenticated, cartData]);

  const createPaymentIntent = async () => {
    setIsProcessing(true);
    setError(null);

    try {
      const data = await fetchClient.post<{
        clientSecret: string;
        orderId: string;
      }>("/api/checkout/create-payment-intent", {});

      setClientSecret(data.clientSecret);
      setOrderId(data.orderId);
    } catch (err: any) {
      setError(err.message || "An unexpected error occurred");
      console.error("Error creating payment intent:", err);
    } finally {
      setIsProcessing(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Sign In Required</h2>
          <p className="mb-6">Please sign in to proceed with checkout.</p>
          <Button variant="primary" onClick={openAuthModal}>
            Sign In
          </Button>
        </Card>
      </div>
    );
  }

  if (isLoadingCart || isProcessing) {
    return (
      <div className="container mx-auto px-4 py-8 flex justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  const cart = cartData?.cart;
  const isEmpty = !cart?.items.length;

  if (isEmpty) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Your cart is empty</h2>
          <p className="mb-6">
            Add some products to your cart before checkout.
          </p>
          <Link href="/shop">
            <Button variant="primary">Shop Now</Button>
          </Link>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Error</h2>
          <p className="text-accent mb-6">{error}</p>
          <Button variant="primary" onClick={createPaymentIntent}>
            Try Again
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Checkout</h1>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Order Summary */}
        <div className="lg:col-span-1">
          <Card title="Order Summary">
            <div className="p-4">
              <div className="space-y-4 mb-4">
                {cart?.items.map((item) => (
                  <div key={item.id} className="flex justify-between">
                    <span>
                      {item.quantity} x {item.product.name}
                    </span>
                    <span>
                      ${(item.product.price * item.quantity).toFixed(2)}
                    </span>
                  </div>
                ))}
              </div>
              <div className="border-t border-border-subtle my-4"></div>
              <div className="flex justify-between font-bold text-lg">
                <span>Total</span>
                <span>${cart?.subtotal.toFixed(2)}</span>
              </div>
            </div>
          </Card>
        </div>

        {/* Payment Form */}
        <div className="lg:col-span-2">
          <Card title="Payment Information">
            {clientSecret ? (
              <Elements
                stripe={getStripeClient()}
                options={{ clientSecret, appearance: { theme: "night" } }}
              >
                <CheckoutForm orderId={orderId} />
              </Elements>
            ) : (
              <div className="p-8 flex justify-center">
                <Spinner />
              </div>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
}
