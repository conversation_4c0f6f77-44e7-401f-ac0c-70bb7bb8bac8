import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getCurrentUser, userHasRole } from "@/lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// GET /api/volunteer/events/[id]/categories - Get categories for a specific event
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id: eventId } = params;

    // Check if user is authenticated and has volunteer coordinator role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");
    if (!hasCoordinatorRole) {
      return NextResponse.json(
        { error: "Unauthorized - Coordinator role required" },
        { status: 403 },
      );
    }

    // Check if event exists
    const event = await prisma.event.findUnique({
      where: { id: eventId },
    });

    if (!event) {
      return NextResponse.json({ error: "Event not found" }, { status: 404 });
    }

    // Get categories for this event
    const categories = await prisma.volunteerCategory.findMany({
      where: { eventId },
      include: {
        leadManager: {
          select: {
            id: true,
            displayName: true,
            email: true,
            avatar: true,
          },
        },
        shifts: {
          select: {
            id: true,
            _count: {
              select: {
                assignments: true,
              },
            },
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    // Calculate statistics for each category
    const categoriesWithStats = await Promise.all(
      categories.map(async (category) => {
        // Count total shifts
        const totalShifts = category.shifts ? category.shifts.length : 0;

        // Count total assignments (signups)
        const totalSignups = category.shifts
          ? category.shifts.reduce(
              (sum: number, shift: any) => sum + shift._count.assignments,
              0,
            )
          : 0;

        // Count completed shifts (this is a placeholder - you'll need to implement the actual logic)
        const completedShifts = 0; // Placeholder

        return {
          id: category.id,
          name: category.name,
          description: category.description,
          payRate: category.payRate,
          eventId: category.eventId,
          createdAt: category.createdAt,
          updatedAt: category.updatedAt,
          leadManager: category.leadManager || null,
          leadManagerId: category.leadManagerId,
          stats: {
            totalShifts,
            totalSignups,
            completedShifts,
          },
        };
      }),
    );

    return NextResponse.json(categoriesWithStats);
  } catch (error) {
    console.error("Error fetching categories:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch categories",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}

// POST /api/volunteer/events/[id]/categories - Create a new category for a specific event
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id: eventId } = params;
    const { name, description, payRate, leadManagerId } = await req.json();

    // Check if user is authenticated and has volunteer coordinator role
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 },
      );
    }

    const hasCoordinatorRole = await userHasRole(req, "volunteerCoordinator");
    if (!hasCoordinatorRole) {
      return NextResponse.json(
        { error: "Unauthorized - Coordinator role required" },
        { status: 403 },
      );
    }

    // Validate required fields
    if (!name || name.trim() === "") {
      return NextResponse.json(
        { error: "Category name is required" },
        { status: 400 },
      );
    }

    // Check if event exists
    const event = await prisma.event.findUnique({
      where: { id: eventId },
    });

    if (!event) {
      return NextResponse.json({ error: "Event not found" }, { status: 404 });
    }

    // Check if category with the same name already exists for this event
    // Using a more compatible approach for case-insensitive search
    const existingCategory = await prisma.volunteerCategory.findFirst({
      where: {
        eventId,
        name: {
          contains: name,
        },
      },
    });

    // Manual case-insensitive check since Prisma might not support mode: "insensitive"
    const categoryExists =
      existingCategory &&
      existingCategory.name.toLowerCase() === name.toLowerCase();

    if (categoryExists) {
      return NextResponse.json(
        { error: "A category with this name already exists for this event" },
        { status: 409 },
      );
    }

    // Create the new category
    const newCategory = await prisma.volunteerCategory.create({
      data: {
        name,
        description,
        payRate: payRate ? parseFloat(payRate) : null,
        eventId,
        leadManagerId: leadManagerId || null,
      },
    });

    // If a lead manager was assigned, update the user's role and category ID
    if (leadManagerId) {
      await prisma.user.update({
        where: { id: leadManagerId },
        data: {
          isLeadManager: true,
          leadManagerCategoryId: newCategory.id, // Set the category ID reference
        },
      });
    }

    return NextResponse.json(newCategory, { status: 201 });
  } catch (error) {
    console.error("Error creating category:", error);
    return NextResponse.json(
      {
        error: "Failed to create category",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
