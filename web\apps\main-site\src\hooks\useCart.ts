import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import fetchClient from "@/lib/fetchClient";

// Query keys
export const cartQueryKeys = {
  cart: ["cart"],
  cartItem: (id: string) => ["cartItem", id],
};

// Types
export interface CartItem {
  id: string;
  quantity: number;
  isCodeRedemption: boolean;
  redemptionCodeId?: string;
  redemptionCode?: {
    id: string;
    code: string;
  };
  product: {
    id: string;
    name: string;
    price: number;
    image: string | null;
    inventory: number | null;
    category: {
      id: string;
      name: string;
    };
    event?: {
      id: string;
      name: string;
      startDate: string;
      endDate: string;
    };
  };
  ticketHold?: {
    id: string;
    expiresAt: string;
    tickets: Array<{
      id: string;
      status: string;
      seatInfo?: any;
    }>;
  };
  eventCapacityHold?: {
    id: string;
    expiresAt: string;
    quantity: number;
  };
  ticketCount?: number;
  holdExpiresAt?: string;
}

export interface Cart {
  id: string;
  userId: string;
  items: CartItem[];
  subtotal: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Hook to fetch user's cart
 */
export function useCart() {
  return useQuery({
    queryKey: cartQueryKeys.cart,
    queryFn: async () => {
      return fetchClient.get<{ cart: Cart }>("/api/cart");
    },
  });
}

/**
 * Hook to add item to cart
 */
export function useAddToCart() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      productId,
      quantity,
    }: {
      productId: string;
      quantity: number;
    }) => {
      return fetchClient.post<{ item: CartItem }>("/api/cart/items", {
        productId,
        quantity,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: cartQueryKeys.cart });
    },
  });
}

/**
 * Hook to update cart item
 */
export function useUpdateCartItem() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      itemId,
      quantity,
    }: {
      itemId: string;
      quantity: number;
    }) => {
      return fetchClient.put<{ item: CartItem }>(`/api/cart/items/${itemId}`, {
        quantity,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: cartQueryKeys.cart });
    },
  });
}

/**
 * Hook to remove item from cart
 */
export function useRemoveCartItem() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (itemId: string) => {
      return fetchClient.delete<{ success: boolean }>(
        `/api/cart/items/${itemId}`,
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: cartQueryKeys.cart });
    },
  });
}

/**
 * Hook to clear cart
 */
export function useClearCart() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      return fetchClient.delete<{ success: boolean }>("/api/cart/clear");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: cartQueryKeys.cart });
    },
  });
}

/**
 * Hook to refresh/extend cart holds
 */
export function useRefreshHolds() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      return fetchClient.post<{
        refreshed: number;
        ticketsRefreshed: number;
        expiresAt: string;
      }>("/api/cart/refresh-holds", {});
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: cartQueryKeys.cart });
    },
  });
}
