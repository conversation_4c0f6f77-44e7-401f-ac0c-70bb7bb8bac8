import { PrismaClient } from "@prisma/client";

// Query Buffer for time-window analysis
class QueryBuffer {
  private buffer: Array<{
    timestamp: number;
    query: string;
    duration: number;
    params: any[];
    memorySnapshot: {
      rss: number;
      heapTotal: number;
      heapUsed: number;
    };
  }> = [];
  private readonly windowSize = 60000; // 1 minute in milliseconds
  private queryPatterns: Map<
    string,
    { count: number; totalTime: number; lastSeen: number }
  > = new Map();

  addQuery(query: string, duration: number, params: any[]) {
    const now = Date.now();

    // Add new query
    this.buffer.push({
      timestamp: now,
      query,
      duration,
      params: Array.isArray(params) ? params : [], // Ensure params is always an array
      memorySnapshot: {
        rss: process.memoryUsage().rss,
        heapTotal: process.memoryUsage().heapTotal,
        heapUsed: process.memoryUsage().heapUsed,
      },
    });

    // Update query patterns
    const normalizedQuery = this.normalizeQuery(query);
    const pattern = this.queryPatterns.get(normalizedQuery) || {
      count: 0,
      totalTime: 0,
      lastSeen: 0,
    };
    this.queryPatterns.set(normalizedQuery, {
      count: pattern.count + 1,
      totalTime: pattern.totalTime + duration,
      lastSeen: now,
    });

    // Purge old entries
    this.purgeOldEntries(now);
  }

  private normalizeQuery(query: string): string {
    // Remove specific values but keep the query structure
    return query
      .replace(/\d+/g, "N")
      .replace(/'[^']*'/g, "?")
      .replace(/`[^`]+`/g, "T")
      .trim();
  }

  private purgeOldEntries(now: number) {
    const cutoff = now - this.windowSize;
    this.buffer = this.buffer.filter((entry) => entry.timestamp > cutoff);

    // Cleanup old patterns
    for (const [query, stats] of this.queryPatterns.entries()) {
      if (stats.lastSeen < cutoff) {
        this.queryPatterns.delete(query);
      }
    }
  }

  getStats() {
    if (this.buffer.length === 0) return null;

    const now = Date.now();
    this.purgeOldEntries(now);

    // Calculate various statistics
    const durations = this.buffer.map((q) => q.duration).sort((a, b) => a - b);
    const totalQueries = durations.length;
    const qps = totalQueries / (this.windowSize / 1000);

    // Memory trends
    const memoryTrend = this.buffer.map((q) => q.memorySnapshot.heapUsed);
    const memoryGrowthRate =
      memoryTrend.length > 1
        ? (memoryTrend[memoryTrend.length - 1] - memoryTrend[0]) /
          (this.windowSize / 1000)
        : 0;

    // Percentile calculation helper
    const getPercentile = (arr: number[], p: number) => {
      const pos = (arr.length - 1) * p;
      const base = Math.floor(pos);
      const rest = pos - base;
      if (arr[base + 1] !== undefined) {
        return arr[base] + rest * (arr[base + 1] - arr[base]);
      } else {
        return arr[base];
      }
    };

    // Find hotspots (frequently repeated queries)
    const hotspots = Array.from(this.queryPatterns.entries())
      .map(([query, stats]) => ({
        query,
        count: stats.count,
        avgTime: stats.totalTime / stats.count,
        frequency: stats.count / (this.windowSize / 1000),
      }))
      .filter((h) => h.frequency > 1) // More than once per second
      .sort((a, b) => b.frequency - a.frequency);

    return {
      window: {
        startTime: now - this.windowSize,
        endTime: now,
        durationSeconds: this.windowSize / 1000,
      },
      performance: {
        queriesPerSecond: qps,
        p50: getPercentile(durations, 0.5),
        p95: getPercentile(durations, 0.95),
        p99: getPercentile(durations, 0.99),
        min: durations[0],
        max: durations[durations.length - 1],
      },
      memory: {
        currentHeapUsed: memoryTrend[memoryTrend.length - 1],
        growthRatePerSecond: memoryGrowthRate,
        isGrowthConcerning: memoryGrowthRate > 1024 * 1024, // Alert if growing more than 1MB/s
      },
      patterns: {
        totalUniquePatterns: this.queryPatterns.size,
        hotspots: hotspots.slice(0, 5), // Top 5 hotspots
      },
    };
  }
}

// Create a singleton to prevent multiple instances during hot reloading
const globalForPrisma = global as unknown as {
  prisma: PrismaClient;
  queryBuffer?: QueryBuffer;
};

// Create a new Prisma client instance with logging configuration
const createPrismaClient = () => {
  const client = new PrismaClient({
    log:
      process.env.NODE_ENV === "development"
        ? [
            {
              emit: "event",
              level: "query",
            },
            "error",
            "warn",
          ]
        : ["error"],
  });

  // Track query statistics
  const queryStats = {
    totalQueries: 0,
    totalTime: 0,
    slowestQuery: { query: "", time: 0, params: [] as any[] },
    fastestQuery: {
      query: "",
      time: Number.MAX_SAFE_INTEGER,
      params: [] as any[],
    },
    startTime: Date.now(),
  };

  // Define ANSI color codes
  const colors = {
    blue: "\x1b[34m",
    cyan: "\x1b[36m",
    green: "\x1b[32m",
    magenta: "\x1b[35m",
    red: "\x1b[31m",
    yellow: "\x1b[33m",
    reset: "\x1b[0m",
  };

  // Add middleware for timing queries in development mode
  if (process.env.NODE_ENV === "development") {
    client.$on("query", (e) => {
      const formattedTime = new Date().toLocaleTimeString("en-US", {
        hour12: false,
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        fractionalSecondDigits: 3,
      });

      // Save query info with properly typed params
      const queryParams = Array.isArray(e.params) ? e.params : [];

      // Update query statistics
      queryStats.totalQueries++;
      queryStats.totalTime += e.duration;

      if (e.duration > queryStats.slowestQuery.time) {
        queryStats.slowestQuery = {
          query: e.query,
          time: e.duration,
          params: queryParams,
        };
      }

      if (e.duration < queryStats.fastestQuery.time) {
        queryStats.fastestQuery = {
          query: e.query,
          time: e.duration,
          params: queryParams,
        };
      }

      // Get the raw query
      let query = e.query;

      // Remove any database name prefixes
      query = query.replace(/`[^`]+`\./g, "");

      // Define regex patterns for different SQL elements
      const patterns = {
        keywords:
          /\b(SELECT|FROM|WHERE|ORDER BY|LIMIT|OFFSET|INSERT|UPDATE|DELETE|JOIN|LEFT|RIGHT|INNER|OUTER|ON|AS|AND|OR|NOT|NULL|IS|IN|BETWEEN|LIKE|CREATE|TABLE|PRIMARY KEY|UNIQUE|INDEX|DEFAULT|CHARACTER SET|COLLATE|ASC|DESC)\b/gi,
        dataTypes:
          /\b(VARCHAR|LONGTEXT|TEXT|DATETIME|BOOLEAN|INTEGER|INT|FLOAT|DOUBLE|DECIMAL|DATE|TIME|TIMESTAMP)\b/gi,
        identifiers: /`([^`]+)`/g,
        strings: /'([^']*)'/g,
        numbers: /\b(\d+)\b/g,
        params: /\?/g,
      };

      // Apply syntax highlighting with simple ANSI colors
      const coloredQuery = query
        // Keywords
        .replace(
          patterns.keywords,
          (match) => `${colors.blue}${match}${colors.reset}`,
        )
        // Data types
        .replace(
          patterns.dataTypes,
          (match) => `${colors.cyan}${match}${colors.reset}`,
        )
        // Identifiers
        .replace(
          patterns.identifiers,
          (match) => `${colors.red}${match}${colors.reset}`,
        )
        // Strings
        .replace(
          patterns.strings,
          (match) => `${colors.yellow}${match}${colors.reset}`,
        )
        // Numbers
        .replace(
          patterns.numbers,
          (match) => `${colors.green}${match}${colors.reset}`,
        )
        // Parameters
        .replace(
          patterns.params,
          (match) => `${colors.yellow}${match}${colors.reset}`,
        );

      // Format the query for better readability
      const queryLines = coloredQuery.split("\n");
      const formattedQuery = queryLines.map((line) => `  ${line}`).join("\n");

      // Format parameters
      const params = JSON.stringify(e.params);

      // Calculate performance metrics
      const avgQueryTime = queryStats.totalTime / queryStats.totalQueries;
      const uptimeSeconds = (Date.now() - queryStats.startTime) / 1000;
      const uptime = uptimeSeconds.toFixed(1);
      const queriesPerSecond = (
        queryStats.totalQueries / Number(uptime)
      ).toFixed(2);

      // Performance indicator (plain text)
      let performanceIndicator = "";
      if (e.duration < avgQueryTime * 0.5) {
        performanceIndicator = "⚡ Fast";
      } else if (e.duration > avgQueryTime * 2) {
        performanceIndicator = "🐢 Slow";
      } else {
        performanceIndicator = "⏱️ Normal";
      }

      // Memory usage
      const memoryUsage = process.memoryUsage();
      const formattedMemory = {
        rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
        heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
      };

      // Log the query with colored SQL but plain text for metrics
      console.log(
        `-- [${formattedTime}] Prisma Query (${e.duration}ms) ${performanceIndicator}`,
      );
      console.log(formattedQuery);
      console.log(`-- Params: ${params}`);

      // Performance metrics (plain text)
      console.log(
        `-- Performance: ${e.duration.toFixed(
          2,
        )}ms (avg: ${avgQueryTime.toFixed(2)}ms)`,
      );
      console.log(
        `-- Stats: ${queryStats.totalQueries} queries in ${uptime}s (${queriesPerSecond} q/s)`,
      );
      console.log(
        `-- Memory: RSS: ${formattedMemory.rss} | Heap: ${formattedMemory.heapUsed}/${formattedMemory.heapTotal}`,
      );
      console.log(""); // Empty line for better separation

      // Every 100 queries, show a summary (plain text)
      if (queryStats.totalQueries % 100 === 0) {
        console.log("=== PRISMA QUERY STATS SUMMARY ===");
        console.log(`Total Queries: ${queryStats.totalQueries}`);
        console.log(`Average Time: ${avgQueryTime.toFixed(2)}ms`);
        console.log(
          `Slowest Query: ${queryStats.slowestQuery.time.toFixed(2)}ms`,
        );
        console.log(
          `Fastest Query: ${queryStats.fastestQuery.time.toFixed(2)}ms`,
        );
        console.log(`Queries/Second: ${queriesPerSecond}`);
        console.log("================================");
        console.log("");
      }

      // Add query to buffer for enhanced statistics
      if (!globalForPrisma.queryBuffer) {
        globalForPrisma.queryBuffer = new QueryBuffer();
      }
      globalForPrisma.queryBuffer.addQuery(
        e.query,
        e.duration,
        Array.isArray(e.params) ? e.params : [],
      );
    });
  }

  return client;
};

// Use existing instance or create a new one
export const prisma = globalForPrisma.prisma || createPrismaClient();

// Save the instance in development mode
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

export default prisma;
