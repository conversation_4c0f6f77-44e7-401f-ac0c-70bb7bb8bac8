"use client";

import React, { useState, useEffect } from "react";
import fetchClient from "@/lib/fetchClient";

interface DashboardStats {
  totalEvents: number;
  activeCategories: number;
  upcomingShifts: number;
  registeredVolunteers: number;
}

export const DashboardStats: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalEvents: 0,
    activeCategories: 0,
    upcomingShifts: 0,
    registeredVolunteers: 0,
  });
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setIsLoading(true);
        const data = await fetchClient.get<DashboardStats>(
          "/api/volunteer/dashboard/stats",
        );
        setStats(data);
        setError(null);
      } catch (err) {
        setError("Error loading statistics");
        console.error("Error fetching dashboard stats:", err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, []);

  // Stat card component to avoid repetition
  const StatCard: React.FC<{
    title: string;
    value: number;
    icon: string;
  }> = ({ title, value, icon }) => (
    <div className="bg-secondary rounded-lg shadow-md p-4 border border-gray-600">
      <div className="flex items-center">
        <div className="mr-4">
          <svg
            className="w-8 h-8 text-primary"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d={icon}
            />
          </svg>
        </div>
        <div>
          <p className="text-sm text-gray-400">{title}</p>
          {isLoading ? (
            <div className="animate-pulse h-6 bg-gray-600 rounded w-16 mt-1"></div>
          ) : (
            <p className="text-xl font-bold text-white">{value}</p>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <StatCard
        title="Total Events"
        value={stats.totalEvents}
        icon="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
      />
      <StatCard
        title="Active Categories"
        value={stats.activeCategories}
        icon="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
      />
      <StatCard
        title="Upcoming Shifts"
        value={stats.upcomingShifts}
        icon="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
      />
      <StatCard
        title="Registered Volunteers"
        value={stats.registeredVolunteers}
        icon="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
      />
    </div>
  );
};
