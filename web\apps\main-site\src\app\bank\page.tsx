"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { Hero, ContentCard } from "@bank-of-styx/ui";
import { useAuth } from "../../contexts/AuthContext";

export default function BankPage() {
  const { isAuthenticated, openAuthModal } = useAuth();
  const router = useRouter();

  // Redirect to dashboard if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.push("/bank/dashboard");
    }
  }, [isAuthenticated, router]);

  return (
    <div>
      <Hero
        title="Bank of Styx"
        subtitle="Secure financial services for the Pirate Rinfair community"
        backgroundImage="/images/bank-background.jpg"
        height="250px "
      >
        <div className="flex justify-center">
          <button
            onClick={openAuthModal}
            className="bg-primary hover:bg-primary-dark text-white font-bold py-2 px-6 rounded-full"
          >
            Login / Register
          </button>
        </div>
      </Hero>

      <div className="container mx-auto px-2 py-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-12">
          <div className="bg-secondary-light p-6 rounded-lg shadow-md text-white">
            <div className="text-primary mb-4">
              <svg
                className="w-12 h-12"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <h3 className="text-xl font-bold mb-2">Secure Storage</h3>
            <p className="text-gray-400">
              Keep your treasure safe in our heavily guarded vaults. Insurance
              available for all deposits.
            </p>
          </div>

          <div className="bg-secondary-light p-6 rounded-lg shadow-md text-white">
            <div className="text-primary mb-4">
              <svg
                className="w-12 h-12"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <h3 className="text-xl font-bold mb-2">Currency Exchange</h3>
            <p className="text-gray-400">
              Exchange Sterling and other currencies at competitive rates.
              Special rates for regular customers.
            </p>
          </div>

          <div className="bg-secondary-light p-6 rounded-lg shadow-md text-white">
            <div className="text-primary mb-4">
              <svg
                className="w-12 h-12"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold mb-2">Loans & Financing</h3>
            <p className="text-gray-400">
              Get financing for your next adventure or business venture.
              Flexible repayment terms available.
            </p>
          </div>
        </div>

        <div className="bg-secondary p-8 rounded-lg mb-12 text-white">
          <h2 className="text-2xl font-bold mb-4">
            Banking Dashboard Coming Soon
          </h2>
          <p className="mb-4">
            Our full banking services will be available online soon. Check back
            for updates on our progress.
          </p>
          <p>
            In the meantime, visit any of our physical locations for all your
            banking needs.
          </p>
        </div>
      </div>
    </div>
  );
}
