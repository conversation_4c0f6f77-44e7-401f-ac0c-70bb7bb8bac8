import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";

interface Params {
  params: {
    slug: string;
  };
}

export async function GET(request: Request, { params }: Params) {
  const { slug } = params;

  try {
    // Find the article by slug, ensuring it's published
    const article = await prisma.newsArticle.findFirst({
      where: {
        slug,
        status: "published",
      },
      include: {
        author: {
          select: {
            displayName: true,
            avatar: true,
          },
        },
        category: {
          select: {
            name: true,
            slug: true,
          },
        },
      },
    });

    if (!article) {
      return NextResponse.json({ error: "Article not found" }, { status: 404 });
    }

    // Increment view count
    await prisma.newsArticle.update({
      where: { id: article.id },
      data: { views: { increment: 1 } },
    });

    // Get related articles in the same category
    const relatedArticles = await prisma.newsArticle.findMany({
      where: {
        categoryId: article.categoryId,
        status: "published",
        id: { not: article.id }, // Exclude current article
      },
      take: 3,
      orderBy: { publishedAt: "desc" },
      select: {
        id: true,
        title: true,
        excerpt: true,
        slug: true,
        image: true,
        publishedAt: true,
      },
    });

    return NextResponse.json({
      ...article,
      relatedArticles,
    });
  } catch (error) {
    console.error("Error fetching article by slug:", error);
    return NextResponse.json(
      { error: "Failed to fetch article" },
      { status: 500 },
    );
  }
}
