import { test, expect } from '@playwright/test';

test.describe('Concurrent User Load Test', () => {
  const testUsers = [
    { email: '<EMAIL>', password: 'TestPassword123!' },
    { email: '<EMAIL>', password: 'TestPassword123!' },
    { email: '<EMAIL>', password: 'TestPassword123!' },
    { email: '<EMAIL>', password: 'TestPassword123!' },
    { email: '<EMAIL>', password: 'TestPassword123!' }
  ];

  test('5 concurrent users can browse and interact', async ({ browser }) => {
    const contexts = await Promise.all(
      testUsers.map(() => browser.newContext())
    );

    const pages = await Promise.all(
      contexts.map(context => context.newPage())
    );

    try {
      // Simulate 5 users loading the homepage simultaneously
      await Promise.all(
        pages.map(async (page, index) => {
          await page.goto('/');
          await expect(page).toHaveTitle(/Bank of Styx/);
          console.log(`User ${index + 1} loaded homepage`);
        })
      );

      // Simulate users navigating to different sections
      await Promise.all([
        pages[0].goto('/news'),
        pages[1].goto('/events'), 
        pages[2].goto('/shop'),
        pages[3].goto('/volunteer'),
        pages[4].goto('/bank')
      ]);

      // Wait for all pages to load
      await Promise.all(
        pages.map((page, index) => {
          console.log(`User ${index + 1} navigated to their section`);
          return page.waitForLoadState('domcontentloaded');
        })
      );

      // Test concurrent login attempts
      await Promise.all(
        pages.map(async (page, index) => {
          await page.goto('/');
          await page.click('button[data-testid="login-button"]');
          await page.fill('input[name="email"]', testUsers[index].email);
          await page.fill('input[name="password"]', testUsers[index].password);
          await page.click('button[type="submit"]');
          console.log(`User ${index + 1} attempted login`);
        })
      );

    } finally {
      await Promise.all(contexts.map(context => context.close()));
    }
  });

  test('Banking system under concurrent load', async ({ browser }) => {
    const contexts = await Promise.all(
      Array(3).fill(null).map(() => browser.newContext())
    );

    const pages = await Promise.all(
      contexts.map(context => context.newPage())
    );

    try {
      // All users navigate to bank
      await Promise.all(
        pages.map(async (page, index) => {
          await page.goto('/bank');
          console.log(`Banking user ${index + 1} loaded bank page`);
        })
      );

      // Simulate concurrent balance checks and transaction views
      await Promise.all(
        pages.map(async (page, index) => {
          await page.click('[data-testid="account-summary"]');
          await page.waitForSelector('[data-testid="balance-display"]');
          console.log(`Banking user ${index + 1} checked balance`);
        })
      );

    } finally {
      await Promise.all(contexts.map(context => context.close()));
    }
  });

  test('Shopping cart concurrency test', async ({ browser }) => {
    const contexts = await Promise.all(
      Array(3).fill(null).map(() => browser.newContext())
    );

    const pages = await Promise.all(
      contexts.map(context => context.newPage())
    );

    try {
      // All users navigate to shop
      await Promise.all(
        pages.map(async (page, index) => {
          await page.goto('/shop');
          await page.waitForSelector('[data-testid="product-grid"]');
          console.log(`Shopping user ${index + 1} loaded shop`);
        })
      );

      // Simulate concurrent cart actions
      await Promise.all(
        pages.map(async (page, index) => {
          const products = await page.$$('[data-testid="product-card"]');
          if (products.length > index) {
            await products[index].click();
            await page.click('[data-testid="add-to-cart"]');
            console.log(`Shopping user ${index + 1} added product to cart`);
          }
        })
      );

    } finally {
      await Promise.all(contexts.map(context => context.close()));
    }
  });
});