"use client";

import React from "react";
import { Product } from "@/services/productService";
import { Card, Button } from "@bank-of-styx/ui";
import { Badge } from "@/components/shared";
import Image from "next/image";
import Link from "next/link";

interface ProductCardProps {
  product: Product;
}

export const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const isOutOfStock = product.inventory !== null && product.inventory <= 0;

  return (
    <Card className="h-full flex flex-col">
      <div className="relative aspect-square bg-secondary-light overflow-hidden">
        {product.image ? (
          <Image
            src={product.image}
            alt={product.name}
            fill
            className="object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <span className="text-text-muted">No image</span>
          </div>
        )}

        {/* Event badge if applicable */}
        {product.event && (
          <div className="absolute top-2 left-2">
            <Badge variant="primary">{product.event.name}</Badge>
          </div>
        )}

        {/* Inventory status */}
        {isOutOfStock && (
          <div className="absolute top-2 right-2">
            <Badge variant="danger">Out of Stock</Badge>
          </div>
        )}
      </div>

      <div className="p-4 flex-grow flex flex-col">
        <h3 className="text-lg font-semibold mb-1">{product.name}</h3>

        <div className="text-sm text-text-muted mb-2">
          {product.category.name}
        </div>

        {product.shortDescription && (
          <p className="text-sm mb-4 flex-grow">{product.shortDescription}</p>
        )}

        <div className="mt-auto flex items-center justify-between">
          <span className="text-lg font-bold">${product.price.toFixed(2)}</span>

          <Link href={`/shop/products/${product.id}`}>
            <Button variant="primary" size="sm" disabled={isOutOfStock}>
              View Details
            </Button>
          </Link>
        </div>
      </div>
    </Card>
  );
};
