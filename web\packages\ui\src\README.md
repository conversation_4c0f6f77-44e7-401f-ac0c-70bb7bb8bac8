# UI Component Source

Source code for the shared UI component library with reusable React components and utilities.

## Components

- **button/** - Button components with various styles, sizes, and states
- **card/** - Card layout components for content containers
- **content-card/** - Specialized content display cards
- **editor/** - Rich text editor components for content creation
- **featured-content/** - Components for highlighting featured content
- **hero/** - Hero section components for landing pages
- **input/** - Form input components including text fields, selects, and validation
- **modal/** - Modal dialog components for overlays and confirmations
- **pagination/** - Pagination controls for data navigation
- **scroll-to-top/** - Utility components for page navigation
- **search-bar/** - Search input components with filtering
- **sidebar/** - Navigation sidebar components
- **spinner/** - Loading indicator components

## Utilities

- **hooks/** - Custom React hooks for UI-specific functionality
- **index.tsx** - Main export file for the component library

Each component directory contains the component implementation, styles, and TypeScript definitions for consistent usage across applications.
