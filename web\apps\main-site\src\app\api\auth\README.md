# Authentication API Routes

Authentication and user session management endpoints supporting multiple authentication methods.

## Core Authentication

- **login/** - Email/password authentication with JWT token generation
- **register/** - New user registration with email verification
- **me/** - Current user information and session validation
- **verify-email/** - Email verification for new accounts
- **set-password/** - Password creation and reset functionality
- **has-password/** - Check if user has set a password

## OAuth Integration

- **discord/** - Discord OAuth authentication and account linking

## Development & Testing

- **test/** - Authentication testing utilities and validation
- **prisma-test/** - Database connection testing for authentication

These endpoints handle secure user authentication, session management, and provide integration with external OAuth providers while maintaining JWT-based session security.
