# Bank of Styx - Codebase Structure

## Root Directory Structure
```
/
├── web/                           # Core monorepo
├── context/                       # Documentation context
├── docs/                          # Project documentation  
├── Operations/                    # Operational files
├── CLAUDE.md                      # Development instructions
├── README.md                      # Project overview
└── package.json                   # Root package configuration
```

## Monorepo Structure (`web/`)
```
web/
├── apps/                          # Applications
│   ├── api/                       # Standalone API modules
│   │   └── auth/                  # Authentication endpoints
│   └── main-site/                 # Primary Next.js app
└── packages/                      # Shared packages
    ├── config/                    # Shared configurations
    └── ui/                        # Shared UI components
```

## Main Application (`web/apps/main-site/`)
```
src/
├── app/                           # Next.js App Router
│   ├── api/                       # API endpoints
│   │   ├── auth/                  # Authentication APIs
│   │   ├── bank/                  # Banking APIs
│   │   ├── events/                # Event management
│   │   ├── news/                  # News system
│   │   ├── volunteer/             # Volunteer system
│   │   ├── shop/                  # Shopping system
│   │   └── support/               # Support tickets
│   ├── admin/                     # Admin dashboard
│   ├── bank/                      # Banking pages
│   ├── events/                    # Event pages
│   ├── news/                      # News pages
│   ├── shop/                      # Shopping pages
│   └── volunteer/                 # Volunteer pages
├── components/                    # React components
│   ├── auth/                      # Auth components
│   ├── bank/                      # Banking components
│   ├── events/                    # Event components
│   ├── news/                      # News components
│   ├── shop/                      # Shop components
│   ├── volunteer/                 # Volunteer components
│   ├── shared/                    # Shared components
│   └── ui/                        # UI utility components
├── hooks/                         # Custom React hooks
├── lib/                           # Utility functions
├── services/                      # API service functions
├── types/                         # TypeScript definitions
└── utils/                         # Helper utilities
```

## Database Structure (`prisma/`)
```
prisma/
├── schema.prisma                  # Database schema
├── seed.js                        # Database seeding
└── migrations/                    # Migration history
    ├── init/                      # Initial setup
    ├── bank_models/               # Banking system
    ├── news_models/               # News system
    ├── event_management/          # Events
    ├── volunteer_models/          # Volunteer system
    ├── cart_and_order_models/     # Shopping
    └── ticket_hold_system/        # Ticket holds
```

## Key File Locations
- **API Routes**: `src/app/api/`
- **Pages**: `src/app/` (App Router structure)
- **Components**: `src/components/` (organized by feature)
- **Database Schema**: `prisma/schema.prisma`
- **Environment Config**: `.env.local.example`
- **TypeScript Config**: `tsconfig.json`