config:
  target: 'http://192.168.1.87:3000'
  phases:
    - duration: 60
      arrivalRate: 5
    - duration: 120 
      arrivalRate: 10
    - duration: 60
      arrivalRate: 20
  variables:
    - loginEmail: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
    - loginPassword: "TestPassword123!"

scenarios:
  - name: "Homepage Browse"
    weight: 30
    flow:
      - get:
          url: "/"
      - think: 2
      - get:
          url: "/news"
      - think: 3
      - get:
          url: "/events"

  - name: "Banking Flow"
    weight: 25
    flow:
      - get:
          url: "/bank"
      - think: 2
      - get:
          url: "/api/bank/account-summary"
          headers:
            Authorization: "Bearer {{ token }}"
      - think: 3
      - get:
          url: "/api/bank/transactions"
          headers:
            Authorization: "Bearer {{ token }}"

  - name: "Shop Browse"
    weight: 25
    flow:
      - get:
          url: "/shop"
      - think: 2
      - get:
          url: "/api/products"
      - think: 1
      - get:
          url: "/api/product-categories"
      - think: 2
      - get:
          url: "/api/cart"

  - name: "User Login Flow"
    weight: 20
    flow:
      - post:
          url: "/api/auth/login"
          json:
            email: "{{ loginEmail }}"
            password: "{{ loginPassword }}"
          capture:
            - json: "$.token"
              as: "token"
      - think: 1
      - get:
          url: "/api/auth/me"
          headers:
            Authorization: "Bearer {{ token }}"
      - think: 2
      - get:
          url: "/api/notifications"
          headers:
            Authorization: "Bearer {{ token }}"