import { NextRequest, NextResponse } from "next/server";
import prisma from "../../../../lib/prisma";
import { getCurrentUser } from "../../../../lib/auth";


// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
// PUT endpoint to update default view
export async function PUT(req: NextRequest) {
  try {
    // Get current user
    const currentUser = await getCurrentUser(req);
    if (!currentUser) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Parse request body
    const { defaultView } = await req.json();

    // Validate input
    if (!defaultView) {
      return NextResponse.json(
        { error: "Default view is required" },
        { status: 400 },
      );
    }

    // Validate defaultView value
    const validViews = [
      "dashboard",
      "transactions",
      "transfer",
      "pay-code",
      "donate",
    ];
    if (!validViews.includes(defaultView)) {
      return NextResponse.json(
        { error: "Invalid default view value" },
        { status: 400 },
      );
    }

    // Update user preferences
    const updatedUser = await prisma.user.update({
      where: { id: currentUser.id },
      data: {
        defaultView,
      },
      select: {
        defaultView: true,
      },
    });

    // Return updated preferences
    return NextResponse.json({
      success: true,
      defaultView: updatedUser.defaultView,
    });
  } catch (error) {
    console.error("Error updating default view:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
