# Shopping Cart API Routes

Shopping cart management with integrated ticket hold system for preventing overselling.

## Endpoints

- **route.ts** - Main cart operations (view, add items, update quantities)
- **items/** - Individual cart item management and operations
- **clear/** - Cart clearing and cleanup operations
- **refresh-holds/** - Ticket hold refresh and extension functionality

## Ticket Hold Integration

The cart system integrates with the ticket hold system to:

- Automatically place holds on items when added to cart
- Provide 15-minute automatic expiration with extension capabilities
- Prevent race conditions and overselling
- Manage hold status throughout the shopping experience

These endpoints ensure a secure shopping experience with inventory protection and seamless cart management.
