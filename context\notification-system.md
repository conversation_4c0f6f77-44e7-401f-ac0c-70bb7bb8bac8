# Notification System Architecture

## Overview
A comprehensive multi-channel notification system with user preferences, real-time SSE delivery, role-based broadcasting, and category-based filtering integrated across all platform features.

## Core Components

### Notification Lifecycle
The notification system follows a structured flow from creation to delivery:

#### Notification Creation Flow
```typescript
// Multi-step notification process
const notificationFlow = {
  1: "Check user notification preferences for category",
  2: "Create notification record in database if enabled",
  3: "Format notification for SSE delivery", 
  4: "Broadcast to active SSE connections",
  5: "Store persistently for later retrieval"
};

// Preference-based filtering prevents unwanted notifications
const preferenceCheck = {
  transaction: "user.notifyTransfers/Deposits/Withdrawals",
  news: "user.notifyNewsEvents",
  auction: "user.notifyAuctions", 
  chat: "user.notifyChat",
  admin: "user.notifyAdmin"
};
```

#### Notification Priority System
```typescript
// Three-tier priority system affecting display and urgency
const priorityLevels = {
  low: "Standard notifications, normal display order",
  medium: "Default priority, standard handling",
  high: "Important notifications, prominent display"
};

// Priority influences:
// - Display order in notification panels
// - UI styling and visual prominence
// - Future: Could affect email/push notification urgency
```

### Database Schema

#### Notification Model
```sql
Notification {
  id: String (UUID)
  category: String (required) -- transaction, news, auction, chat, admin, system
  type: String (required) -- specific action type within category
  title: String (required, max 255 chars)
  message: String (required, Text field)
  read: Boolean (default: false)
  
  // Optional Enhancement Fields
  link: String? (URL for click-through actions)
  icon: String? (Icon identifier for UI display)
  priority: String (default: "medium") -- low, medium, high
  
  // Relationships
  userId: String (required)
  user: User (relationship)
  transactionId: String? (optional link to transaction)
  transaction: Transaction? (optional relationship)
  
  // Timestamps
  createdAt: DateTime
  updatedAt: DateTime
}
```

#### User Notification Preferences
```sql
// Stored directly on User model for performance
User {
  // Individual category toggles
  notifyTransfers: Boolean (default: true)
  notifyDeposits: Boolean (default: true) 
  notifyWithdrawals: Boolean (default: true)
  notifyNewsEvents: Boolean (default: false)
  notifyAuctions: Boolean (default: true)
  notifyChat: Boolean (default: true)
  notifyAdmin: Boolean (default: true)
}
```

### Multi-Channel Delivery System

#### SSE Real-time Delivery  
```typescript
// Primary delivery mechanism via Server-Sent Events
const sseDelivery = {
  trigger: "Immediate after database creation",
  target: "Active SSE connections for user",
  format: {
    type: "notification",
    notification: "formatted notification object"
  },
  fallback: "Skips if no active connections"
};

// SSE integration with connection store
const broadcastToUser = async (userId: string, notification: Notification) => {
  if (connectionStore.getUserConnectionCount(userId) > 0) {
    await connectionStore.sendToUser(userId, {
      type: "notification",
      notification: formatNotification(notification)
    });
  }
};
```

#### Persistent Storage
```typescript
// Database persistence for offline retrieval
const persistentStorage = {
  purpose: "Store notifications for later retrieval",
  access: "API endpoints for notification history",
  features: [
    "Pagination support",
    "Category filtering", 
    "Read/unread status",
    "Bulk read marking"
  ]
};
```

### Category-based Notification Types

#### Transaction Notifications
```typescript
// Banking system integration
const transactionNotifications = {
  transfer_sent: {
    title: "Transfer Sent",
    message: "You sent NS {amount} to @{recipient}",
    category: "transaction",
    priority: "medium"
  },
  transfer_received: {
    title: "Transfer Received", 
    message: "You received NS {amount} from @{sender}",
    category: "transaction",
    priority: "medium"
  },
  deposit_approved: {
    title: "Deposit Approved",
    message: "Your deposit of NS {amount} has been approved",
    category: "transaction", 
    priority: "medium"
  },
  withdrawal_request: {
    title: "Withdrawal Request",
    message: "Your withdrawal request of NS {amount} is pending approval",
    category: "transaction",
    priority: "medium"
  }
};
```

#### Admin Notifications
```typescript
// Administrative system notifications
const adminNotifications = {
  new_support_ticket: {
    title: "New Support Ticket",
    message: "New ticket from {user}: {subject}",
    category: "admin",
    priority: "high"
  },
  withdrawal_request: {
    title: "New Withdrawal Request", 
    message: "New withdrawal request of NS {amount} from {user}",
    category: "admin",
    priority: "high"
  },
  system_alert: {
    title: "System Alert",
    message: "System maintenance scheduled for {time}",
    category: "admin",
    priority: "high"
  }
};
```

#### News & Event Notifications
```typescript
// Content system integration
const newsNotifications = {
  article_published: {
    title: "New Article Published",
    message: "New article: {title}",
    category: "news",
    priority: "low"
  },
  event_reminder: {
    title: "Event Reminder",
    message: "Event {eventName} starts in {timeRemaining}",
    category: "news", 
    priority: "medium"
  }
};
```

## User Preference Management

### Preference Storage
```typescript
// User preferences stored on User model for performance
const preferenceFields = {
  notifyTransfers: "Bank transfer notifications",
  notifyDeposits: "Deposit approval/rejection notifications", 
  notifyWithdrawals: "Withdrawal request/approval notifications",
  notifyNewsEvents: "News articles and event notifications",
  notifyAuctions: "Auction activity notifications",
  notifyChat: "Chat and messaging notifications",
  notifyAdmin: "Administrative announcements"
};
```

### Preference Enforcement
```typescript
// Preference checking before notification creation
const checkUserPreferences = async (userId: string, category: string, type: string) => {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: preferenceFields
  });
  
  switch (category) {
    case "transaction":
      if (type.includes("transfer")) return user.notifyTransfers;
      if (type.includes("deposit")) return user.notifyDeposits;  
      if (type.includes("withdrawal")) return user.notifyWithdrawals;
      break;
    case "news":
      return user.notifyNewsEvents;
    case "admin": 
      return user.notifyAdmin;
    // ... other categories
  }
  
  return true; // Default to notify if category not found
};
```

### Preference Management API
```typescript
// API endpoints for preference management
const preferenceAPI = {
  get: "GET /api/users/notification-preferences",
  update: "PUT /api/users/notification-preferences",
  format: {
    notifications: {
      notifyTransfers: boolean,
      notifyDeposits: boolean,
      // ... other preferences
    }
  }
};
```

## API Architecture

### Core Notification Endpoints

#### GET /api/notifications
**User notification retrieval** with filtering and pagination:

```typescript
interface NotificationFilters {
  limit?: number;              // Max notifications to return (default: 20)
  unreadOnly?: boolean;        // Show only unread notifications
  category?: string;           // Filter by notification category
}

// Response includes transaction relationships
interface NotificationResponse {
  notifications: Notification[];
  // Each notification includes:
  // - Full notification data
  // - Related transaction data if transactionId present
  // - Formatted timestamps as ISO strings
}
```

#### PATCH /api/notifications
**Mark notifications as read** with bulk operations:

```typescript
const markAsRead = async (data: {
  ids?: string[];           // Mark specific notifications as read
  all?: boolean;            // Mark all user notifications as read
}) => {
  // Supports both individual and bulk read marking
  // Updates only notifications belonging to authenticated user
};
```

#### POST /api/notifications/create
**Admin notification creation** with permission checks:

```typescript
const createNotification = async (data: {
  userId?: string;          // Target user (admin-only for other users)
  category: string;         // Required notification category
  type: string;             // Required notification type
  title: string;            // Required notification title
  message: string;          // Required notification message
  link?: string;            // Optional click-through URL
  icon?: string;            // Optional icon identifier
  priority?: "low" | "medium" | "high";
  transactionId?: string;   // Optional transaction reference
}) => {
  // Permission validation:
  // - Users can only create notifications for themselves
  // - Admins can create notifications for any user
};
```

### Broadcasting Endpoints

#### POST /api/notifications/broadcast
**Real-time notification broadcasting** with role-based access:

```typescript
const broadcastNotification = async (data: {
  userId?: string;          // Send to specific user
  userIds?: string[];       // Send to multiple users (admin-only)
  broadcast?: boolean;      // Send to all users (admin-only)
  notification: object;     // Notification data to broadcast
}) => {
  // Permission matrix:
  // - Users: Can broadcast to themselves only
  // - Admins: Can broadcast to specific users, multiple users, or all users
  
  // Returns delivery statistics:
  // - Number of connections reached
  // - Number of users targeted
  // - Delivery results per user
};
```

## Helper Functions and Utilities

### Core Notification Creation
```typescript
// Primary notification creation function
export async function createNotification(
  userId: string,
  data: {
    category: string;
    type: string; 
    title: string;
    message: string;
    link?: string;
    icon?: string;
    priority?: "low" | "medium" | "high";
    transactionId?: string;
  }
) {
  // 1. Check user notification preferences for category
  // 2. Create database record if preferences allow
  // 3. Format for SSE delivery
  // 4. Broadcast to active connections
  // 5. Return created notification or null if blocked by preferences
}
```

### Batch Notification Functions
```typescript
// Multi-user notification creation
export async function createNotificationForMultipleUsers(
  userIds: string[],
  notificationData: NotificationData
) => {
  // Creates notifications for multiple users
  // Respects individual user preferences
  // Returns array of created notifications
}

// System-wide notifications
export async function createSystemNotification(
  notificationData: NotificationData
) => {
  // Sends notification to all active users
  // Queries all users with status: "active"
  // Processes preferences individually
}

// Admin-targeted notifications  
export async function createAdminNotification(
  notificationData: NotificationData
) => {
  // Sends notification to all admin users
  // Automatically sets category: "admin"
  // Filters to users with isAdmin: true
}
```

## Integration Patterns

### Banking System Integration
```typescript
// Transaction notification examples
const bankingIntegration = {
  transfer: async (transaction) => {
    // Notify sender
    await createNotification(transaction.senderId, {
      category: "transaction",
      type: "transfer_sent", 
      title: "Transfer Sent",
      message: `You sent NS ${transaction.amount} to @${transaction.recipient.username}`,
      transactionId: transaction.id
    });
    
    // Notify recipient
    await createNotification(transaction.recipientId, {
      category: "transaction",
      type: "transfer_received",
      title: "Transfer Received", 
      message: `You received NS ${transaction.amount} from @${transaction.sender.username}`,
      transactionId: transaction.id
    });
  }
};
```

### Support Ticket Integration
```typescript
// Support system notifications
const supportIntegration = {
  newTicket: async (ticket) => {
    // Notify all admins of new ticket
    await createAdminNotification({
      type: "new_support_ticket",
      title: "New Support Ticket",
      message: `New ticket from ${ticket.name}: ${ticket.subject}`,
      link: `/admin/support/tickets/${ticket.id}`,
      priority: "high"
    });
  },
  
  ticketResponse: async (ticket, response) => {
    // Notify ticket submitter of response
    if (ticket.userId) {
      await createNotification(ticket.userId, {
        category: "admin",
        type: "ticket_response",
        title: "Support Ticket Response",
        message: `New response to your ticket: ${ticket.subject}`,
        link: `/support/tickets/${ticket.id}`
      });
    }
  }
};
```

### Volunteer System Integration
```typescript
// Volunteer management notifications
const volunteerIntegration = {
  shiftAssignment: async (assignment) => {
    await createNotification(assignment.userId, {
      category: "admin",
      type: "shift_assigned",
      title: "Volunteer Shift Confirmed",
      message: `Your volunteer shift for ${assignment.shift.title} has been confirmed`,
      link: `/volunteer/dashboard`
    });
  },
  
  paymentProcessed: async (payment) => {
    await createNotification(payment.userId, {
      category: "transaction", 
      type: "volunteer_payment",
      title: "Volunteer Payment Processed",
      message: `Payment of NS ${payment.amount} for volunteer work has been processed`,
      transactionId: payment.transactionId
    });
  }
};
```

## Frontend Integration

### TypeScript Service Layer
```typescript
// Complete notification service interface
export interface NotificationService {
  // Notification retrieval
  getUserNotifications: (filters?: NotificationFilters) => Promise<Notification[]>;
  
  // Read state management
  markNotificationsAsRead: (data: { ids?: string[]; all?: boolean }) => Promise<{ message: string }>;
  
  // Preference management
  getNotificationPreferences: () => Promise<UserPreferences>;
  updateNotificationPreferences: (preferences: Partial<UserPreferences>) => Promise<{ success: boolean }>;
  
  // Admin functions
  createNotification: (data: NotificationData) => Promise<{ notification: Notification }>;
  broadcastNotification: (data: BroadcastData) => Promise<{ success: boolean; results: any[] }>;
}
```

### SSE Integration
```typescript
// Client-side SSE notification handling
const sseNotificationHandler = {
  connect: "useSSE hook establishes connection",
  listen: "Event listener for 'notification' type messages",
  process: "Update local notification state",
  display: "Show in-app notification UI",
  update: "Refresh notification lists"
};

// Example SSE message handling
const handleSSENotification = (event: MessageEvent) => {
  const data = JSON.parse(event.data);
  
  if (data.type === "notification") {
    // Update notification state
    setNotifications(prev => [data.notification, ...prev]);
    
    // Show toast/popup for real-time feedback
    showNotificationToast(data.notification);
    
    // Update unread count
    updateUnreadCount();
  }
};
```

### UI Components Integration
```typescript
// Notification display components
const notificationComponents = {
  NotificationPanel: "Main notification list with filtering",
  NotificationItem: "Individual notification display",
  NotificationIcon: "Bell icon with unread count badge",  
  NotificationToast: "Real-time popup notifications",
  PreferenceManager: "User preference configuration UI"
};
```

## Performance Considerations

### Database Optimization
```typescript
// Query optimization patterns
const optimizedQueries = {
  // Index on userId for fast user notification retrieval
  userNotifications: "Indexed by userId, createdAt DESC",
  
  // Limit result sets with pagination
  pagination: "LIMIT clause prevents large result sets",
  
  // Selective field loading for API responses
  fieldSelection: "Only load required fields for each endpoint",
  
  // Preference caching on User model
  preferences: "Stored directly on User table for fast access"
};
```

### Connection Management
```typescript
// SSE connection efficiency
const connectionOptimization = {
  // Single connection per user (not per tab)
  connectionLimit: "One SSE connection per user across all devices",
  
  // Batch delivery for multiple notifications
  batchDelivery: "Group notifications sent together when possible",
  
  // Connection health monitoring
  healthCheck: "Heartbeat system detects and cleans stale connections",
  
  // Fallback for offline users
  persistence: "Notifications stored for retrieval when user comes online"
};
```

### Scalability Features
```typescript
// System scalability considerations
const scalabilityFeatures = {
  // Preference-based filtering reduces notification volume
  preferenceFiltering: "Users only receive notifications they want",
  
  // Priority system allows important notifications to surface
  priorityQueuing: "High priority notifications get prominence",
  
  // Batch operations for admin notifications
  batchProcessing: "Efficient multi-user notification creation",
  
  // Connection pooling and cleanup
  connectionManagement: "Automatic cleanup of inactive connections"
};
```

## Security Features

### Permission Validation
```typescript
// Role-based access control for notification operations
const securityValidation = {
  // User permissions
  userPermissions: {
    read: "Own notifications only",
    create: "Self-notifications only", 
    broadcast: "Self only",
    preferences: "Own preferences only"
  },
  
  // Admin permissions  
  adminPermissions: {
    read: "All notifications (admin endpoint)",
    create: "Any user notifications",
    broadcast: "Single user, multiple users, or system-wide",
    preferences: "View/modify any user preferences"
  }
};
```

### Data Privacy
```typescript
// Privacy protection measures
const privacyProtection = {
  // User isolation
  userIsolation: "Users can only access their own notifications",
  
  // Admin audit trail
  adminAudit: "Admin actions logged for security monitoring",
  
  // Preference privacy
  preferencePrivacy: "Notification preferences are private to each user",
  
  // Connection security
  connectionSecurity: "SSE connections validated with JWT tokens"
};
```

## Important Files
- `src/lib/notifications.ts` - Core notification creation and management functions
- `src/app/api/notifications/route.ts` - Main notification CRUD operations
- `src/app/api/notifications/create/route.ts` - Admin notification creation
- `src/app/api/notifications/broadcast/route.ts` - Real-time notification broadcasting
- `src/services/notificationService.ts` - Frontend service layer
- `src/lib/connectionStore.ts` - SSE connection management for delivery
- `prisma/schema.prisma` - Notification model and user preference fields