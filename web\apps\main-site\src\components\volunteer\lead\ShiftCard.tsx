"use client";

import React, { useState } from "react";
import Image from "next/image";
import { format } from "date-fns";
import fetchClient from "@/lib/fetchClient";
import toast from "react-hot-toast";

interface User {
  id: string;
  displayName: string;
  avatar: string;
  email: string;
}

interface VolunteerHours {
  id: string;
  hoursWorked: number;
  paymentAmount: number;
  paymentStatus: string;
}

interface Assignment {
  id: string;
  userId: string;
  status: string;
  user: User;
  hours: VolunteerHours | null;
}

interface Shift {
  id: string;
  title: string;
  description: string | null;
  startTime: string;
  endTime: string;
  location: string | null;
  maxVolunteers: number;
  vacancies: number;
  assignments: Assignment[];
}

interface ShiftCardProps {
  shift: Shift;
  onUpdate: () => void;
}

export const LeadShiftCard: React.FC<ShiftCardProps> = ({
  shift,
  onUpdate,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isProcessing, setIsProcessing] = useState<Record<string, boolean>>({});
  const [selectedMultiplier, setSelectedMultiplier] = useState<
    Record<string, number>
  >({});

  const startTime = new Date(shift.startTime);
  const endTime = new Date(shift.endTime);
  const formattedDate = format(startTime, "MMMM d, yyyy");
  const formattedStartTime = format(startTime, "h:mm a");
  const formattedEndTime = format(endTime, "h:mm a");

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const handleStatusChange = async (assignmentId: string, status: string) => {
    try {
      setIsProcessing((prev) => ({ ...prev, [assignmentId]: true }));

      const multiplier = selectedMultiplier[assignmentId] || 1;

      await fetchClient.patch(
        `/api/volunteer/lead/shifts/${assignmentId}/attendance`,
        {
          status,
          payMultiplier: multiplier,
        },
      );

      toast.success(`Volunteer status updated to ${status}`);
      onUpdate();
    } catch (error) {
      console.error("Error updating status:", error);
      toast.error("Failed to update volunteer status");
    } finally {
      setIsProcessing((prev) => ({ ...prev, [assignmentId]: false }));
    }
  };

  const handleMultiplierChange = (assignmentId: string, multiplier: number) => {
    setSelectedMultiplier((prev) => ({ ...prev, [assignmentId]: multiplier }));
  };

  return (
    <div className="bg-secondary rounded-lg shadow-md border border-gray-600 mb-4 overflow-hidden">
      {/* Shift Header */}
      <div
        className="p-4 cursor-pointer flex justify-between items-center"
        onClick={toggleExpand}
      >
        <div>
          <h3 className="text-lg font-semibold text-white">{shift.title}</h3>
          <p className="text-gray-400 text-sm">
            {formattedDate} • {formattedStartTime} - {formattedEndTime}
          </p>
          {shift.location && (
            <p className="text-gray-400 text-sm">Location: {shift.location}</p>
          )}
        </div>
        <div className="text-right">
          <p className="text-white font-medium">
            {shift.assignments.length}/{shift.maxVolunteers} Volunteers
          </p>
          <p
            className={`text-sm ${
              shift.vacancies > 0 ? "text-green-400" : "text-red-400"
            }`}
          >
            {shift.vacancies > 0
              ? `${shift.vacancies} spots available`
              : "Fully booked"}
          </p>
          <span className="text-gray-400">
            {isExpanded ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 15l7-7 7 7"
                />
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            )}
          </span>
        </div>
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div className="border-t border-gray-600 p-4">
          {shift.description && (
            <div className="mb-4">
              <p className="text-gray-300">{shift.description}</p>
            </div>
          )}

          <h4 className="text-white font-medium mb-2">Volunteers</h4>

          {shift.assignments.length === 0 ? (
            <p className="text-gray-400">No volunteers have signed up yet.</p>
          ) : (
            <div className="space-y-4">
              {shift.assignments.map((assignment) => (
                <div
                  key={assignment.id}
                  className="bg-secondary-dark p-3 rounded-lg border border-gray-700 flex flex-col md:flex-row justify-between items-start md:items-center gap-3"
                >
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full overflow-hidden mr-3">
                      <Image
                        src={assignment.user.avatar}
                        alt={assignment.user.displayName}
                        width={40}
                        height={40}
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <p className="text-white font-medium">
                        {assignment.user.displayName}
                      </p>
                      <p className="text-gray-400 text-sm">
                        {assignment.user.email}
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2 items-center w-full md:w-auto">
                    {/* Status badges */}
                    <div className="flex gap-2 flex-wrap">
                      <span
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                          assignment.status === "assigned"
                            ? "bg-blue-900/50 text-blue-200"
                            : assignment.status === "checked_in"
                            ? "bg-indigo-900/50 text-indigo-200"
                            : assignment.status === "completed"
                            ? "bg-green-900/50 text-green-200"
                            : assignment.status === "abandoned"
                            ? "bg-orange-900/50 text-orange-200"
                            : assignment.status === "no-show"
                            ? "bg-red-900/50 text-red-200"
                            : "bg-gray-700 text-gray-300"
                        }`}
                      >
                        {assignment.status.charAt(0).toUpperCase() +
                          assignment.status.slice(1)}
                      </span>

                      {assignment.hours && (
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            assignment.hours.paymentStatus === "pending"
                              ? "bg-yellow-900/50 text-yellow-200"
                              : assignment.hours.paymentStatus === "processing"
                              ? "bg-blue-900/50 text-blue-200"
                              : assignment.hours.paymentStatus === "paid"
                              ? "bg-green-900/50 text-green-200"
                              : "bg-gray-700 text-gray-300"
                          }`}
                        >
                          {assignment.hours.paymentStatus
                            .charAt(0)
                            .toUpperCase() +
                            assignment.hours.paymentStatus.slice(1)}
                        </span>
                      )}
                    </div>

                    {/* Payment multiplier */}
                    {assignment.status !== "completed" && (
                      <div className="flex items-center gap-2 mt-2 md:mt-0">
                        <label className="text-gray-300 text-sm">Pay:</label>
                        <select
                          className="bg-secondary border border-gray-600 text-white rounded-md text-sm px-2 py-1"
                          value={selectedMultiplier[assignment.id] || 1}
                          onChange={(e) =>
                            handleMultiplierChange(
                              assignment.id,
                              Number(e.target.value),
                            )
                          }
                        >
                          <option value={1}>1x</option>
                          <option value={2}>2x</option>
                          <option value={3}>3x</option>
                          <option value={4}>4x</option>
                        </select>
                      </div>
                    )}

                    {/* Action buttons */}
                    <div className="flex gap-2 mt-2 md:mt-0">
                      {assignment.status === "assigned" && (
                        <>
                          <button
                            className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-md text-sm font-medium disabled:opacity-50"
                            onClick={() =>
                              handleStatusChange(assignment.id, "completed")
                            }
                            disabled={isProcessing[assignment.id]}
                          >
                            {isProcessing[assignment.id]
                              ? "Processing..."
                              : "Completed"}
                          </button>
                          <button
                            className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-md text-sm font-medium disabled:opacity-50"
                            onClick={() =>
                              handleStatusChange(assignment.id, "no-show")
                            }
                            disabled={isProcessing[assignment.id]}
                          >
                            No-show
                          </button>
                        </>
                      )}
                      
                      {assignment.status === "checked_in" && (
                        <>
                          <button
                            className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-md text-sm font-medium disabled:opacity-50"
                            onClick={() =>
                              handleStatusChange(assignment.id, "completed")
                            }
                            disabled={isProcessing[assignment.id]}
                          >
                            {isProcessing[assignment.id]
                              ? "Processing..."
                              : "Completed"}
                          </button>
                          <button
                            className="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1 rounded-md text-sm font-medium disabled:opacity-50"
                            onClick={() =>
                              handleStatusChange(assignment.id, "abandoned")
                            }
                            disabled={isProcessing[assignment.id]}
                          >
                            Abandoned
                          </button>
                          <button
                            className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-md text-sm font-medium disabled:opacity-50"
                            onClick={() =>
                              handleStatusChange(assignment.id, "no-show")
                            }
                            disabled={isProcessing[assignment.id]}
                          >
                            No-show
                          </button>
                        </>
                      )}
                      
                      {(assignment.status === "no-show" || assignment.status === "abandoned") && (
                        <button
                          className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-md text-sm font-medium disabled:opacity-50"
                          onClick={() =>
                            handleStatusChange(assignment.id, "checked_in")
                          }
                          disabled={isProcessing[assignment.id]}
                        >
                          Mark as Checked In
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
