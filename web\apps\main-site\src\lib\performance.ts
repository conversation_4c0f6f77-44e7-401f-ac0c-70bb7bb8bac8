/**
 * Performance utilities to reduce unnecessary re-renders and optimize component performance
 */

import { useCallback, useMemo } from "react";

/**
 * Stable reference for empty arrays to prevent unnecessary re-renders
 */
export const EMPTY_ARRAY: never[] = [];

/**
 * Stable reference for empty objects to prevent unnecessary re-renders
 */
export const EMPTY_OBJECT = {};

/**
 * Hook to create a stable empty array reference
 */
export function useEmptyArray<T>(): T[] {
  return useMemo(() => [], []);
}

/**
 * Hook to create a stable empty object reference
 */
export function useEmptyObject<T extends Record<string, any>>(): T {
  return useMemo(() => ({}) as T, []);
}

/**
 * Deep comparison utility for memoization
 */
export function deepEqual(a: any, b: any): boolean {
  if (a === b) return true;
  if (a == null || b == null) return false;
  if (typeof a !== typeof b) return false;

  if (typeof a === "object") {
    if (Array.isArray(a) !== Array.isArray(b)) return false;

    if (Array.isArray(a)) {
      if (a.length !== b.length) return false;
      return a.every((item, index) => deepEqual(item, b[index]));
    }

    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    if (keysA.length !== keysB.length) return false;

    return keysA.every((key) => deepEqual(a[key], b[key]));
  }

  return false;
}

/**
 * Debounce utility to prevent excessive function calls
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Hook for debounced callbacks
 */
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList,
): (...args: Parameters<T>) => void {
  return useCallback(
    debounce(callback, delay),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [...deps, delay],
  );
}
