import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";

interface Params {
  params: {
    id: string;
  };
}

export async function PATCH(request: Request, { params }: Params) {
  const { id } = params;

  try {
    const { status } = await request.json();

    // Validate status
    if (!status || !["draft", "published", "paused"].includes(status)) {
      return NextResponse.json(
        {
          error: "Invalid status. Must be one of: draft, published, paused",
        },
        { status: 400 },
      );
    }

    // Find the existing article
    const existingArticle = await prisma.newsArticle.findUnique({
      where: { id },
    });

    if (!existingArticle) {
      return NextResponse.json({ error: "Article not found" }, { status: 404 });
    }

    // Update publishedAt if needed
    let publishedAt = existingArticle.publishedAt;
    if (status === "published" && existingArticle.status !== "published") {
      publishedAt = new Date();
    }

    // Update the article status
    const updatedArticle = await prisma.newsArticle.update({
      where: { id },
      data: {
        status,
        publishedAt,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({
      id: updatedArticle.id,
      status: updatedArticle.status,
      publishedAt: updatedArticle.publishedAt,
    });
  } catch (error) {
    console.error("Error updating article status:", error);
    return NextResponse.json(
      { error: "Failed to update article status" },
      { status: 500 },
    );
  }
}
