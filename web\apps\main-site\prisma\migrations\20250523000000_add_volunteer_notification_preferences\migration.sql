-- AlterTable
ALTER TABLE `volunteer_assignments` ADD COLUMN `metadata` <PERSON><PERSON><PERSON> NULL,
    MODIFY `status` VARCHAR(191) NOT NULL DEFAULT 'pending';

-- CreateTable
CREATE TABLE `volunteer_notification_preferences` (
    `id` VARCHAR(191) NOT NULL,
    `assignmentId` VARCHAR(191) NOT NULL,
    `emailNotification` BOOLEAN NOT NULL DEFAULT true,
    `websiteNotification` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `volunteer_notification_preferences_assignmentId_key`(`assignmentId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddF<PERSON>ign<PERSON><PERSON>
ALTER TABLE `volunteer_notification_preferences` ADD CONSTRAINT `volunteer_notification_preferences_assignmentId_fkey` F<PERSON><PERSON><PERSON><PERSON> KEY (`assignmentId`) REFERENCES `volunteer_assignments`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
