"use client";

import { useState } from "react";
import {
  useSalesProductCategories,
  useDeleteProductCategory,
} from "@/hooks/useProductCategories";
import { ProductCategory } from "@/services/productService";
import { <PERSON><PERSON>, Spinner } from "@bank-of-styx/ui";
import { Select, Badge } from "@/components/shared";
import Link from "next/link";
import { toast } from "react-hot-toast";
import { useConfirm } from "@/hooks/useConfirm";

export const CategoryList: React.FC = () => {
  // State for filters
  const [activeFilter, setActiveFilter] = useState<string>("all");

  // Fetch categories with filters
  const { data, isLoading, error } = useSalesProductCategories(
    activeFilter === "active"
      ? true
      : activeFilter === "inactive"
      ? false
      : undefined,
  );

  // Delete category mutation
  const deleteCategory = useDeleteProductCategory();
  const { confirm, ConfirmationDialog } = useConfirm();

  // Handle delete category
  const handleDeleteCategory = async (id: string, name: string) => {
    const confirmed = await confirm({
      title: "Delete Category",
      message: `Are you sure you want to delete "${name}"? This action cannot be undone. Products in this category will need to be reassigned.`,
      confirmText: "Delete",
      cancelText: "Cancel",
      variant: "danger",
    });

    if (confirmed) {
      try {
        await deleteCategory.mutateAsync(id);
        toast.success("Category deleted successfully");
      } catch (error) {
        console.error("Error deleting category:", error);
        toast.error("Failed to delete category");
      }
    }
  };

  // Handle filter changes
  const handleActiveFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>,
  ) => {
    setActiveFilter(e.target.value);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center p-8">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 text-center">
        <p className="text-accent mb-4">Error loading categories</p>
        <Button onClick={() => window.location.reload()} variant="secondary">
          Try Again
        </Button>
      </div>
    );
  }

  const categories = data?.categories || [];

  return (
    <div>
      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-4 mb-6 p-4 bg-secondary-light rounded-lg">
        <div className="w-full md:w-1/3">
          <Select
            label="Status"
            value={activeFilter}
            onChange={handleActiveFilterChange}
            fullWidth
          >
            <option value="all">All Status</option>
            <option value="active">Active Only</option>
            <option value="inactive">Inactive Only</option>
          </Select>
        </div>
      </div>

      {/* Categories Table */}
      {categories.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-text-muted mb-4">
            No categories found matching your filters.
          </p>
          <Link href="/sales/categories/create">
            <Button variant="primary">Create New Category</Button>
          </Link>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-secondary-dark">
                <th className="px-4 py-3 text-left">Name</th>
                <th className="px-4 py-3 text-left">Description</th>
                <th className="px-4 py-3 text-center">Products</th>
                <th className="px-4 py-3 text-center">Status</th>
                <th className="px-4 py-3 text-right">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-border-subtle">
              {categories.map((category: ProductCategory) => (
                <tr key={category.id} className="hover:bg-secondary-light">
                  <td className="px-4 py-3 font-medium">{category.name}</td>
                  <td className="px-4 py-3">
                    {category.description ? (
                      <div className="max-w-md truncate">
                        {category.description}
                      </div>
                    ) : (
                      <span className="text-text-muted">No description</span>
                    )}
                  </td>
                  <td className="px-4 py-3 text-center">
                    {category._count?.products || 0}
                  </td>
                  <td className="px-4 py-3 text-center">
                    <Badge variant={category.isActive ? "success" : "warning"}>
                      {category.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </td>
                  <td className="px-4 py-3 text-right">
                    <div className="flex justify-end space-x-2">
                      <Link href={`/sales/categories/${category.id}/edit`}>
                        <Button variant="secondary" size="sm">
                          Edit
                        </Button>
                      </Link>
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={() =>
                          handleDeleteCategory(category.id, category.name)
                        }
                        loading={deleteCategory.isPending}
                      >
                        Delete
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Confirmation Dialog */}
      <ConfirmationDialog />
    </div>
  );
};
