"use client";

import React, { useState, useEffect } from "react";
import { NewsDashboardLayout } from "../../../components/news";
import Link from "next/link";
import { useArticles } from "../../../hooks/useNews";
import { useAuth } from "../../../contexts/AuthContext";
import { useRouter } from "next/navigation";

export default function NewsDashboardPage() {
  const { user, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);
  // Define types for dashboard data
  interface CategoryDistribution {
    category: string;
    count: number;
  }

  interface TopArticle {
    id: string;
    title: string;
    views: number;
    category: string;
  }

  interface RecentArticle {
    id: string;
    title: string;
    status: string;
    author: string;
    lastModified: string;
  }

  interface DashboardStats {
    publishedArticles: number;
    draftArticles: number;
    pausedArticles: number;
    totalViews: number;
    topArticles: TopArticle[];
    categoriesDistribution: CategoryDistribution[];
    recentArticles: RecentArticle[];
  }

  interface DashboardData {
    stats: DashboardStats;
  }

  const [dashboardData, setDashboardData] = useState<DashboardData | null>(
    null,
  );

  // Fetch articles with React Query
  const {
    data: articlesData,
    isLoading: articlesLoading,
    isError: articlesError,
  } = useArticles({ limit: 100 }); // Get a larger batch to calculate statistics

  // Process article data to build statistics
  useEffect(() => {
    if (articlesData?.data) {
      const articles = articlesData.data;

      // Count articles by status
      const publishedArticles = articles.filter(
        (a) => a.status === "published",
      ).length;
      const draftArticles = articles.filter((a) => a.status === "draft").length;
      const pausedArticles = articles.filter(
        (a) => a.status === "paused",
      ).length;

      // Calculate total views
      const totalViews = articles.reduce(
        (sum, article) => sum + (article.views || 0),
        0,
      );

      // Get top articles by views
      const topArticles = [...articles]
        .sort((a, b) => (b.views || 0) - (a.views || 0))
        .slice(0, 5)
        .map((article) => ({
          id: article.id,
          title: article.title,
          views: article.views || 0,
          category: article.category?.name || "Uncategorized",
        }));

      // Get category distribution
      const categoryMap: Record<string, number> = {};
      articles.forEach((article) => {
        const categoryName = article.category?.name || "Uncategorized";
        if (!categoryMap[categoryName]) {
          categoryMap[categoryName] = 0;
        }
        categoryMap[categoryName]++;
      });

      const categoriesDistribution = Object.entries(categoryMap)
        .map(([category, count]) => ({
          category,
          count: count,
        }))
        .sort((a, b) => b.count - a.count);

      // Get recent articles
      const recentArticles = [...articles]
        .sort(
          (a, b) =>
            new Date(b.updatedAt || b.createdAt).getTime() -
            new Date(a.updatedAt || a.createdAt).getTime(),
        )
        .slice(0, 5)
        .map((article) => ({
          id: article.id,
          title: article.title,
          status: article.status,
          author: article.author?.displayName || "Unknown",
          lastModified: new Date(
            article.updatedAt || article.createdAt,
          ).toLocaleDateString(),
        }));

      // Set dashboard data
      setDashboardData({
        stats: {
          publishedArticles,
          draftArticles,
          pausedArticles,
          totalViews,
          topArticles,
          categoriesDistribution,
          recentArticles,
        },
      });
    }
  }, [articlesData]);

  // Check authorization after auth is loaded
  useEffect(() => {
    if (!authLoading) {
      if (!user || !user.roles?.editor) {
        router.push("/");
      } else {
        setIsAuthorized(true);
      }
    }
  }, [user, router, authLoading]);

  // Show loading state or nothing while checking authorization
  if (authLoading || !isAuthorized) {
    return (
      <div className="min-h-screen bg-secondary-dark flex items-center justify-center">
        <div className="bg-secondary-light p-6 rounded-lg shadow-md border border-gray-600">
          <p className="text-white text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  // Show loading state while fetching articles
  if (articlesLoading) {
    return (
      <NewsDashboardLayout dashboardData={null}>
        <div className="space-y-6">
          <div className="bg-secondary-light rounded-lg shadow-md p-6 border border-gray-600">
            <div className="flex items-center justify-center h-64">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
              <p className="ml-2 text-gray-400">Loading dashboard data...</p>
            </div>
          </div>
        </div>
      </NewsDashboardLayout>
    );
  }

  // Show error state if article fetching failed
  if (articlesError) {
    return (
      <NewsDashboardLayout dashboardData={null}>
        <div className="space-y-4">
          <div className="bg-secondary-light rounded-lg shadow-md p-6 border border-gray-600">
            <div className="bg-accent bg-opacity-20 text-accent border border-accent rounded-md p-4">
              <p>Error loading dashboard data. Please try again later.</p>
            </div>
          </div>
        </div>
      </NewsDashboardLayout>
    );
  }

  return (
    <NewsDashboardLayout dashboardData={dashboardData}>
      <div className="space-y-4">
        {/* Quick Actions Section */}
        <div className="bg-secondary-light rounded-lg shadow-md pt-2 p-4 border border-gray-600">
          <h2 className="text-xl font-bold mb-2 text-white">Quick Actions</h2>

          {/* Quick Actions Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link
              href="/news/dashboard/articles/new"
              className="bg-secondary hover:bg-secondary-light border-2 border-primary p-4 rounded-lg flex flex-col items-center justify-center text-center transition-colors"
            >
              <svg
                className="w-8 h-8 text-primary mb-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
              <span className="text-white">Create Article</span>
            </Link>

            <Link
              href="/news/dashboard/articles"
              className="bg-secondary hover:bg-secondary-light border-2 border-gray-600 p-4 rounded-lg flex flex-col items-center justify-center text-center transition-colors"
            >
              <svg
                className="w-8 h-8 text-gray-400 mb-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                />
              </svg>
              <span className="text-white">Manage Articles</span>
            </Link>

            <Link
              href="/news/dashboard/categories"
              className="bg-secondary hover:bg-secondary-light border-2 border-gray-600 p-4 rounded-lg flex flex-col items-center justify-center text-center transition-colors"
            >
              <svg
                className="w-8 h-8 text-gray-400 mb-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                />
              </svg>
              <span className="text-white">Manage Categories</span>
            </Link>

            <Link
              href="/news/dashboard/featured"
              className="bg-secondary hover:bg-secondary-light border-2 border-gray-600 p-4 rounded-lg flex flex-col items-center justify-center text-center transition-colors"
            >
              <svg
                className="w-8 h-8 text-gray-400 mb-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"
                />
              </svg>
              <span className="text-white">Manage Featured</span>
            </Link>
          </div>
        </div>

        {/* Article Statistics Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Article Stats */}
          <div className="bg-secondary-light rounded-lg shadow-md pt-2 p-4 border border-gray-600">
            <h3 className="text-lg font-semibold mb-2 text-white">
              Article Status
            </h3>
            <div className="grid grid-cols-1 gap-2">
              <div className="bg-secondary-dark rounded-lg p-4 border border-gray-600">
                <h4 className="font-semibold mb-2 text-white">
                  Status Distribution
                </h4>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-green-500 mr-2"></div>
                      <span className="text-gray-400">Published</span>
                    </div>
                    <span className="text-white font-medium">
                      {dashboardData?.stats.publishedArticles || 0}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-gray-500 mr-2"></div>
                      <span className="text-gray-400">Draft</span>
                    </div>
                    <span className="text-white font-medium">
                      {dashboardData?.stats.draftArticles || 0}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-yellow-500 mr-2"></div>
                      <span className="text-gray-400">Paused</span>
                    </div>
                    <span className="text-white font-medium">
                      {dashboardData?.stats.pausedArticles || 0}
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-secondary-dark rounded-lg p-4 border border-gray-600">
                <h4 className="font-semibold mb-2 text-white">
                  Category Distribution
                </h4>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {dashboardData?.stats.categoriesDistribution?.map(
                    (category: CategoryDistribution, index: number) => (
                      <div
                        key={index}
                        className="flex justify-between items-center"
                      >
                        <span className="text-gray-400 truncate w-3/4">
                          {category.category}
                        </span>
                        <span className="text-white font-medium">
                          {category.count}
                        </span>
                      </div>
                    ),
                  ) || <p className="text-gray-400">No categories found</p>}
                </div>
              </div>
            </div>
          </div>

          {/* Top Articles */}
          <div className="bg-secondary-light rounded-lg shadow-md pt-2  p-4 border border-gray-600">
            <h3 className="text-lg font-semibold mb-2 text-white">
              Top Articles by Views
            </h3>
            <div className="space-y-2">
              {dashboardData?.stats.topArticles?.map(
                (article: TopArticle, index: number) => (
                  <div
                    key={index}
                    className="bg-secondary-dark rounded-lg p-4 border border-gray-600"
                  >
                    <h4 className="font-medium text-white truncate mb-2">
                      {article.title}
                    </h4>
                    <div className="flex justify-between">
                      <span className="text-gray-400 text-sm">
                        {article.category}
                      </span>
                      <span className="text-success text-sm">
                        {article.views} views
                      </span>
                    </div>
                    <div className="mt-3">
                      <Link
                        href={`/news/dashboard/articles/${article.id}`}
                        className="px-3 py-1 bg-secondary text-xs text-white rounded-md hover:bg-secondary-light"
                      >
                        Edit
                      </Link>
                    </div>
                  </div>
                ),
              ) || (
                <div className="bg-secondary-dark rounded-lg p-4 border border-gray-600 text-center">
                  <p className="text-gray-400">
                    No article view data available
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Recent Articles */}
          <div className="bg-secondary-light rounded-lg shadow-md pt-2 p-4 border border-gray-600">
            <h3 className="text-lg font-semibold mb-2 text-white">
              Recently Modified
            </h3>
            <div className="space-y-3">
              {dashboardData?.stats.recentArticles?.map(
                (article: RecentArticle, index: number) => (
                  <div
                    key={index}
                    className="bg-secondary-dark rounded-lg p-4 border border-gray-600"
                  >
                    <h4 className="font-medium text-white truncate mb-2">
                      {article.title}
                    </h4>
                    <div className="flex justify-between">
                      <span className="text-gray-400 text-sm">
                        {article.author}
                      </span>
                      <span className="text-gray-400 text-sm">
                        {article.lastModified}
                      </span>
                    </div>
                    <div className="flex justify-between items-center mt-2">
                      <span
                        className={`px-2 py-0.5 text-xs rounded-full ${
                          article.status === "published"
                            ? "bg-green-900 text-green-200"
                            : article.status === "draft"
                            ? "bg-gray-700 text-gray-300"
                            : "bg-yellow-900 text-yellow-200"
                        }`}
                      >
                        {article.status}
                      </span>
                      <Link
                        href={`/news/dashboard/articles/${article.id}`}
                        className="px-3 py-1 bg-secondary text-xs text-white rounded-md hover:bg-secondary-light"
                      >
                        Edit
                      </Link>
                    </div>
                  </div>
                ),
              ) || (
                <div className="bg-secondary-dark rounded-lg p-4 border border-gray-600 text-center">
                  <p className="text-gray-400">No recent articles</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </NewsDashboardLayout>
  );
}
