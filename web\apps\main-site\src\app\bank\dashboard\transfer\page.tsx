"use client";

import React, { useState, useEffect } from "react";
import {
  DashboardLayout,
  RecentTransactions,
} from "../../../../components/bank";
import { UserSearchInput } from "@bank-of-styx/ui";
import Link from "next/link";
import {
  useBankUser,
  useRecentTransfers,
  useCreateTransaction,
  useSearchUsers,
} from "../../../../hooks/useBank";
import { useAuth } from "../../../../contexts/AuthContext";
import { toast } from "react-hot-toast";

export default function TransferPage() {
  const { user } = useAuth();
  const { data: bankUser, isLoading: isLoadingUser } = useBankUser();
  const { data: recentTransfers, isLoading: isLoadingTransfers } =
    useRecentTransfers(5);
  const createTransaction = useCreateTransaction();

  const [recipient, setRecipient] = useState<string>("");
  const [recipientId, setRecipientId] = useState<string>("");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [amount, setAmount] = useState<string>("");
  const [note, setNote] = useState<string>("");
  const [showConfirmation, setShowConfirmation] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // User search hook
  const { data: searchResults, isLoading: isSearching } =
    useSearchUsers(searchQuery);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Validate amount
    const amountValue = parseFloat(amount);
    if (isNaN(amountValue) || amountValue <= 0) {
      setError("Please enter a valid amount greater than 0");
      return;
    }

    // Validate recipient
    if (!recipient || !recipientId) {
      setError("Please select a valid recipient from the search results");
      return;
    }

    // Validate balance
    if (bankUser && amountValue > bankUser.balance) {
      setError("Insufficient balance for this transfer");
      return;
    }

    setShowConfirmation(true);
  };

  const handleConfirm = async () => {
    try {
      await createTransaction.mutateAsync({
        type: "transfer",
        amount: parseFloat(amount),
        recipient,
        note: note || undefined,
      });

      // Show success message
      toast.success(`Successfully transferred NS ${amount} to @${recipient}`);

      // Reset form
      setRecipient("");
      setRecipientId("");
      setSearchQuery("");
      setAmount("");
      setNote("");
      setShowConfirmation(false);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "An error occurred";
      setError(errorMessage);
      toast.error(errorMessage);
      setShowConfirmation(false);
    }
  };

  const handleCancel = () => {
    setShowConfirmation(false);
  };

  const handleUserSelect = (user: {
    id: string;
    username: string;
    displayName: string;
  }) => {
    setRecipient(user.username);
    setRecipientId(user.id);
    setSearchQuery("");
  };

  // Reset recipient ID when recipient input changes
  useEffect(() => {
    if (!recipient) {
      setRecipientId("");
    }
  }, [recipient]);

  return (
    <DashboardLayout>
      <div className="mb-3">
        <Link
          href="/bank/dashboard"
          className="text-primary hover:text-primary-light"
        >
          &larr; Back to Dashboard
        </Link>
      </div>
      <div className="bg-secondary-light rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-2xl font-bold text-white mb-6">Transfer Funds</h2>

        <div className="mb-6">
          <p className="text-gray-400">
            Current Balance:{" "}
            <span className="font-bold text-success">
              NS {bankUser ? bankUser.balance.toFixed(0) : "..."}
            </span>
          </p>
        </div>

        {error && (
          <div className="mb-6 p-3 bg-error bg-opacity-20 border border-error rounded-md">
            <p className="text-error">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <div className="relative">
                <label
                  htmlFor="recipient"
                  className="block text-white font-medium mb-2"
                >
                  Recipient
                </label>
                <input
                  type="text"
                  id="recipient"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="Search for a user..."
                  required={!recipient}
                  disabled={!!recipient}
                />

                {recipient && (
                  <div className="mt-2 p-2 bg-secondary rounded-md flex justify-between items-center">
                    <span className="text-white">@{recipient}</span>
                    <button
                      type="button"
                      onClick={() => {
                        setRecipient("");
                        setRecipientId("");
                        setSearchQuery("");
                      }}
                      className="text-gray-400 hover:text-white"
                    >
                      ✕
                    </button>
                  </div>
                )}

                {searchQuery.length >= 3 && !recipient && (
                  <div className="absolute z-10 mt-1 w-full bg-secondary-light border border-gray-600 rounded-md shadow-lg max-h-60 overflow-auto">
                    {isSearching ? (
                      <div className="p-2 text-gray-400">Searching...</div>
                    ) : searchResults && searchResults.length > 0 ? (
                      <ul>
                        {searchResults.map((user) => (
                          <li key={user.id}>
                            <button
                              type="button"
                              onClick={() => handleUserSelect(user)}
                              className="w-full text-left px-4 py-2 hover:bg-secondary flex items-center"
                            >
                              {user.avatar && (
                                <img
                                  src={user.avatar}
                                  alt={user.username}
                                  className="w-8 h-8 rounded-full mr-2"
                                />
                              )}
                              <div>
                                <div className="text-white">
                                  @{user.username}
                                </div>
                                <div className="text-gray-400 text-sm">
                                  {user.displayName}
                                </div>
                              </div>
                            </button>
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <div className="p-2 text-gray-400">No users found</div>
                    )}
                  </div>
                )}
              </div>
              <p className="mt-1 text-sm text-gray-400">
                Type at least 3 characters to search for users
              </p>
            </div>

            <div>
              <label
                htmlFor="amount"
                className="block text-white font-medium mb-2"
              >
                Amount
              </label>
              <div className="relative flex items-center">
                <span className="absolute left-3 text-gray-400 select-none pointer-events-none">
                  NS
                </span>
                <input
                  type="number"
                  id="amount"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="0"
                  min="1"
                  step="1"
                  required
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="note"
                className="block text-white font-medium mb-2"
              >
                Note <span className="text-gray-400 text-sm">(Optional)</span>
              </label>
              <input
                type="text"
                id="note"
                value={note}
                onChange={(e) => setNote(e.target.value)}
                className="w-full px-4 py-2 bg-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="What's this transfer for?"
              />
            </div>

            <div className="flex justify-end space-x-4 pt-4">
              <button
                type="button"
                onClick={() => {
                  setRecipient("");
                  setAmount("");
                  setNote("");
                }}
                className="px-4 py-2 border border-gray-600 rounded-md text-white hover:bg-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
              >
                Send Transfer
              </button>
            </div>
          </div>
        </form>
      </div>

      <RecentTransactions
        transactions={recentTransfers || []}
        title="Recent Transfers"
        emptyMessage="No recent transfers."
        isLoading={isLoadingTransfers}
        useApi={true}
      />

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-secondary-light rounded-lg p-6 max-w-md w-full">
            <h3 className="text-xl font-bold text-white mb-4">
              Confirm Transfer
            </h3>
            <p className="mb-4 text-white">
              You are about to transfer{" "}
              <span className="font-bold">NS {amount}</span> to{" "}
              <span className="font-bold">@{recipient}</span>.
            </p>
            {note && <p className="mb-4 text-white">Note: {note}</p>}
            <div className="flex justify-end space-x-4">
              <button
                onClick={handleCancel}
                className="px-4 py-2 border border-gray-600 rounded-md text-white hover:bg-secondary"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirm}
                disabled={createTransaction.isPending}
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {createTransaction.isPending ? "Processing..." : "Confirm"}
              </button>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
}
