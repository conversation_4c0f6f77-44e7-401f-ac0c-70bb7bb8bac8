# Fix Me Now Template - URL Problem Documentation

**Instructions:** Fill out this template completely to document problems you see on specific pages. When you're done, save the file and the automated system will process it to update the corresponding audit files.

**How to use this:**
1. Fill out all sections below with your observations
2. Save this file in the `Problems/` folder with "problem" in the filename (e.g., "bank-deposit-button-problem.md")
3. The automation will find this file and update the correct audit page headers
4. You'll get notified when the fixes are implemented

**File Naming:** Always include "problem" in your filename so the system can find it easily:
- ✅ Good: `bank-deposit-problem.md`, `checkout-cart-problem.md`, `news-editor-problem.md`  
- ❌ Bad: `bank-deposit-issues.md`, `checkout-broken.md`, `news-bug.md`

---

## URL Information
**URL:** [Enter the full URL, e.g., /bank/dashboard/deposit]
**Page Name:** [What you call this page, e.g., "Deposit Money Page"]
**Date:** [Today's date]
**Your Name:** [Your name]

---

## What's Wrong?
**Problem:** [Describe in plain English what's not working]

**What Should Happen:** [Describe what you expect to happen when it works correctly]

**What Actually Happens:** [Describe what you see happening instead]

**How Bad Is It?** [ ] Broken (Can't use the page As intended) [ ] Annoying (Works but poorly) [ ] Minor (Small issue)




## More Details

### When Does This Happen?
**Always:** [ ] This problem happens every time
**Sometimes:** [ ] This problem happens randomly
**Specific Steps:** [ ] This problem only happens when I do these steps:
1. [Step 1]
2. [Step 2] 
3. [Step 3]

### What You See
**Error Messages:** [Copy any error messages you see on screen]

**Page Looks Wrong:** [Describe if buttons, text, or layout look broken]

**Nothing Happens:** [Describe if you click something and nothing happens]

### Who This Affects
**Just Me:** [ ] I think this only affects my account
**Everyone:** [ ] I think this affects all users
**Some People:** [ ] This probably affects: [describe which users]



## What Happens Next
After you save this completed template:
1. The automation system will find this file
2. It will locate the correct audit results file for your URL
3. It will copy your fixes to the audit file header and add the "FixMeNow" flag
4. The development agent will automatically implement your requested fixes

---

## Checklist
Before submitting:
- [ ] I filled out the URL and page name
- [ ] I described what's wrong in plain English
- [ ] I explained what should happen instead
- [ ] I saved and named in the correct folder
---

**Template Version:** 2.0 (Simplified)  
**Last Updated:** [Today's date]