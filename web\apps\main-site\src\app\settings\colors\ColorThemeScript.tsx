"use client";

import { useEffect, useState } from "react";

export default function ColorThemeScript() {
  // Add a state to ensure this component only renders on the client
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);

    // This script runs only on the client side
    const applyColorTheme = () => {
      try {
        const savedTheme = localStorage.getItem("bankOfStyxColorTheme");
        if (savedTheme) {
          const themeValues = JSON.parse(savedTheme);

          // Apply saved color values to CSS variables
          Object.entries(themeValues).forEach(([variable, value]) => {
            document.documentElement.style.setProperty(
              variable,
              value as string,
            );
          });
        }
      } catch (error) {
        console.error("Error applying color theme:", error);
      }
    };

    // Apply theme immediately
    applyColorTheme();

    // Also set up a storage event listener to apply changes if made in another tab
    window.addEventListener("storage", (event) => {
      if (event.key === "bankOfStyxColorTheme") {
        applyColorTheme();
      }
    });

    return () => {
      window.removeEventListener("storage", applyColorTheme);
    };
  }, []);

  // Only render on client side
  if (!mounted) return null;

  return null;
}
